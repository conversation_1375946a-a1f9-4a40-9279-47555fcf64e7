# JavaScript内容抓取解决方案

## 问题分析

### 当前问题
1. **左侧菜单动态加载**：腾讯千帆文档网站的左侧导航菜单大量使用JavaScript动态生成
2. **折叠菜单需要交互**：许多子菜单项隐藏在折叠状态，需要用户点击才能展开
3. **AJAX异步加载**：部分内容通过AJAX请求动态获取
4. **链接发现不完整**：当前只能发现20个链接，实际网站有200+页面

### 技术挑战
- 普通HTTP请求无法执行JavaScript
- 需要模拟用户交互行为
- 动态内容加载时机难以把握
- 不同页面的菜单结构可能不同

## 解决方案

### 方案1：增强Selenium自动化

#### 1.1 智能菜单展开策略
```python
class SmartMenuExpander:
    """智能菜单展开器"""
    
    def __init__(self, driver, logger):
        self.driver = driver
        self.logger = logger
        self.expanded_elements = set()  # 记录已展开的元素
    
    def expand_all_menus(self, max_rounds=10):
        """智能展开所有菜单"""
        
        total_expanded = 0
        
        for round_num in range(max_rounds):
            round_expanded = 0
            
            # 1. 查找所有可能的折叠元素
            collapsible_elements = self.find_collapsible_elements()
            
            # 2. 过滤已处理的元素
            new_elements = [elem for elem in collapsible_elements 
                          if self.get_element_id(elem) not in self.expanded_elements]
            
            if not new_elements:
                self.logger.info(f"第{round_num + 1}轮：没有发现新的折叠元素")
                break
            
            self.logger.info(f"第{round_num + 1}轮：发现 {len(new_elements)} 个新的折叠元素")
            
            # 3. 逐个展开元素
            for element in new_elements:
                if self.expand_element(element):
                    round_expanded += 1
                    total_expanded += 1
                    self.expanded_elements.add(self.get_element_id(element))
            
            # 4. 等待动态内容加载
            self.wait_for_content_update()
            
            self.logger.info(f"第{round_num + 1}轮：成功展开 {round_expanded} 个元素")
        
        self.logger.info(f"总共展开了 {total_expanded} 个菜单元素")
        return total_expanded
    
    def find_collapsible_elements(self):
        """查找所有可折叠元素"""
        selectors = [
            # 主要选择器
            '.menu__list-item--collapsed .menu__link--sublist-caret',
            '.menu__list-item--collapsed',
            '[aria-expanded="false"]',
            
            # 备用选择器
            '.collapsed .expand-button',
            '.collapsed .toggle-button',
            '.sidebar-item.collapsed',
            '.nav-item.collapsed',
            '.tree-item.collapsed',
            
            # 通用模式
            '[class*="collapsed"]',
            '[class*="closed"]',
            '[class*="folded"]',
            
            # 特定网站模式
            '.docusaurus-menu-item--collapsed',
            '.theme-doc-sidebar-item-category--collapsed'
        ]
        
        all_elements = []
        
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    if self.is_valid_collapsible_element(elem):
                        all_elements.append(elem)
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 查找失败: {e}")
        
        # 去重
        unique_elements = []
        seen_ids = set()
        
        for elem in all_elements:
            elem_id = self.get_element_id(elem)
            if elem_id not in seen_ids:
                unique_elements.append(elem)
                seen_ids.add(elem_id)
        
        return unique_elements
    
    def is_valid_collapsible_element(self, element):
        """验证元素是否为有效的可折叠元素"""
        try:
            # 检查元素是否可见
            if not element.is_displayed():
                return False
            
            # 检查元素大小
            size = element.size
            if size['width'] == 0 or size['height'] == 0:
                return False
            
            # 检查是否在视口内
            location = element.location
            if location['y'] < -1000 or location['y'] > 10000:
                return False
            
            # 检查是否已经展开
            aria_expanded = element.get_attribute('aria-expanded')
            if aria_expanded == 'true':
                return False
            
            # 检查类名
            class_names = element.get_attribute('class') or ''
            if 'collapsed' not in class_names.lower() and 'closed' not in class_names.lower():
                # 进一步检查父元素
                parent = element.find_element(By.XPATH, '..')
                parent_classes = parent.get_attribute('class') or ''
                if 'collapsed' not in parent_classes.lower():
                    return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"验证元素失败: {e}")
            return False
    
    def expand_element(self, element):
        """展开单个元素"""
        try:
            # 滚动到元素可见
            self.scroll_to_element(element)
            
            # 尝试多种点击方式
            click_methods = [
                lambda: element.click(),
                lambda: self.driver.execute_script("arguments[0].click();", element),
                lambda: ActionChains(self.driver).click(element).perform(),
                lambda: self.click_with_js_event(element)
            ]
            
            for i, click_method in enumerate(click_methods):
                try:
                    click_method()
                    
                    # 等待展开动画
                    time.sleep(1)
                    
                    # 验证是否成功展开
                    if self.verify_expansion(element):
                        self.logger.debug(f"使用方法{i+1}成功展开元素")
                        return True
                    
                except Exception as e:
                    self.logger.debug(f"点击方法{i+1}失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.debug(f"展开元素失败: {e}")
            return False
    
    def scroll_to_element(self, element):
        """滚动到元素可见"""
        try:
            # 滚动到元素中心
            self.driver.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            """, element)
            
            time.sleep(0.5)
            
        except Exception as e:
            self.logger.debug(f"滚动到元素失败: {e}")
    
    def click_with_js_event(self, element):
        """使用JavaScript事件点击"""
        self.driver.execute_script("""
            var element = arguments[0];
            var event = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        """, element)
    
    def verify_expansion(self, element):
        """验证元素是否已展开"""
        try:
            # 检查aria-expanded属性
            aria_expanded = element.get_attribute('aria-expanded')
            if aria_expanded == 'true':
                return True
            
            # 检查类名变化
            class_names = element.get_attribute('class') or ''
            if 'expanded' in class_names.lower() or 'open' in class_names.lower():
                return True
            
            # 检查父元素类名变化
            parent = element.find_element(By.XPATH, '..')
            parent_classes = parent.get_attribute('class') or ''
            if 'collapsed' not in parent_classes.lower():
                return True
            
            return False
            
        except Exception:
            return False
    
    def wait_for_content_update(self):
        """等待内容更新"""
        try:
            # 等待JavaScript执行完成
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 等待AJAX请求完成
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script(
                    "return (typeof jQuery !== 'undefined') ? jQuery.active === 0 : true"
                )
            )
            
            # 额外等待时间
            time.sleep(2)
            
        except TimeoutException:
            self.logger.warning("等待内容更新超时")
    
    def get_element_id(self, element):
        """获取元素唯一标识"""
        try:
            # 尝试获取ID
            elem_id = element.get_attribute('id')
            if elem_id:
                return f"id:{elem_id}"
            
            # 使用XPath作为标识
            xpath = self.driver.execute_script("""
                function getXPath(element) {
                    if (element.id !== '') {
                        return 'id("' + element.id + '")';
                    }
                    if (element === document.body) {
                        return element.tagName;
                    }
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }
                return getXPath(arguments[0]);
            """, element)
            
            return f"xpath:{xpath}"
            
        except Exception:
            return f"hash:{hash(str(element))}"
```

#### 1.2 多页面链接发现策略
```python
class ComprehensiveLinkDiscoverer:
    """综合链接发现器"""
    
    def __init__(self, crawler):
        self.crawler = crawler
        self.driver = crawler.driver
        self.logger = crawler.logger
        self.discovered_links = set()
    
    def discover_all_links(self):
        """发现所有链接的综合策略"""
        
        # 策略1：主页面完整展开
        main_links = self.discover_from_main_page()
        self.discovered_links.update(main_links)
        
        # 策略2：多个样本页面
        sample_links = self.discover_from_sample_pages()
        self.discovered_links.update(sample_links)
        
        # 策略3：API接口探测
        api_links = self.discover_from_api()
        self.discovered_links.update(api_links)
        
        # 策略4：站点地图
        sitemap_links = self.discover_from_sitemap()
        self.discovered_links.update(sitemap_links)
        
        # 策略5：深度递归
        recursive_links = self.discover_recursive()
        self.discovered_links.update(recursive_links)
        
        self.logger.info(f"综合策略总共发现 {len(self.discovered_links)} 个链接")
        return list(self.discovered_links)
    
    def discover_from_main_page(self):
        """从主页面发现链接"""
        self.logger.info("策略1：从主页面发现链接")
        
        try:
            # 访问主页面
            self.driver.get(self.crawler.start_url)
            self.wait_for_page_load()
            
            # 智能展开所有菜单
            expander = SmartMenuExpander(self.driver, self.logger)
            expanded_count = expander.expand_all_menus()
            
            # 提取所有链接
            links = self.extract_all_links()
            
            self.logger.info(f"主页面展开 {expanded_count} 个菜单，发现 {len(links)} 个链接")
            return links
            
        except Exception as e:
            self.logger.error(f"主页面链接发现失败: {e}")
            return set()
    
    def discover_from_sample_pages(self):
        """从样本页面发现链接"""
        self.logger.info("策略2：从样本页面发现链接")
        
        # 选择样本页面
        sample_urls = [
            'https://qian.tencent.com/document/53799',  # 起始页
            'https://qian.tencent.com/document/53794',  # 产品概述
            'https://qian.tencent.com/document/53795',  # 快速入门
        ]
        
        all_sample_links = set()
        
        for url in sample_urls:
            try:
                self.logger.info(f"处理样本页面: {url}")
                
                self.driver.get(url)
                self.wait_for_page_load()
                
                # 展开菜单
                expander = SmartMenuExpander(self.driver, self.logger)
                expander.expand_all_menus(max_rounds=5)
                
                # 提取链接
                links = self.extract_all_links()
                all_sample_links.update(links)
                
                self.logger.info(f"样本页面 {url} 发现 {len(links)} 个链接")
                
            except Exception as e:
                self.logger.error(f"样本页面 {url} 处理失败: {e}")
        
        self.logger.info(f"样本页面策略总共发现 {len(all_sample_links)} 个链接")
        return all_sample_links
    
    def extract_all_links(self):
        """提取页面中的所有有效链接"""
        links = set()
        
        try:
            # 等待页面稳定
            time.sleep(3)
            
            # 多种选择器策略
            link_selectors = [
                'a[href*="/document/"]',  # 文档链接
                '.sidebar a[href]',       # 侧边栏链接
                '.menu a[href]',          # 菜单链接
                'nav a[href]',            # 导航链接
                '.toc a[href]',           # 目录链接
            ]
            
            for selector in link_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and self.is_valid_document_link(href):
                            links.add(href)
                            
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
            
            # 额外的JavaScript提取
            js_links = self.extract_links_with_javascript()
            links.update(js_links)
            
        except Exception as e:
            self.logger.error(f"提取链接失败: {e}")
        
        return links
    
    def extract_links_with_javascript(self):
        """使用JavaScript提取链接"""
        try:
            js_code = """
            var links = new Set();
            
            // 查找所有包含href的元素
            var elements = document.querySelectorAll('a[href]');
            elements.forEach(function(elem) {
                var href = elem.href;
                if (href && href.includes('/document/')) {
                    links.add(href);
                }
            });
            
            // 查找可能的数据属性
            var dataElements = document.querySelectorAll('[data-href], [data-url], [data-link]');
            dataElements.forEach(function(elem) {
                var href = elem.getAttribute('data-href') || 
                          elem.getAttribute('data-url') || 
                          elem.getAttribute('data-link');
                if (href && href.includes('/document/')) {
                    links.add(href);
                }
            });
            
            return Array.from(links);
            """
            
            js_links = self.driver.execute_script(js_code)
            return set(js_links) if js_links else set()
            
        except Exception as e:
            self.logger.debug(f"JavaScript提取链接失败: {e}")
            return set()
    
    def is_valid_document_link(self, url):
        """验证是否为有效的文档链接"""
        try:
            if not url:
                return False
            
            # 基本URL验证
            if not url.startswith('http'):
                return False
            
            # 域名验证
            if 'qian.tencent.com' not in url:
                return False
            
            # 路径验证
            if '/document/' not in url:
                return False
            
            # 排除特殊链接
            excluded_patterns = [
                '#', 'javascript:', 'mailto:', 'tel:',
                '.pdf', '.doc', '.zip', '.rar'
            ]
            
            for pattern in excluded_patterns:
                if pattern in url.lower():
                    return False
            
            return True
            
        except Exception:
            return False
    
    def wait_for_page_load(self, timeout=30):
        """等待页面完全加载"""
        try:
            # 等待基础DOM
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待文档就绪
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 等待侧边栏
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".sidebar, .menu, nav"))
                )
            except TimeoutException:
                self.logger.warning("侧边栏加载超时")
            
            # 额外等待时间
            time.sleep(2)
            
        except TimeoutException:
            self.logger.warning("页面加载超时")
```

### 方案2：API接口探测

#### 2.1 自动API发现
```python
class APIDiscoverer:
    """API接口发现器"""
    
    def __init__(self, base_url, session, logger):
        self.base_url = base_url
        self.session = session
        self.logger = logger
    
    def discover_api_endpoints(self):
        """发现可能的API端点"""
        
        potential_endpoints = [
            # 常见API模式
            '/api/docs',
            '/api/documents',
            '/api/navigation',
            '/api/menu',
            '/api/sitemap',
            '/api/list',
            
            # 特定网站模式
            '/document/api/list',
            '/document/api/tree',
            '/document/api/navigation',
            
            # GraphQL端点
            '/graphql',
            '/api/graphql',
            
            # 其他可能的端点
            '/_next/static/chunks/pages',
            '/static/js',
            '/.well-known'
        ]
        
        valid_endpoints = []
        
        for endpoint in potential_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    
                    if 'application/json' in content_type:
                        self.logger.info(f"发现JSON API端点: {endpoint}")
                        valid_endpoints.append((endpoint, 'json'))
                    elif 'text/xml' in content_type or 'application/xml' in content_type:
                        self.logger.info(f"发现XML端点: {endpoint}")
                        valid_endpoints.append((endpoint, 'xml'))
                    
            except Exception as e:
                self.logger.debug(f"API端点 {endpoint} 测试失败: {e}")
        
        return valid_endpoints
    
    def extract_links_from_api(self, endpoint, content_type):
        """从API响应中提取链接"""
        try:
            url = urljoin(self.base_url, endpoint)
            response = self.session.get(url, timeout=10)
            
            if content_type == 'json':
                return self.extract_from_json(response.json())
            elif content_type == 'xml':
                return self.extract_from_xml(response.text)
            
        except Exception as e:
            self.logger.error(f"从API {endpoint} 提取链接失败: {e}")
        
        return set()
    
    def extract_from_json(self, data):
        """从JSON数据中提取链接"""
        links = set()
        
        def recursive_extract(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in ['url', 'href', 'link', 'path'] and isinstance(value, str):
                        if '/document/' in value:
                            links.add(value)
                    else:
                        recursive_extract(value)
            elif isinstance(obj, list):
                for item in obj:
                    recursive_extract(item)
        
        recursive_extract(data)
        return links
```

### 方案3：混合策略实现

#### 3.1 完整的混合爬虫
```python
class HybridTencentQianCrawler(BaseCrawler):
    """混合策略爬虫"""
    
    def __init__(self, use_selenium=True, use_api=True):
        super().__init__()
        self.use_selenium = use_selenium
        self.use_api = use_api
        
        if self.use_selenium:
            self.setup_selenium()
    
    def discover_all_links_hybrid(self):
        """混合策略发现所有链接"""
        
        all_links = set()
        
        # 策略1：Selenium完整发现
        if self.use_selenium:
            selenium_links = self.discover_with_selenium()
            all_links.update(selenium_links)
            self.logger.info(f"Selenium策略发现 {len(selenium_links)} 个链接")
        
        # 策略2：API接口发现
        if self.use_api:
            api_links = self.discover_with_api()
            new_api_links = api_links - all_links
            all_links.update(api_links)
            self.logger.info(f"API策略新增 {len(new_api_links)} 个链接")
        
        # 策略3：站点地图发现
        sitemap_links = self.discover_with_sitemap()
        new_sitemap_links = sitemap_links - all_links
        all_links.update(sitemap_links)
        self.logger.info(f"站点地图策略新增 {len(new_sitemap_links)} 个链接")
        
        # 策略4：递归发现
        recursive_links = self.discover_recursive(list(all_links)[:20])
        new_recursive_links = recursive_links - all_links
        all_links.update(recursive_links)
        self.logger.info(f"递归策略新增 {len(new_recursive_links)} 个链接")
        
        # 更新URL管理器
        added_count = self.url_manager.add_urls_batch(list(all_links), self.start_url)
        self.logger.info(f"混合策略总共发现 {len(all_links)} 个链接，新增 {added_count} 个")
        
        return list(all_links)
    
    def discover_with_selenium(self):
        """使用Selenium发现链接"""
        if not self.driver:
            return set()
        
        discoverer = ComprehensiveLinkDiscoverer(self)
        return set(discoverer.discover_all_links())
    
    def run_hybrid(self, max_pages=None):
        """运行混合策略爬虫"""
        
        try:
            # 阶段1：链接发现
            self.logger.info("开始混合策略链接发现")
            all_links = self.discover_all_links_hybrid()
            
            # 阶段2：内容抓取
            self.logger.info("开始内容抓取")
            self.crawl_content_optimized(all_links, max_pages)
            
        finally:
            if self.driver:
                self.driver.quit()
```

## 实施建议

### 优先级1：立即实施
1. **修复函数命名问题**：解决当前13个URL保存失败的问题
2. **增强菜单展开策略**：实现SmartMenuExpander类

### 优先级2：短期实施（1-2周）
1. **实现综合链接发现**：ComprehensiveLinkDiscoverer类
2. **添加API探测功能**：APIDiscoverer类
3. **优化等待策略**：改善页面加载等待机制

### 优先级3：中期实施（2-4周）
1. **完整混合策略**：HybridTencentQianCrawler类
2. **性能优化**：分阶段并发处理
3. **错误处理增强**：提高系统稳定性

## 预期效果

### 链接发现能力
- **当前**：20个链接
- **目标**：200+个链接
- **提升**：10倍以上

### 成功率提升
- **当前**：38%成功率
- **目标**：90%以上成功率
- **改善**：减少JavaScript相关失败

### 系统稳定性
- 更好的错误处理
- 智能重试机制
- 优雅的降级策略

这个解决方案将显著提升项目对JavaScript动态内容的处理能力，解决当前链接发现不完整的核心问题。