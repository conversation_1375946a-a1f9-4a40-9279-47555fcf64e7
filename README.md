# 腾讯千帆（电子签）文档爬虫

自动抓取腾讯千帆文档中心的所有页面，并转换为Markdown格式本地保存。

## 功能特点

- 🕷️ **智能爬取**: 自动发现和抓取所有相关文档页面
- 📝 **Markdown转换**: 将HTML内容转换为清晰的Markdown格式
- 🏷️ **智能命名**: 基于内容自动生成中文文件名
- 🔄 **断点续传**: 支持中断后继续爬取
- 🛡️ **反爬策略**: 内置请求频率控制和User-Agent轮换
- 📊 **进度监控**: 实时显示爬取进度和统计信息
- 🧵 **并发支持**: 多线程并发提升效率

## 项目结构

```
py_qian/
├── main.py              # 主启动脚本
├── crawler.py           # 核心爬虫类
├── config.py            # 配置文件
├── utils.py             # 工具函数
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
├── download/           # 下载文件存储目录
│   ├── progress.json   # 爬取进度文件
│   ├── crawler.log     # 运行日志
│   └── *.md           # 下载的Markdown文件
├── 项目实现计划.md      # 项目计划文档
└── 技术设计文档.md      # 技术设计文档
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行爬虫

```bash
# 使用默认设置运行
python main.py

# 限制抓取页面数量
python main.py -p 50

# 设置并发线程数
python main.py -w 5

# 从头开始（重置进度）
python main.py --reset
```

### 3. 查看结果

爬取完成后，所有文档将保存在 `download/` 目录下，每个文档都是一个独立的Markdown文件。

## 配置说明

### 基础配置

- **目标网站**: https://qian.tencent.com/document
- **入口页面**: https://qian.tencent.com/document/53799
- **输出目录**: ./download
- **文件格式**: Markdown (.md)

### 爬取策略

- **请求间隔**: 1-3秒随机间隔
- **重试次数**: 失败后最多重试3次
- **并发数量**: 默认3个线程（可调节）
- **超时设置**: 30秒请求超时

### 内容处理

- **标题提取**: 自动识别页面主标题
- **内容清理**: 移除导航、广告等无关元素
- **链接保留**: 保持原有链接结构
- **图片处理**: 保留图片链接和alt文本

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-p, --pages` | 最大页面数量 | 无限制 |
| `-w, --workers` | 并发线程数 | 3 |
| `--reset` | 重置进度，从头开始 | - |
| `--output-dir` | 输出目录 | ./download |

## 文件命名规则

1. **优先使用页面标题**：如果页面有明确标题，直接使用
2. **关键词提取**：从内容中提取关键词组合
3. **URL解析**：从URL路径中提取有意义的部分  
4. **哈希后缀**：最后使用URL哈希值确保唯一性

示例文件名：
- `产品概述.md`
- `个人版操作指南.md`
- `API文档_接口说明.md`

## 输出格式

每个Markdown文件包含：

```markdown
# 页面标题

> 来源: https://qian.tencent.com/document/xxxxx
> 抓取时间: 2024-01-01 12:00:00

---

页面内容（Markdown格式）
```

## 技术架构

### 核心模块

- **WebCrawler**: 主爬虫类，负责页面抓取
- **ContentExtractor**: 内容提取器，解析HTML
- **MarkdownConverter**: Markdown转换器
- **URLManager**: URL队列管理
- **FileManager**: 文件管理和命名

### 依赖包

- `requests`: HTTP请求库
- `beautifulsoup4`: HTML解析库
- `html2text`: HTML转Markdown
- `jieba`: 中文分词（用于文件命名）
- `tqdm`: 进度条显示
- `lxml`: 高性能XML/HTML解析器

## 注意事项

### 使用建议

1. **合理设置并发数**：建议不超过5个线程，避免对服务器造成压力
2. **网络环境**：确保网络连接稳定，支持访问目标网站
3. **存储空间**：预估需要几百MB存储空间
4. **运行时间**：完整爬取可能需要1-2小时，可以随时中断后继续

### 法律声明

- 本工具仅用于学习和研究目的
- 请遵守目标网站的robots.txt协议
- 请勿用于商业用途或大规模数据采集
- 使用前请确认符合相关法律法规

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用代理

2. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

3. **中文编码问题**
   - 确保系统支持UTF-8编码
   - Windows用户可能需要设置环境变量

4. **文件权限问题**
   - 确保对输出目录有写权限
   - 必要时使用管理员权限运行

### 日志查看

详细的运行日志保存在 `download/crawler.log`，可以查看具体的错误信息。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础爬取功能
- 智能文件命名
- 断点续传功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License