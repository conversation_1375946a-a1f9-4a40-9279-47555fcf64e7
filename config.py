"""
配置文件 - 腾讯千帆文档爬虫
"""

import os

# 基础配置
BASE_URL = 'https://qian.tencent.com/document'
START_URL = 'https://qian.tencent.com/document/53799'
OUTPUT_DIR = './download'
PAGE_OUTPUT_DIR = './download/page'

# 请求配置
REQUEST_CONFIG = {
    'timeout': 30,
    'retry_times': 3,
    'delay_min': 0.2,  # 最小请求间隔（秒）200ms
    'delay_max': 2.0,  # 最大请求间隔（秒）2000ms
    'user_agents': [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ],
    'headers': {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }
}

# 内容提取配置
CONTENT_CONFIG = {
    'main_content_selectors': [
        'main',
        '.main-content', 
        'article',
        '.document-content',
        '.content',
        '[role="main"]',
        '#main-content'
    ],
    'title_selectors': [
        'h1',
        '.page-title', 
        '.document-title',
        'title',
        '.title'
    ],
    'remove_selectors': [
        'nav',
        '.navigation',
        '.nav',
        '.sidebar',
        '.side-nav',
        'footer',
        '.footer',
        '.ads',
        '.advertisement', 
        'script',
        'style',
        'noscript',
        '.comments',
        '.comment',
        '.breadcrumb',
        '.pagination',
        '.next-prev',
        '.social-share',
        '.print-button',
        '.edit-button',
        '.feedback',
        'iframe',
        '.video-player'
    ],
    'keep_selectors': [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'div', 'span',
        'ul', 'ol', 'li',
        'table', 'tr', 'td', 'th',
        'blockquote',
        'pre', 'code',
        'strong', 'b', 'em', 'i',
        'a', 'img'
    ]
}

# 文件管理配置
FILE_CONFIG = {
    'max_filename_length': 50,
    'forbidden_chars': ['<', '>', ':', '"', '|', '?', '*', '/', '\\'],
    'replacement_char': '_',
    'encoding': 'utf-8',
    'markdown_extension': '.md'
}

# Markdown转换配置
MARKDOWN_CONFIG = {
    'ignore_links': False,
    'ignore_images': False,
    'body_width': 0,  # 0表示不限制宽度
    'protect_links': True,
    'unicode_snob': True,
    'escape_snob': False,
    'default_image_alt': '图片'
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'crawler.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# URL过滤配置
URL_FILTER_CONFIG = {
    'allowed_domains': ['qian.tencent.com'],
    'allowed_paths': ['/document'],
    'excluded_extensions': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar'],
    'excluded_patterns': [
        r'/api/',
        r'/login',
        r'/logout',
        r'/register',
        r'/search',
        r'#',
        r'javascript:',
        r'mailto:',
        r'tel:'
    ]
}

# 进度管理配置
PROGRESS_CONFIG = {
    'save_interval': 10,  # 每处理10个页面保存一次进度
    'progress_file': 'progress.json',
    'max_concurrent': 5   # 最大并发数
}

def ensure_output_dir():
    """确保输出目录存在"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"创建输出目录: {OUTPUT_DIR}")
    
    if not os.path.exists(PAGE_OUTPUT_DIR):
        os.makedirs(PAGE_OUTPUT_DIR)
        print(f"创建页面输出目录: {PAGE_OUTPUT_DIR}")

def get_full_output_path(filename):
    """获取完整的输出文件路径"""
    return os.path.join(OUTPUT_DIR, filename)

# 初始化
ensure_output_dir()