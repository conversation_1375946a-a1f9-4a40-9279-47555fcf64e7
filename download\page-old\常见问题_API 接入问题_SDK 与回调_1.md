# SDK 与回调

> 导航路径: 常见问题 > API 接入问题 > SDK 与回调
> 来源: https://qian.tencent.com/document/116840
> 抓取时间: 2025-06-15 16:34:11

---

### 报错“签名计算错误，请使用 SDK”

1. 请确保使用了电子签提供的 SDK。

2. 请确保 `SecretId` 和 `SecretKey` 正确。

### 报错“The SecretId is not found, please ensure that your SecretId is correct”

常见原因是，指定的 `SecretId` 和 `SecretKey` 传入了正式/测试环境的密钥，而接口请求到了测试/正式环境。

### 为什么 API 文档中的接口在 SDK 中找不到？

首先，需要确认引入的 SDK 是否正确。以 `JAVA` 为例：

自建应用请 [参考 Demo](https://github.com/tencentess/ess-java-kit)﻿

第三方应用请 [参考 Demo](https://github.com/tencentess/essbasic-java-kit)﻿

其次，由于电子签功能也在不断迭代，可能新的 API 在更高版本的 SDK 中，尝试将 SDK 升级到 [最新版本](https://central.sonatype.com/artifact/com.tencentcloudapi/tencentcloud-sdk-java "https://central.sonatype.com/artifact/com.tencentcloudapi/tencentcloud-sdk-java")。

### 报错“请求的 Endpoint 或 Version 错误，请检查并修正后重试”

强烈建议用 SDK 请求接口。如果是用电子签提供的 Demo 进行调试，请确认下载的 Demo 是否正确，以 java 语言为例：

自建应用请 [参考 Demo](https://github.com/tencentess/ess-java-kit)﻿

第三方应用请 [参考 Demo](https://github.com/tencentess/essbasic-java-kit)﻿

### UploadFiles 报错：业务错误，身份鉴权失败

常见原因是，指定的 `SecretId` 和 `SecretKey` 传入了正式/测试环境的密钥，而接口请求到了测试/正式环境。

### 为什么收不到回调消息？

如果配置了回调 Url 地址，但是没有收到回调通知，您可在**外网环境** 用下方 curl 命令，需确保 `HttpCode` 返回200：

    # 需将 https://tsign.tencent.com/callback 换成您配置的回调地址
    
    curl  https://tsign.tencent.com/callback -H "Content-type: application/json" -X POST -d "{}"  

### 业务上线前需要注意什么？

联调完成上线时需要做以下工作：

1. 更换 API 密钥；

2. 配置新的回调地址；

3. 更改请求域名

详细操作请参考：

[自建应用上线环境配置](https://qian.tencent.com/developers/company/online_env_integration "https://qian.tencent.com/developers/company/online_env_integration")

[第三方应用上线环境配置](https://qian.tencent.com/developers/partner/online_env_integration "https://qian.tencent.com/developers/partner/online_env_integration")

### 集团架构下如何给不同子企业设置不同的回调地址？

目前包括子企业在内，「专业版」以下版本不支持配置回调地址，但子企业的所有消息都会发到主企业配置的回调地址，建议业务方自行分发。

### 回调信息解密后是乱码

参考 [解密示例](https://github.com/tencentess/ess-java-kit/blob/main/src/main/java/com/tencent/ess/callback/CallbackAes.java "https://github.com/tencentess/ess-java-kit/blob/main/src/main/java/com/tencent/ess/callback/CallbackAes.java")，确认传入的 Key 正确。

**注意：**

请确保正式环境和测试环境用了不同的回调地址。

### 报错“操作禁止”

常见错误是调用其他通用请求时用了文件上传接口的请求地址，请检查：

应用类型| 环境| 服务| 地址  
---|---|---|---  
自建/第三方| 联调环境| 文件服务（UploadFiles）| file.test.ess.tencent.cn  
自建/第三方| 线上环境| 文件服务（UploadFiles）| file.ess.tencent.cn  
自建应用| 联调环境| 其他通用服务| ess.test.ess.tencent.cn  
自建应用| 线上环境| 其他通用服务| ess.tencentcloudapi.com  
第三方应用| 联调环境| 其他通用服务| essbasic.test.ess.tencent.cn  
第三方应用| 线上环境| 其他通用服务| essbasic.tencentcloudapi.com  
  
### 报错“action not exist”

常见错误是调用 `UploadFiles` 接口时使用了通用服务请求地址，请检查：

应用类型| 环境| 服务| 地址  
---|---|---|---  
自建/第三方| 联调环境| 文件服务（UploadFiles）| file.test.ess.tencent.cn  
自建/第三方| 线上环境| 文件服务（UploadFiles）| file.ess.tencent.cn  
自建应用| 联调环境| 其他通用服务| ess.test.ess.tencent.cn  
自建应用| 线上环境| 其他通用服务| ess.tencentcloudapi.com  
第三方应用| 联调环境| 其他通用服务| essbasic.test.ess.tencent.cn  
第三方应用| 线上环境| 其他通用服务| essbasic.tencentcloudapi.com