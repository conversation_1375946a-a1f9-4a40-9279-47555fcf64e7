#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
升级后爬虫测试脚本
测试URL管理器和编码处理功能
"""

import os
import sys
import time
from crawler import TencentQianCrawler
from url_manager import URLManager


def test_url_manager():
    """测试URL管理器功能"""
    print("=" * 50)
    print("测试URL管理器功能")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = "test_output"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 创建URL管理器
    url_manager = URLManager(test_dir, "https://qian.tencent.com")
    
    # 测试添加URL
    test_urls = [
        "https://qian.tencent.com/document/53799",
        "https://qian.tencent.com/document/53800",
        "https://qian.tencent.com/document/53801",
        "https://example.com/invalid",  # 无效URL，应该被过滤
    ]
    
    print("添加测试URL...")
    for url in test_urls:
        result = url_manager.add_url(url, "test_source")
        print(f"添加 {url}: {'成功' if result else '失败/重复'}")
    
    # 显示统计信息
    stats = url_manager.get_statistics()
    print(f"\n统计信息:")
    print(f"总URL数: {stats['total_urls']}")
    print(f"待处理: {stats['pending_urls']}")
    
    # 测试标记完成
    pending_urls = url_manager.get_pending_urls()
    if pending_urls:
        test_url = pending_urls[0]
        url_manager.mark_completed(test_url)
        print(f"\n标记完成: {test_url}")
    
    # 测试标记失败
    if len(pending_urls) > 1:
        test_url = pending_urls[1]
        url_manager.mark_failed(test_url, "测试错误")
        print(f"标记失败: {test_url}")
    
    # 保存数据
    url_manager.save_urls()
    print("\nURL数据已保存")
    
    # 显示最终统计
    final_stats = url_manager.get_statistics()
    print(f"\n最终统计:")
    print(f"总URL数: {final_stats['total_urls']}")
    print(f"已完成: {final_stats['completed_urls']}")
    print(f"失败: {final_stats['failed_urls']}")
    print(f"待处理: {final_stats['pending_urls']}")
    print(f"完成率: {final_stats['completion_rate']:.2f}%")
    
    # 生成报告
    url_manager.export_report()
    print("详细报告已生成")
    
    return url_manager


def test_encoding():
    """测试编码处理"""
    print("\n" + "=" * 50)
    print("测试编码处理功能")
    print("=" * 50)
    
    # 测试中文字符处理
    test_strings = [
        "腾讯千帆文档",
        "产品概述与介绍",
        "API接口说明",
        "混合字符串 Mixed Content 测试",
        "特殊符号：！@#￥%……&*（）",
    ]
    
    print("测试UTF-8编码处理:")
    for test_str in test_strings:
        # 测试编码和解码
        encoded = test_str.encode('utf-8')
        decoded = encoded.decode('utf-8')
        print(f"原始: {test_str}")
        print(f"编码后长度: {len(encoded)} 字节")
        print(f"解码后: {decoded}")
        print(f"编码一致性: {'✅' if test_str == decoded else '❌'}")
        print("-" * 30)


def test_crawler_integration():
    """测试爬虫集成功能"""
    print("\n" + "=" * 50)
    print("测试升级后爬虫集成功能")
    print("=" * 50)
    
    try:
        # 创建爬虫实例
        crawler = TencentQianCrawler()
        
        print("爬虫初始化成功")
        print(f"基础URL: {crawler.base_url}")
        print(f"起始URL: {crawler.start_url}")
        print(f"输出目录: {crawler.output_dir}")
        
        # 检查URL管理器
        url_stats = crawler.url_manager.get_statistics()
        print(f"\nURL管理器状态:")
        print(f"总URL数: {url_stats['total_urls']}")
        print(f"待处理: {url_stats['pending_urls']}")
        
        # 测试少量页面抓取
        print("\n开始测试抓取（限制2个页面）...")
        crawler.run(max_pages=2, max_workers=1)
        
        # 显示最终结果
        final_stats = crawler.url_manager.get_statistics()
        print(f"\n抓取完成统计:")
        print(f"发现URL: {final_stats['total_urls']}")
        print(f"已完成: {final_stats['completed_urls']}")
        print(f"失败: {final_stats['failed_urls']}")
        print(f"待处理: {final_stats['pending_urls']}")
        
        return True
        
    except Exception as e:
        print(f"爬虫测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def check_files():
    """检查生成的文件"""
    print("\n" + "=" * 50)
    print("检查生成的文件")
    print("=" * 50)
    
    # 检查下载目录
    download_dir = "download"
    if os.path.exists(download_dir):
        files = os.listdir(download_dir)
        print(f"下载目录文件数: {len(files)}")
        
        # 显示前几个文件
        for i, filename in enumerate(files[:5]):
            filepath = os.path.join(download_dir, filename)
            if os.path.isfile(filepath):
                try:
                    # 检查文件编码
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read(100)  # 读取前100个字符
                    print(f"{i+1}. {filename} (UTF-8编码正常)")
                    print(f"   内容预览: {content[:50]}...")
                except UnicodeDecodeError:
                    print(f"{i+1}. {filename} (编码错误)")
                except Exception as e:
                    print(f"{i+1}. {filename} (读取错误: {e})")
    else:
        print("下载目录不存在")
    
    # 检查URL管理文件
    url_files = [
        "all_urls.txt",
        "completed_urls.txt", 
        "failed_urls.txt",
        "url_metadata.json",
        "crawl_report.md"
    ]
    
    print(f"\nURL管理文件:")
    for filename in url_files:
        filepath = os.path.join(download_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"✅ {filename} ({size} 字节)")
        else:
            print(f"❌ {filename} (不存在)")


def main():
    """主测试函数"""
    print("腾讯千帆爬虫升级版测试")
    print("测试时间:", time.strftime('%Y-%m-%d %H:%M:%S'))
    
    try:
        # 1. 测试URL管理器
        url_manager = test_url_manager()
        
        # 2. 测试编码处理
        test_encoding()
        
        # 3. 测试爬虫集成
        success = test_crawler_integration()
        
        # 4. 检查生成的文件
        check_files()
        
        # 总结
        print("\n" + "=" * 50)
        print("测试总结")
        print("=" * 50)
        if success:
            print("✅ 所有测试通过！")
            print("升级后的爬虫功能正常，包括：")
            print("- URL管理和跟踪")
            print("- UTF-8编码处理")
            print("- 断点续传功能")
            print("- 详细报告生成")
        else:
            print("❌ 部分测试失败，请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 