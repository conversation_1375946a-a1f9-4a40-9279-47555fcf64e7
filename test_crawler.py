"""
爬虫功能测试脚本
"""

import sys
import os
from crawler import TencentQianCrawler
from utils import *

def test_single_page():
    """测试单页面爬取"""
    print("=" * 50)
    print("测试单页面爬取功能")
    print("=" * 50)
    
    crawler = TencentQianCrawler()
    test_url = "https://qian.tencent.com/document/53799"
    
    print(f"测试URL: {test_url}")
    
    # 测试页面获取
    success, content = crawler.fetch_page(test_url)
    
    if success:
        print("✅ 页面获取成功")
        print(f"内容长度: {len(content)} 字符")
        
        # 测试内容提取
        content_info = crawler.extract_content(content, test_url)
        
        print(f"页面标题: {content_info['title']}")
        print(f"发现链接数: {len(content_info['links'])}")
        
        # 测试文件保存
        save_success = crawler.save_content(content_info)
        
        if save_success:
            print("✅ 文件保存成功")
        else:
            print("❌ 文件保存失败")
            
    else:
        print(f"❌ 页面获取失败: {content}")
        
    print()

def test_url_extraction():
    """测试URL提取功能"""
    print("=" * 50)
    print("测试URL提取功能")
    print("=" * 50)
    
    test_html = """
    <html>
        <body>
            <a href="/document/53800">相对链接1</a>
            <a href="https://qian.tencent.com/document/53801">绝对链接1</a>
            <a href="https://other.com/page">外部链接</a>
            <a href="/api/data">API链接</a>
            <a href="javascript:void(0)">JavaScript链接</a>
        </body>
    </html>
    """
    
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(test_html, 'lxml')
    
    crawler = TencentQianCrawler()
    links = crawler.extract_links(soup, "https://qian.tencent.com/document/53799")
    
    print("提取的有效链接:")
    for link in links:
        print(f"  - {link}")
        
    print(f"总计: {len(links)} 个有效链接")
    print()

def test_filename_generation():
    """测试文件名生成功能"""
    print("=" * 50)
    print("测试文件名生成功能")
    print("=" * 50)
    
    test_cases = [
        {
            'title': '腾讯电子签产品概述',
            'content': '腾讯电子签是一款为企业及个人提供安全、便捷的电子合同签约服务...',
            'url': 'https://qian.tencent.com/document/53799'
        },
        {
            'title': 'API 接口文档 - 签署流程管理',
            'content': '本文档介绍如何通过API接口管理电子签名的签署流程...',
            'url': 'https://qian.tencent.com/document/api/sign'
        },
        {
            'title': '',  # 空标题
            'content': '用户操作指南内容，包含详细的步骤说明和注意事项...',
            'url': 'https://qian.tencent.com/document/guide/user'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        filename = generate_filename_from_content(
            case['title'],
            case['content'],
            case['url']
        )
        print(f"测试用例 {i}:")
        print(f"  标题: {case['title'] or '(空)'}")
        print(f"  生成文件名: {filename}")
        print()

def test_markdown_conversion():
    """测试Markdown转换功能"""
    print("=" * 50)
    print("测试Markdown转换功能")
    print("=" * 50)
    
    test_html = """
    <div>
        <h1>产品概述</h1>
        <p>腾讯电子签是一款<strong>安全便捷</strong>的电子合同签约服务。</p>
        <h2>主要功能</h2>
        <ul>
            <li>电子合同签署</li>
            <li>身份认证</li>
            <li>证据保存</li>
        </ul>
        <blockquote>
            <p>数字化转型的必备工具</p>
        </blockquote>
    </div>
    """
    
    crawler = TencentQianCrawler()
    markdown = crawler.convert_to_markdown(test_html)
    
    print("转换后的Markdown:")
    print("-" * 30)
    print(markdown)
    print("-" * 30)
    print()

def test_progress_management():
    """测试进度管理功能"""
    print("=" * 50)
    print("测试进度管理功能")
    print("=" * 50)
    
    crawler = TencentQianCrawler()
    
    # 添加一些测试URL
    test_urls = [
        'https://qian.tencent.com/document/53799',
        'https://qian.tencent.com/document/53800',
        'https://qian.tencent.com/document/53801'
    ]
    
    crawler.add_urls_to_queue(test_urls)
    
    print(f"待爬取队列: {len(crawler.to_crawl)} 个URL")
    print(f"已爬取队列: {len(crawler.crawled)} 个URL")
    
    # 测试进度保存和加载
    crawler.save_progress()
    print("✅ 进度保存成功")
    
    # 创建新实例测试加载
    new_crawler = TencentQianCrawler()
    print(f"加载后待爬取队列: {len(new_crawler.to_crawl)} 个URL")
    print()

def main():
    """主测试函数"""
    print("腾讯千帆文档爬虫 - 功能测试")
    print("=" * 50)
    
    # 检查必要的依赖
    try:
        import requests
        import bs4
        import html2text
        import jieba
        print("✅ 所有依赖包已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        return
    
    print()
    
    # 运行各项测试
    try:
        test_filename_generation()
        test_markdown_conversion()
        test_url_extraction()
        test_progress_management()
        
        # 最后测试单页面爬取（需要网络连接）
        print("是否测试网络连接和页面爬取? (y/N): ", end="")
        test_network = input().strip().lower()
        
        if test_network in ['y', 'yes', '是']:
            test_single_page()
        
        print("=" * 50)
        print("✅ 所有测试完成")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()