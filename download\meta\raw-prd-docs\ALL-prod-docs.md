好的，作为一名文档整理专家，我已经对您提供的腾讯电子签产品说明文档进行了全面的整理、去重和结构化分类。以下是整理后的文档，内容详尽，结构清晰，方便您查阅。

***

### **腾讯电子签产品使用说明大全**

本文档旨在为您提供腾讯电子签产品（包含个人版、企业版、视频会议签等）的全面使用指南，涵盖了从产品入门、账号管理、合同操作到技术集成和常见问题的各类信息。

---

### **第一章：产品概述**

本章介绍腾讯电子签的各个产品版本及其核心优势与应用场景。

#### **一、 腾讯电子签简介**

腾讯电子签（E-Sign Service）是一款为企业及个人提供安全、便捷的电子合同签约及证据保存服务的产品。用户只需一部手机即可完成合同签约与管理。签约全程将进行区块链记录，确保法律效力与安全性。

#### **二、 各版本介绍**

1.  **个人版**
    * **产品概述**：为个人用户提供安全、便捷的电子合同签约及证据保存服务。用户在实名认证后，可与约定方在线完成签约，并将签约过程存证保全。
    * **产品优势**：
        * 强大的腾讯云技术支持，具备区块链、小程序云开发等技术储备。
        * 依托微信生态，提供一站式签署和管理服务。
        * 具备全流程存证记录及提取能力，可直通在线诉讼平台。
    * **应用场景**：提供借条、收据、租房合同、买卖合同、兼职合同等多种常用合同模板。

2.  **企业版**
    * **产品概述**：面向企业客户的一站式电子合同解决方案，涵盖合同、模板、印章的全生命周期管理。支持网页端、小程序、企业微信及API等多种方式进行线上签约与存证。
    * **产品优势**：
        * 操作便捷，支持移动端随时随地发起和管理合同。
        * 联合微信、企业微信、腾讯会议等平台，形成丰富的生态。
        * 符合《电子签名法》要求，采用“至信链”区块链技术全程存证，确保证据效力。
    * **应用场景**：满足企业日常经营（如用工、采购销售、租赁）及与自有应用（如HR系统）集成的需求。

3.  **视频会议签**
    * **产品概述**：基于腾讯会议的应用，实现“边开会，边签约”。可在会议中将合同投屏共享、实时扫码签约，快速锁定客户。
    * **产品优势**：签约流程实时快捷，合规有效，且便于会后管理。
    * **应用场景**：保险销售、文件公证、远程股东会等。

4.  **战略会议签**
    * **产品概述**：一款开箱即用的战略签约工具，通过将手写签名实时展示在定制的签约背景页上，服务于线上及线下的战略签约仪式。
    * **产品优势**：开箱即用、交互式签署、支持自定义背景和会议布局（线上）。
    * **应用场景**：企业间战略合作、政府部门对外合作等仪式性签约。

---

### **第二章：个人版使用指南**

本章详细介绍个人版用户的账号管理和合同操作方法。

#### **一、 账号管理**

1.  **变更手机号**
    * 登录腾讯电子签小程序，进入“个人中心” > “账号信息页”，点击“更换手机号”。
    * 输入新手机号并验证，然后通过人脸识别确认本人意愿即可完成更换。

2.  **变更邮箱地址**
    * 在“个人中心”点击“查看个人信息”，进入邮箱地址信息页。
    * 点击“更换邮箱”，输入新邮箱地址并回填收到的验证码即可。

3.  **个人更名**
    * **须知**：更名后，原姓名下的所有个人印章、签名及法人章都将失效，需重新生成。所有未完成的合同将无法继续签署。
    * **流程**：在“个人中心” > “查看个人信息”，点击姓名进行更名操作。阅读并同意协议后，输入变更后的姓名，完成人脸识别即可。
    * **后续操作**：更名成功后，需按指引重新生成失效的个人印章/签名及法定代表人章。

4.  **与港澳台居民签署合同**
    * 在小程序个人版“个人中心” > “查看个人信息” > “高级设置”中，开启“同港澳台居民签署合同”开关。
    * 开启前需进行人脸识别，完成意愿确认。

5.  **设置签署验证方式**
    * 在“个人中心” > “查看个人信息” > “签署认证方式”页面。
    * 支持设置或关闭签署密码、指纹、面容ID等验证方式。开启指纹或面容ID需手机硬件支持，并需人脸识别授权。

6.  **账号注销**
    * **前提条件**：需先在所有已加入的电子签企业中完成离职。
    * **流程**：在“个人中心” > “查看个人信息” > “高级设置”中找到“账号注销”入口。
    * 仔细阅读注意事项并确认，通过人脸识别进行意愿确认后，即可成功注销。

#### **二、 合同操作**

1.  **使用官方模板**
    * **借条**
        * **限制**：仅支持22-75岁的大陆公民，单笔金额不超过50万元。
        * **发起**：在首页点击“借条”，填写借款双方信息、借款金额、利率等。可选择“对方填”或“我来填”来补充对方信息。
        * **签署**：双方信息补充完整后，发起人先签署并人脸识别。接收方通过短信或分享链接进入小程序补充信息并签署。
        * **管理**：支持撤销（对方未签时）、申请解除、申请结清、下载、申请出证报告及寻求法律服务。
    * **收据**
        * **限制**：支持18-75岁的大陆公民，单笔金额不超过50万元。
        * **收款人发起**：在首页点击“收据”，填写付款人信息、收款金额等，支付费用并发起。付款人通过链接进入小程序确认领取。
        * **付款人发起**：切换为“我付款”身份，填写收款人信息并发起。收款人通过链接进入签署并人脸识别。
        * **管理**：支持撤销（对方未操作时）、申请解除、下载及法律服务等。
    * **其他合同模板（租房、买卖等）**
        * **发起**：在首页“模板发起”中选择所需模板（如租房合同）。
        * 填写双方信息、合同具体条款（如租金、期限等），预览无误后发起。
        * **签署**：一方先通过链接或二维码补充信息并签署，然后将链接或二维码发给另一方完成签署。
        * **管理**：同样支持合同的解除、下载、出证及法律服务。

2.  **使用自有合同文件/图片**
    * **发起**：在首页选择“文件发起”或“图片发起”。文件支持从微信聊天中选择，图片支持从微信、手机相册或拍照。
    * 设置签署方信息，可选择“我来填”或“对方填”。
    * 在文件上拖拽签署控件到合适位置，然后发起合同。
    * **签署与管理**：流程与使用模板类似，双方完成签署后，合同生效。支持合同解除、出证等管理操作。

---

### **第三章：企业版使用指南**

本章为企业用户提供全面的操作指引，覆盖从认证到日常管理的方方面面。

#### **一、 企业认证**

1.  **认证方式**
    * **通过上传营业执照**：在小程序或网页端选择创建企业，上传营业执照或机构证件，系统会自动识别信息。非法人操作时，需要法人授权或通过授权书+对公打款验证。
    * **通过腾讯云账号**：前提是已拥有完成企业实名认证的腾讯云账号。登录该腾讯云账号，补充企业信息后，根据操作人身份（法人或员工）完成相应授权流程。
    * **通过微信支付商户号授权**：前提是已开通微信支付商户号。通过商户号管理员授权，即可完成企业信息认证，后续流程同上。

2.  **企业信息收录指引**
    * 若在认证或变更信息时，系统提示信息无法校验（通常是因为工商数据未及时更新），可点击“提交收录申请”。
    * 平台将在1-3个工作日内审核，结果会以短信通知。审核通过后即可继续认证流程。

#### **二、 账号与组织管理**

1.  **企业账号管理**
    * **变更企业名称**：超管在小程序“个人中心” > “企业信息” > “更多设置”中，选择“变更企业基础信息”，上传新营业执照即可。
    * **变更法定代表人**：流程与变更企业名称类似，上传新营业执照后，系统识别出新的法定代表人，填写新法人联系方式即可完成。
    * **变更超级管理员**：法人或现任超管在小程序或PC端的“企业信息”中发起变更。根据操作人身份，可能需要法人人脸识别、法人授权或上传授权书。
    * **修改腾讯云安全手机**：在网页端“企业设置”中点击“修改手机号”，通过人脸识别验证身份后，输入新手机号和验证码即可。
    * **变更腾讯云账号**：法人或超管在小程序“企业信息”页，点击腾讯云账号旁的箭头，按指引授权新的腾讯云账号即可。
    * **注销企业账号**：法人或超管在小程序“企业信息” > “更多设置”中申请。需填写历史合同接收邮箱并签署注销协议。非法人超管操作需额外上传授权书。
    * **解绑企业微信**：法人或超管在小程序“企业信息”页，点击绑定的企业微信信息旁的箭头，阅读风险告知后，人脸识别确认即可解绑。

2.  **员工账号管理**
    * **变更手机号**：员工登录企业账号，在“个人中心” > “查看个人信息”中更换手机号，需验证新手机号并通过人脸识别。
    * **变更邮箱地址**：员工在“个人中心” > “查看个人信息”中更换邮箱，输入新邮箱和验证码即可。

3.  **组织架构与角色管理**
    * **组织架构**：在网页端“组织管理”中，可以新增、编辑、删除部门。支持手动、邀请码、表格导入等方式新增员工，并可进行员工的部门更换、角色编辑和离职交接。
    * **角色管理**：系统提供超级管理员、合同管理员等多种默认角色。企业可创建自定义角色，并配置其在合同、模板、印章等模块的数据权限和操作权限。

4.  **联系人管理**
    * 支持在网页端或小程序端手动或批量导入常用联系人（企业或个人）。
    * 发起合同时，可直接从联系人列表中选择，快速填充签署方信息。

#### **三、 印章与模板管理**

1.  **印章管理**
    * **添加印章**：支持添加企业印章（公章、合同章等）、法定代表人章、执业章等。可通过模板生成或上传印章图片创建。创建过程可能需要审批。
    * **管理印章**：可查看印章状态（启用、停用、审核中等），并进行授权、设置模板用印免审批、查看用印记录等操作。
    * **骑缝章**：高级版及以上版本支持。在配置合同时，可选择骑缝章控件，签署后印章将均等分布在每一页的右侧。

2.  **模板管理**
    * **创建模板**：在网页端或小程序上传文件（Word, PDF等），通过拖拽控件的方式设置填写区和签署区，定义签署流程（顺序、自动签署等），最后保存为模板。
    * **管理模板**：支持对已有模板进行编辑、删除、启用/停用、复制等操作。还可生成“一码多签”的签署码，适用于多人签署同一份协议的场景。

#### **四、 合同操作全流程**

1.  **合同起草**
    * 上传本地Word文档，在线进行内容编辑，支持多人协作编辑、文本批注和版本管理。定稿后可直接发起签署。

2.  **合同发起**
    * **文件发起**：上传文件，拖拽控件定义签署区和填写区，指定签署方和签署顺序，设置认证方式、审批流等，最后发起合同。
    * **模板发起**：选择已创建好的企业模板或官方模板，填写签署方信息和必要内容后即可快速发起。
    * **批量发起**：选择一个支持批量发起的模板，下载并填写签署方信息的Excel模板，上传后即可一次性发起多份合同。

3.  **合同签署**
    * **网页端/移动端**：签署人会收到短信通知。进入签署页面，填写必要信息后，点击签署区选择印章或签名。根据设置，可能需要通过密码、短信验证码或人脸识别等方式完成意愿认证。
    * **批量签署**：拥有权限的员工可一次性选择多份待签合同，完成一次认证即可全部签署。
    * **自动签署**：需开通服务并授权。在模板中设置本企业自动签署后，当其他方完成签署，系统将自动为本企业盖章。

4.  **合同管理**
    * 在合同中心（网页端）或文件夹（小程序端），可查看所有合同的状态。
    * 支持对合同进行撤销、拒签、转他人处理、下载、设置合同类型、再写一份等操作。
    * 提供“合同验签”工具，检验合同是否被篡改。
    * 提供“合同对比”工具，智能对比两份PDF文件的内容差异。
    * 支持通过接口发起“合同解除”协议。

#### **五、 企业设置与费用管理**

1.  **企业设置（拓展服务）**
    * **审批流配置**：支持配置腾讯电子签内置审批流和企业微信审批流，可为用印、模板收藏、合同审批等场景设置多级、或签/会签的审批流程。
    * **合同审批强制开启**：开启后，企业内所有合同发起时都必须经过审批。
    * **其他设置**：还可配置与港澳台居民签约、拓展签署方年龄限制、隐藏经办人姓名、短信通知等功能。

2.  **费用管理**
    * 在网页端或小程序的“费用管理”中，可以查看当前企业版本、套餐余量，并进行新购、续费或购买合同加量包等增值服务。
    * 支持查询订单、查询消耗明细和在线开具发票。

---

### **第四章：集团客户（多企业）操作指南**

本章为使用集团账户的企业提供专属操作指引。

* **产品概述**：支持集团型企业集中管理所有成员企业的合同、模板、印章，并共享主企业的合同份额。适用于专业版和旗舰版。
* **集团组织构建**：由主企业创建集团组织，然后通过邀请码邀请子企业扫码授权加入。主企业可管理成员的激活与解绑。
* **集团角色管理**：系统预设集团合同管理、印章管理等角色。主企业可将这些角色授予主企业员工，使其能够管理子企业的相关事务。
* **集团合同/模板/印章管理**：拥有集团权限的主企业员工，可以在网页端切换至各成员企业，对其合同、模板、印章进行统一管理。支持主企业代子企业发起合同，以及将主企业的模板分享给子企业使用。

---

### **第五章：技术与集成**

本章为开发者和技术人员提供API集成和技术问题的解答。

#### **一、 API与SDK**

* **常见问题**：
    * **签名计算错误**：请确保使用官方SDK，并检查`SecretId`和`SecretKey`是否正确且与请求环境（测试/正式）匹配。
    * **收不到回调**：请检查回调URL是否公网可访问，并确保能正确响应200状态码。
    * **上线前准备**：上线时需更换为正式环境的API密钥、回调地址和请求域名。

#### **二、 小程序集成**

* **跳转电子签小程序**：客户的小程序或App可通过`wx.navigateToMiniProgram`等方式，携带合同ID等参数跳转到电子签小程序指定页面（如合同详情页）完成签署。
* **参数配置**：提供了详细的`path`路径和参数说明，可实现跳转到首页、列表页、个人中心、合同详情页等。

#### **三、 控件与定位**

* **PDF签名位置计算**：以Adobe阅读器为例，通过“准备表单”功能添加文本域，在“位置”属性中获取坐标（原点为左下角）。`ComponentPosX` 为左对齐坐标，`ComponentPosY` 为页面高度减去上对齐坐标。
* **API定位**：文件发起时，支持通过关键字或坐标定位控件。关键字定位可通过`OffsetX`和`OffsetY`微调，坐标定位可使用官方提供的“PDF坐标计算助手”。

---

### **第六章：常见问题（FAQ）**

本章汇总了用户在使用过程中最常遇到的问题及其解决方案。

#### **一、 账号与认证**

* **实名认证失败**：请检查是否因脸部遮挡、与照片差距大、信息填写错误等原因导致。
* **提示“已存在实名账号”**：说明该身份信息已用其他手机号注册，根据提示换绑手机即可。
* **更换超级管理员**：支持在线变更，具体流程请参见企业账号管理部分。

#### **二、 合同与签署**

* **合同发起后如何通知**：系统会自动通过短信通知签署人。
* **已签署合同能否作废**：已完成签署的合同不支持撤销，建议另行签署解除协议。
* **打开合同提示无权查看**：请确认当前登录账号的身份信息与合同签署人是否一致。
* **签署方年龄限制**：企业员工作为经办人需年满16周岁。个人签署方默认18-75周岁，企业可开通拓展服务以支持16岁及以上用户。

#### **三、 印章与模板**

* **签署时提示没有可用印章**：请与管理员确认是否已获得该印章的使用权限。
* **如何配置自定义模板**：需有模板管理权限的员工在网页端进行操作，通过拖拽控件的方式完成配置。

#### **四、 法律效力**

* **电子合同是否有效**：腾讯电子签严格遵循《民法典》和《电子签名法》，通过可靠的实名认证和意愿验证流程，确保电子签名的有效性，与手写签名或盖章具有同等法律效力。
* **如何证明是本人签署**：通过实名认证确保签名归属，通过签署时的意愿验证（如短信、人脸识别）确保为本人控制。
* **发生争议怎么办**：可申请《电子文件签署报告》（包含区块链存证证明），用于在诉讼或仲裁中作为证据。

#### **五、 费用与购买**

* **如何计算合同份数**：合同发起成功即扣减一份。仅在所有方均未签署（不含自动签）的情况下，撤回合同会返还份数。
* **版本附赠合同用完怎么办**：可购买“合同加量包”增值服务。
* **如何开发票**：企业超管或有费用管理权限的员工可在网页端“费用管理”中申请开票。
# 路浩AI电子签 - 产品概要设计（V4.2）

## 1. 产品愿景与价值主张

### 1.1 产品愿景
致力于成为中国市场领先的、以AI技术为核心驱动的智能合同全生命周期管理平台。我们的目标是推动传统合同管理模式的变革，将繁琐的事务性工作转变为企业的数据资产与智能决策引擎，赋能每一个组织实现更高效、更安全、更智能的商业协作。

### 1.2 战略定位
"路浩AI电子签"并非单纯的电子签名工具，而是一个融合了**合规签署**、**智能管理**与**业务赋能**三位一体的企业级SaaS解决方案。我们以金融级的安全合规电子签名为基石，将AI能力深度渗透到合同起草、审查、签署、履约、归档、检索的每一个环节，立志成为企业数字化转型战略中不可或缺的信任基础设施。

### 1.3 核心价值主张
*   **极致效率 (Efficiency)**: 将数天甚至数周的合同流转周期缩短至几分钟，显著提升商业成交速度与组织运营效率。
*   **绝对安全 (Security)**: 提供符合国家法律法规（如《电子签名法》）及国密标准的全链路安全保障，确保每一份合同的法律效力与数据安全。
*   **深度智能 (Intelligence)**: 利用AI技术降低合同风险、挖掘数据价值，提供前所未有的合同洞察力，辅助企业决策。
*   **无缝集成 (Integration)**: 通过开放的API和嵌入式组件，将电子签能力无缝融入企业现有的ERP、CRM、HRM等业务系统，打破数据孤岛。

---

## 2. 目标用户与适用对象

本平台服务于广泛的用户群体，根据其属性和需求，主要分为以下三类：

| 用户类别 | 核心特征与需求                                                                                                                              |
| :------- | :------------------------------------------------------------------------------------------------------------------------------------------ |
| **个人用户** | **特征**: C端普通用户，如自由职业者、租赁双方、C2C交易者等。<br>**需求**: 快速、便捷、低成本地签署具有法律效力的个人协议、声明、合同等，并能安全地存储和查看。 |
| **企业客户** | **特征**: 从初创公司到大型集团，是平台的核心服务对象。<br>**需求**: 需求多样，覆盖合同全生命周期管理，注重效率、成本、合规风控、组织权限管理和业务系统集成。 |
| **政务用户** | **特征**: 政府机关、事业单位等，对安全、合规、审计和国产化有极高要求。<br>**需求**: 电子政务公文流转、审批，与民众或企业间的各类电子凭证、证明、协议的签署与管理，要求支持国密算法和信创环境。 |

---

## 3. 产品核心场景与终端结构

### 3.1 核心业务场景覆盖
平台完整覆盖电子合同的全生命周期，核心业务场景包括：

1.  **合同发起**: 支持通过本地文件上传、使用企业模板、AI智能问答三种方式快速发起合同草稿。
2.  **协同拟定**: 支持多人在线实时编辑、添加批注、管理版本，高效完成合同定稿。
3.  **流程签署**: 支持顺序、并行、混合等多种签署流程配置，通过多重意愿认证（人脸、密码、短信）确保签署行为真实有效。
4.  **存证归档**: 已签署合同自动上链存证（可选），并进行加密归档，提供完整的操作日志与证据链报告。
5.  **合同管理**: 提供智能分类、多维度高级检索、权限隔离等能力，高效管理海量合同。
6.  **合同处置**: 支持合同的下载、打印、作废、解除等后处理操作。
7.  **履约监控**: （高级功能）对合同关键履约节点（如付款、交付）进行智能提醒与追踪。

### 3.2 支持终端结构
为满足用户在不同场景下的使用需求，平台提供全方位、一致性的多端体验：

*   **PC Web端**: 功能最全面的管理后台，面向企业管理员、法务、财务等角色，提供组织架构、权限、模板、合同等深度管理功能。
*   **H5 移动端**: 轻量化的移动网页应用，无需安装，方便用户通过手机浏览器或App内嵌WebView快速查看和签署合同。
*   **微信小程序**: 深度集成微信生态，支持微信一键登录、拉取手机号、调用微信支付和人脸核身，提供流畅的移动签署体验。
*   **嵌入式组件 (SDK)**: 可嵌入企业自有应用系统（Web/H5）的前端组件，实现"零跳转"的无缝集成体验。
*   **API/SDK**: 面向开发者的全功能接口，支持与各类业务系统的深度集成。

---

## 4. 支付与计费体系

为满足不同用户的付费需求，平台提供灵活的支付与计费方案。

### 4.1 支付方式
*   **个人用户**: 支持主流线上支付方式，包括 **微信支付** 和 **支付宝**。
*   **企业客户**:
    *   线上支付：支持 **微信支付** 和 **支付宝**。
    *   线下支付：支持 **对公银行转账**，满足企业财务合规要求。

### 4.2 计费模式
平台主要采用预付费套餐模式：

*   **按份数计费**: 售卖不同规格的合同签署套餐包（如 100份/年、1000份/年），企业根据自身用量购买。
*   **按资源计费**: 对于短信、实名认证、AI审查等增值服务，可单独购买资源包。
*   **私有化部署**: 针对大型集团或政务用户，提供专属的私有化部署方案，按项目制收费。

### 4.3 发票管理
*   **发票类型**: 支持开具增值税普通发票和专用发票。
*   **申请与查看**: 用户可在线上提交发票申请，填写开票信息。申请通过后，可在线查看和下载电子发票。
*   **发票邮寄**: 对于纸质专票，提供邮寄服务。 # 功能差异补齐笔记 (对标竞品分析)

**文件目的**: 本文档旨在通过对标腾讯电子签、e签宝、法大大、契约锁、上上签、爱签等行业主流产品，识别“路浩AI电子签”在V3版本中存在的功能差距，并提出V42版本的核心补齐与超越建议。本文档是后续产品详细设计、技术选型和工作量评估的输入。

---

## 1. 核心签约与合同管理模块

| 功能点 | 对标产品表现 | “路浩AI电子签”V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **合同验签** | **腾讯/e签宝**: 提供独立的验签工具，用户上传已签署的PDF，系统验证数字签名是否有效、文件是否被篡改。 | **[补齐]** 必须提供独立的、公开的验签入口。API层面也要提供验签接口。这是建立信任的基础。 | ★★★★★ |
| **合同对比** | **腾讯/法大大**: 提供智能对比工具，高亮显示两份合同（如不同版本）的文本差异。 | **[补齐]** 提供PDF内容对比功能。**[超越]** 利用AI，不仅对比字面差异，还能分析条款语义层面的重大变更，并给出风险提示。 | ★★★★☆ |
| **骑缝章** | **腾讯/契约锁**: 作为高级功能提供，配置合同时可添加骑缝章控件，签署后印章自动均分在每页侧边。 | **[补齐]** 必须支持标准骑缝章功能，这是合同规范性的重要体现。支持配置边距、方向。 | ★★★★★ |
| **合同模板市场** | **e签宝/法大大**: 不仅有企业内部模板，还有一个官方模板市场，提供各行业经过法务审核的标准合同模板，用户可付费使用。 | **[补齐]** 建立官方模板库，覆盖劳动、租赁、采购、销售等高频场景。**[超越]** AI可根据用户行业和需求，从模板市场智能推荐最合适的模板。 | ★★★★☆ |
| **合同解除/作废** | **腾讯/契约锁**: 通过线上发起并签署《解除协议》的方式，使原合同状态变更为"已解除"，流程闭环。作废需要审批流。 | **[补齐]** 提供标准化的线上合同解除/作废流程。作废必须强制关联审批流，并记录操作原因。 | ★★★★☆ |
| **履约管理** | **契约锁**: 核心优势之一。支持在合同中设置履约计划（如付款节点、交付日期），到期自动提醒，并可上传履约凭证。 | **[补齐]** V42应包含基础的履约提醒功能。可从合同文本中通过AI自动识别关键日期和金额，并建议用户创建提醒。 | ★★★☆☆ |

---

## 2. 企业组织与权限管理

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **精细化审批流** | **腾讯/契约锁**: 支持配置多级审批、会签/或签。可将审批流与用印、发合同、模板使用等场景绑定。支持与企业微信/钉钉审批打通。 | **[补齐]** 必须建立一个强大的、独立的审批流引擎。支持图形化配置，满足企业复杂的"先审后签"或"用印申请"需求。 | ★★★★★ |
| **子公司/集团管控** | **腾讯/e签宝**: 提供集团账户功能，主企业可集中管理、查看、审计成员子企业的合同、印章，并能统一分享模板、共享套餐。 | **[补齐]** 明确支持集团模型。主企业可以代子企业发起合同，可以统一管理印章授权。这是服务大型客户的必备功能。 | ★★★★★ |
| **业务员/代理人** | **法大大**: 支持"业务员"角色，业务员自己名下的合同数据相互隔离，但其上级可以查看。 | **[补齐]** 在RBAC（基于角色的权限控制）中，数据权限需要支持"本部门及子部门"、"仅本人"之外，增加"本人及下属"这一维度。 | ★★★★☆ |
| **外部联系人管理** | **腾讯/e签宝**: 提供企业级的外部联系人地址库，方便在发起合同时快速选择、填充签署方信息。 | **[优化]** V3已有基础，但需强化。支持批量导入，并记录对方的历史签署信息（如认证状态）。 | ★★★☆☆ |

---

## 3. AI赋能与智能化

这是"路浩AI电子签"实现差异化竞争的关键领域。

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **AI合同审查** | **法大大/部分竞品开始尝试**: 提供AI审查，识别缺少关键条款、条款约定不明、存在不公平条款等风险。通常作为增值服务。 | **[核心-超越]** 这是我们的核心优势。必须深度打造。不仅能审查，还能**一键生成修改建议**。支持用户上传自己的审查标准和条款库，形成企业专属的审查模型。 | ★★★★★ |
| **AI合同生成** | **V3已规划**: 通过问答式生成合同。 | **[核心-强化]** V42需落地。模型需覆盖更广的合同类型。**[超越]** 实现"草稿续写"和"条款优化"功能，在用户编辑合同时，AI能像Copilot一样提供智能补全和润色建议。 | ★★★★★ |
| **智能归档与检索** | **腾讯/e签宝**: 支持按合同类型、签署方、时间等多维度检索。部分支持OCR全文检索。 | **[超越]** 利用AI自动为合同打上标签（如"含保密条款"、"3年期"、"固定资产采购"），并支持**自然语言搜索**，例如输入"查找去年跟华为签的所有服务器采购合同"。 | ★★★★☆ |
| **智能印章OCR** | **V3已规划**: 上传印章图片，AI自动抠图。 | **[核心-补齐]** 必须实现高精度的AI抠图和像素优化。**[超越]** 增加印章真伪辅助识别功能，通过比对印章线条、文字布局等，对疑似伪造的印章进行风险提示。 | ★★★★☆ |

---

## 4. 安全、合规与开放能力

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **国密算法支持** | **e签宝/契约锁**: 明确支持SM2/SM3/SM4国密算法，以满足政务、金融等信创领域客户需求。 | **[补齐]** 技术架构必须将国密算法作为核心选项，允许用户在签署时选择使用国密标准进行摘要和加密。 | ★★★★★ |
| **区块链存证** | **腾讯/法大大**: 普遍采用区块链技术进行存证，并能出具包含区块链哈希的证据报告，增强司法采信度。 | **[补齐]** 必须整合区块链存证能力，无论是自建联盟链还是对接第三方权威区块链（如至信链、司法链）。 | ★★★★☆ |
| **出证报告** | **所有竞品**: 都能提供具备法律效力的《电子文件签署报告》，作为发生纠纷时的核心证据。 | **[补齐]** 提供一键申请出证报告功能。报告内容需详尽，包含操作日志、IP地址、时间戳、数字签名信息、区块链存证信息等。 | ★★★★★ |
| **嵌入式组件(SDK)** | **腾讯/e签宝**: 提供成熟的前端嵌入式SDK，让用户在自己的业务系统里就能完成签署，无需跳转。 | **[补齐]** 必须提供功能完善且UI可定制的前端组件，这是实现与客户业务系统无缝集成的关键。 | ★★★★★ |
| **事件回调(Webhook)** | **腾讯/所有主流平台**: 提供可靠的Webhook机制，能实时将合同状态（已签署、已完成、已拒签等）通知给客户的业务系统。 | **[补齐]** Webhook是开放能力的基石。需要支持消息重试、签名验证，并提供详尽的事件类型供客户订阅。 | ★★★★☆ |

---

**总结**: V42版本需要在补齐行业标准功能（如骑缝章、验签、审批流、集团管控）的基础上，将 **AI合同审查与生成** 作为核心壁垒进行深度打造，并完善 **国密支持** 与 **开放集成能力**，以此确立产品在市场中的差异化竞争优势。 # 路浩AI电子签 - 技术架构与实现（V4.2）

本文档旨在为"路浩AI电子签"V4.2版本提供一份全面、清晰的技术实现蓝图。内容涵盖整体架构、技术选型、数据库方案、核心安全设计、AI能力集成策略以及部署运维等，旨在指导技术团队进行高效、规范的开发工作。

## 1. 整体架构与技术选型

平台采用领先的云原生微服务架构，以领域驱动设计（DDD）为指导思想，确保系统的高内聚、低耦合、高可用和可扩展性。

### 1.1 前后端技术栈

| 领域 | 技术选型 | 核心框架/库 | 选型理由 |
| :--- | :--- | :--- | :--- |
| **后端微服务** | **Java (主力) & Golang (辅助)** | **Java**: Spring Boot 3.x, Spring Cloud Alibaba<br>**Go**: Go-Kratos | **Java**: 生态成熟，拥有强大的事务管理和复杂的业务逻辑处理能力，适合构建核心业务域服务（如合同、组织、计费）。<br>**Go**: 高并发性能卓越，编译速度快，适合构建中间件、网关、AI服务等对性能要求高的基础设施。 |
| **前端应用** | **React** | Next.js, **Tailwind CSS, ShadCN/UI** | **React**: 业界主流，组件化开发模式成熟。<br>**Tailwind CSS**: 原子化CSS框架，提供极高的开发效率和定制自由度。<br>**ShadCN/UI**: 提供一套设计精良、可访问性高的无头组件，与Tailwind完美结合，能快速构建美观、统一的UI界面。 |

### 1.2 数据库与存储方案

为应对不同类型的数据存储需求，我们采用混合数据库架构。

| 方案 | 数据库/服务 | 存储内容 | 选型理由 |
| :--- | :--- | :--- | :--- |
| **关系型数据库** | **PostgreSQL 15+** | 核心业务数据：用户信息、企业信息、组织架构、合同元数据、订单、印章信息等。 | **PG**相比MySQL，在GIS、复杂查询、自定义数据类型和扩展性方面更具优势。其先进的MVCC实现和JSONB字段对非结构化数据的支持，能更好地满足未来业务扩展需求。 |
| **文档数据库** | **MongoDB 7.x** | 非结构化/半结构化数据：合同版本历史、操作日志、用户行为数据、Webhook推送记录等。 | Schema-less特性使其非常适合存储结构多变、写入频繁的日志类和文档类数据，查询性能高。 |
| **搜索引擎** | **Elasticsearch 8.x** | 合同正文、附件内容（需OCR提取）、各类可供检索的元数据。 | 提供强大的全文检索和聚合分析能力，是实现合同内容搜索、自然语言搜索的关键。 |
| **对象存储** | **MinIO (私有化) / Aliyun OSS (公有云)** | 最终版合同PDF、附件、上传的印章图片、人脸识别影像等所有非结构化文件。 | 提供高可用、高可靠的海量文件存储能力，支持S3标准协议，易于扩展和维护。 |
| **键值/缓存数据库** | **Redis 7.x** | Session会话、热点数据缓存（如权限、配置）、分布式锁、消息队列的简单替代等。 | 内存读写性能极高，能有效降低数据库压力，提升系统响应速度。 |

### 1.3 中间件与可观测性

| 类型 | 技术选型 | 用途 |
| :--- | :--- | :--- |
| **消息队列** | **RabbitMQ** | 服务间的异步通信、任务解耦、流量削峰。例如，合同签署完成后的多渠道通知、异步生成报表等。 | 成熟稳定，支持多种消息模式（如Fanout, Direct, Topic），并有完善的延迟队列和死信队列机制，足以满足当前业务需求。 |
| **监控告警** | **Prometheus + Grafana** | 采集和存储所有微服务的性能指标（Metrics），通过Grafana进行可视化展示和配置告警规则。 | 云原生领域的事实标准，与K8s生态无缝集成，便于对服务进行全方位的性能监控。 |
| **日志系统** | **ELK Stack (Elasticsearch, Logstash, Kibana)** | 集中收集、存储和分析所有微服务的应用日志。Kibana提供强大的日志查询和可视化界面。 | 成熟的日志解决方案，便于开发者进行问题排查和系统审计。 |

### 1.4 分布式存储设计

针对平台中海量的非结构化文件（合同、附件、印章图片等），我们设计了统一的分布式存储方案，其核心目标是高可用、高持久、安全可控。

*   **统一上传流程**:
    1.  前端向业务服务（如`contract-service`）请求上传凭证。
    2.  业务服务生成一个预签名的URL（Pre-signed URL），该URL具有时效性（如5分钟）和特定的上传权限，并包含文件最终在对象存储中的路径（Object Key）。路径命名规范：`{tenant_id}/{business_type}/{date}/{uuid}.{ext}`。
    3.  前端使用该URL，通过HTTP PUT请求直接将文件流上传至对象存储（MinIO/S3），绕过业务服务器，避免不必要的带宽和内存消耗。
    4.  上传成功后，前端将Object Key和文件元数据（文件名、大小、Hash等）提交给业务服务。
    5.  业务服务将文件元数据与其业务数据（如合同ID）在数据库中进行关联。

*   **访问控制与安全**:
    *   所有存储桶（Bucket）均设置为 **私有读写**。
    *   外部用户或前端应用绝不直接通过永久密钥访问，所有访问（上传/下载）均通过上述有时效性的预签名URL进行，实现最小权限和租户隔离。
    *   启用服务端加密（SSE-S3），由对象存储服务自动对写入的文件进行加密，进一步增强数据安全性。

*   **高可用与持久性**:
    *   **私有化部署 (MinIO)**: 采用纠删码（Erasure Coding）模式部署，例如`EC:4`表示数据被分成4个数据块和4个校验块，存储在8台不同的服务器上。这种模式允许最多4台服务器宕机而不丢失数据，极大地提高了存储的利用率和可靠性。
    *   **公有云部署 (OSS)**: 直接利用云厂商提供的多副本、跨可用区（AZ）存储能力，数据持久性可达99.9999999999%（12个9），免去自行维护的复杂性。

## 2. 安全加密与唯一性设计

安全是电子签平台的生命线。我们从数据、传输、存储、合规等多个维度构建纵深防御体系。

### 2.1 合同唯一性与防篡改

*   **合同唯一ID**: 每份合同（每条签署流程）在创建时，系统会生成一个全局唯一的、趋势递增的ID（如使用雪花算法`Snowflake`）。此ID将贯穿合同的整个生命周期。
*   **文件摘要算法**: 所有上传的文件在进入签署流程前，都会使用国密 **`SM3`** 算法（或 **`SHA-256`** 作为备选）计算其内容的哈希摘要。此摘要将作为文件的唯一"数字指纹"。
*   **数字签名**: 每一方签署时，平台会调用CA（证书颁发机构）服务，使用签署人的个人/企业数字证书，对 **当前文件版本的内容摘要** 和 **关键签署信息**（如签署人身份、时间、IP）进行数字签名。
*   **时间戳**: 每次签名操作都会加盖一个由权威TSA（时间戳服务机构）颁发的、具有法律效力的可信时间戳，精确记录签署发生的法定时间。
*   **防篡改机制**: 最终生成的合同PDF，会将所有签署方的数字签名、时间戳、证据链信息嵌入其中。任何对PDF内容的微小改动都会导致至少一个数字签名失效，通过合同验签功能即可立即识别。

### 2.2 数据加密方案

*   **传输加密**: 客户端与服务器之间的所有通信，均强制使用 **HTTPS (TLS 1.3)** 协议进行加密，防止数据在传输过程中被窃听或篡改。
*   **存储加密 (静态数据加密)**:
    *   **文件加密**: 所有存储在对象存储（MinIO/OSS）中的合同、附件等敏感文件，均使用 **AES-256** 对称加密算法进行加密存储。每个文件使用独立的密钥（DEK）。
    *   **密钥管理**: 文件加密密钥（DEK）本身，则使用 **RSA-2048** 非对称加密或通过KMS（密钥管理服务）的主密钥进行加密保护。这确保了即使存储介质泄露，文件内容也无法被解密。
    *   **数据库加密**: 核心敏感字段（如用户身份证号、手机号）在数据库中采用加密或脱敏方式存储。

## 3. AI模块技术实现策略

AI是本平台的核心差异化能力。我们将自研与第三方服务相结合，构建强大的AI赋能域。

### 3.1 核心AI能力栈

*   **大语言模型 (LLM)**: 支持灵活接入和切换多种业界领先的大模型，如 **Deepseek-V2**, **Qwen-Max**, **ChatGLM-4**, **豆包Pro** 等。通过统一的接口层进行封装，方便进行模型评估和路由。
*   **向量数据库**: 使用 **Milvus** 或 **PostgreSQL (pgvector)** 来存储合同条款、法律法规、企业知识库等文本的向量化表示，是实现RAG（检索增强生成）的核心。
*   **AI计算框架**: 使用 **Python (FastAPI/PyTorch)** 来构建AI服务，提供模型推理接口。

### 3.2 AI功能实现方案

*   **合同智能生成与审查 (RAG)**:
    1.  **知识库构建**: 将高质量的合同范本、法律法规、企业自己的标准合同等进行切片、向量化，存入向量数据库，构建成专业知识库。
    2.  **用户意图理解**: 当用户通过对话或上传合同进行交互时，首先用LLM理解其核心需求。
    3.  **检索增强**: 将用户需求或待审查的合同文本转换为向量，在知识库中检索最相关的条款或风险点作为上下文（Context）。
    4.  **增强生成**: 将原始请求和检索到的上下文一起打包成一个结构化的Prompt，发送给LLM，生成更精准、更专业的合同文本或审查报告。

*   **印章OCR识别与智能抠图**:
    1.  **模型选型**: 使用 **U-Net** 或类似的图像分割模型，结合传统的计算机视觉技术（如霍夫圆变换、边缘检测）进行印章识别和定位。
    2.  **数据增强**: 在训练模型时，使用大量不同光照、角度、模糊程度的印章图片进行数据增强，提升模型鲁棒性。
    3.  **后处理**: 对模型输出的掩码（Mask）进行精细化处理，去除毛刺和背景噪声，生成高保真的、带透明通道的PNG图片。

## 4. 部署与运维架构

*   **容器化与编排**: 所有微服务都将被打包成 **Docker** 镜像。使用 **Kubernetes (K8s)** 作为容器编排平台，实现服务的自动化部署、弹性伸缩和故障自愈。
*   **CI/CD**: 采用 **GitLab CI/CD** 或 **Jenkins** 构建自动化的持续集成与持续部署流水线。代码提交后，自动触发编译、测试、镜像构建和部署流程。
*   **服务治理**: 借助于 **Istio** 服务网格（或Spring Cloud/Kratos自带能力），实现服务发现、负载均衡、熔断、限流、灰度发布等高级治理能力。

## 5. 核心数据库表结构设计

为确保模型清晰和数据一致性，以下为核心业务微服务所依赖的PostgreSQL数据库表结构设计（仅列出核心字段）。

### 5.1 账户服务 (account-service)

*   `users` (用户信息表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 用户唯一ID |
| `mobile` | `varchar(20)` | `Unique` | 手机号（加密存储） |
| `email` | `varchar(100)` | | 邮箱（加密存储） |
| `user_status` | `smallint` | | 用户状态 (1:未实名, 2:已实名, 3:已注销) |
| `kyc_info` | `jsonb` | | 实名信息 (姓名, 身份证号, 国籍等)（加密） |
| `created_at` | `timestamp` | | 创建时间 |

*   `enterprises` (企业信息表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 企业唯一ID |
| `enterprise_name` | `varchar(100)` | | 企业名称 |
| `credit_code` | `varchar(50)` | `Unique` | 统一社会信用代码 |
| `auth_status` | `smallint` | | 认证状态 (1:未认证, 2:认证中, 3:已认证) |
| `legal_person_info`| `jsonb` | | 法人信息 |
| `super_admin_id`| `bigint` | `FK(users.id)` | 超级管理员的用户ID |

### 5.2 组织权限服务 (org-permission-service)

*   `org_nodes` (组织架构节点表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 节点ID |
| `enterprise_id` | `bigint` | `FK(enterprises.id)`| 所属企业ID |
| `parent_id` | `bigint` | | 父节点ID (根节点为0) |
| `node_name` | `varchar(50)` | | 部门名称 |
| `node_type` | `smallint` | | 节点类型 (1:部门, 2:员工) |
| `user_id` | `bigint` | `FK(users.id)` | 如果是员工节点，对应的用户ID |

*   `roles` (角色表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 角色ID |
| `enterprise_id` | `bigint` | `FK(enterprises.id)`| 所属企业ID (0代表系统预设角色) |
| `role_name` | `varchar(50)` | | 角色名称 |
| `permissions` | `jsonb` | | 权限码集合, e.g., `["contract:create", "seal:use"]`|

*   `user_roles` (用户角色关联表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `user_id` | `bigint` | `PK, FK` | 用户ID |
| `role_id` | `bigint` | `PK, FK` | 角色ID |
| `enterprise_id` | `bigint` | `PK, FK` | 企业ID |

### 5.3 合同与印章服务 (contract & seal service)

*   `contracts` (合同主表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 合同ID (业务ID) |
| `title` | `varchar(255)` | | 合同标题 |
| `enterprise_id` | `bigint` | `FK` | 发起方企业ID |
| `initiator_id` | `bigint` | `FK` | 发起人用户ID |
| `flow_status` | `smallint` | | 流程状态 (1:草稿, 2:签署中, 3:已完成, 4:已撤销, 5:已作废) |
| `created_at` | `timestamp` | | 创建时间 |

*   `contract_files` (合同文件表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 文件ID |
| `contract_id` | `bigint` | `FK` | 合同ID |
| `file_name` | `varchar(255)` | | 原始文件名 |
| `object_key` | `varchar(255)` | | 在对象存储中的路径 |
| `file_hash` | `varchar(100)` | | 文件内容摘要 (SM3/SHA256) |
| `version` | `int` | | 文件版本号（每次签署后递增） |

*   `contract_signers` (合同签署方表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | |
| `contract_id` | `bigint` | `FK` | 合同ID |
| `signer_type` | `smallint` | | 签署方类型 (1:个人, 2:企业) |
| `signer_id` | `bigint` | | 签署主体ID (个人为user_id, 企业为enterprise_id) |
| `actor_user_id` | `bigint` | | 经办人用户ID (企业签署时) |
| `sign_order` | `int` | | 签署顺序 |
| `sign_status` | `smallint` | | 签署状态 (1:待签, 2:已签, 3:已拒) |
| `signed_at` | `timestamp` | | 签署时间 |

*   `seals` (印章表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 印章ID |
| `owner_id` | `bigint` | | 所属主体ID (个人为user_id, 企业为enterprise_id) |
| `seal_type` | `smallint` | | 印章类型 (1:个人签名, 2:企业公章, 3:合同章) |
| `seal_name` | `varchar(50)` | | 印章名称 |
| `object_key` | `varchar(255)` | | 印章图片在对象存储中的路径 |
| `created_at` | `timestamp` | | 创建时间 |

## 6. 核心消息队列(MQ)流转设计

为实现服务解耦和异步处理，我们使用RabbitMQ，并定义以下核心的消息流转模式。

| 业务场景 | 生产者 | Exchange名称<br>(类型) | Routing Key | Queue名称 | 消费者 | 核心消息体 (JSON) |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **合同状态变更** | `SigningSvc` | `contract.topic`<br>(Topic) | `contract.signed.first`<br>`contract.signed.all`<br>`contract.rejected` | `notify_queue`<br>`webhook_queue`<br>`log_queue` | `MessageSvc`<br>`WebhookSvc`<br>`LogSvc` | `{ "contractId": "...", "eventType": "...", "timestamp": "...", "operatorId": "..." }` |
| **异步生成证据链** | `SigningSvc` | `evidence.direct`<br>(Direct) | `evidence.generate` | `evidence_gen_queue` | `EvidenceSvc` | `{ "contractId": "...", "requestorId": "..." }` |
| **AI处理任务** | `ContractSvc` | `ai_task.direct`<br>(Direct) | `ai.review.start`<br>`ai.extract.start` | `ai_review_queue`<br>`ai_extract_queue` | `AISvc` | `{ "taskId": "...", "fileObjectKey": "...", "taskType": "..." }` |
| **用户/组织变更** | `AccountSvc`<br>`OrgSvc` | `iam.fanout`<br>(Fanout) | (无) | `iam_sync_to_contract`<br>`iam_sync_to_seal` | `ContractSvc`<br>`SealSvc` | `{ "changeType": "user.rename", "userId": "...", "oldName": "...", "newName": "..." }` |

*   **`contract.topic` (主题交换机)**: 用于广播合同相关的各类事件。`MessageSvc` 订阅所有消息以发送通知；`WebhookSvc` 订阅以触发对外回调；`LogSvc` 订阅以记录审计日志。
*   **`evidence.direct` (直接交换机)**: 用于需要可靠投递的、一对一的任务处理，如生成耗时较长的证据链报告。
*   **`iam.fanout` (扇出交换机)**: 用于广播用户和组织信息的变更事件。当用户更名或企业认证信息变更时，所有关心这些数据的下游服务（如合同服务、印章服务，需要同步冗余信息）都能收到通知并更新自己的数据，保证最终一致性。

## 7. 部署与运维架构

*   **容器化与编排**: 所有微服务都将被打包成 **Docker** 镜像。使用 **Kubernetes (K8s)** 作为容器编排平台，实现服务的自动化部署、弹性伸缩和故障自愈。
*   **CI/CD**: 采用 **GitLab CI/CD** 或 **Jenkins** 构建自动化的持续集成与持续部署流水线。代码提交后，自动触发编译、测试、镜像构建和部署流程。
*   **服务治理**: 借助于 **Istio** 服务网格（或Spring Cloud/Kratos自带能力），实现服务发现、负载均衡、熔断、限流、灰度发布等高级治理能力。 

## **构建新一代AI智能电子签章平台：业务与技术体系研究方案 (V4.2 修订版)**

### **摘要 (Executive Summary)**

本方案旨在为“路浩AI电子签”（以下简称“本平台”）项目提供一套完整的业务体系与技术架构设计。在对标行业主流产品的基础上，我们明确了以**法律、专利领域为切入点，覆盖政企和个人用户**的市场策略。核心差异化在于**极致易用的产品体验、深度融合的AI合同能力**，以及清晰的价格体系。技术上，我们坚定选择**纯SaaS、云原生**的路线，确保系统的高可用、高扩展性和安全性。

-----

### **第一章：产品背景、意义与价值 (Why We Build It)**

#### **1.1 市场背景与机遇**

  * **宏观趋势**: 数字化转型浪潮下，无纸化办公和远程协作成为常态。《电子签名法》、《民法典》等法律法规为电子合同的法律效力提供了坚实保障。
  * **市场痛点**: 传统纸质合同签署流程繁琐（打印、快递、存储）、成本高昂、效率低下、管理困难、易丢失、易篡改，已成为企业发展的瓶颈。
  * **竞争格局**: 市场已有头部玩家，但仍存在机会点。部分产品功能臃肿、体验复杂；AI功能多为“锦上添花”，未能深入核心流程；特定行业（如法律、知识产权）的深度场景需求未被完全满足。

#### **1.2 核心价值主张 (Value Proposition)**

  * **对企业/政府用户**:
      * **降本增效**: 将数天的签署周期缩短至几分钟，极大降低时间、物流和仓储成本。
      * **安全合规**: 提供金融级的安全保障和完整的证据链条，确保每一份合同的法律效力，有效防范法律风险。
      * **智能管理**: 通过AI能力，将静态的合同文档转化为动态的数据资产，辅助决策，挖掘商业价值。
      * **业务加速**: 通过API/SDK无缝集成到企业现有业务系统（OA, CRM, ERP），打通数据孤岛，加速交易闭环。
  * **对个人用户**:
      * **便捷高效**: 随时随地签署个人文件（劳动合同、租房协议、借据等），告别纸张和快递。
      * **普惠法律**: 以可负担的价格（`¥399/年`）享受安全、合规的电子签约服务。

-----

### **第二章：产品框架与版本规划 (What We Build)**

#### **2.1 产品总体框架 (Product Architecture)**

平台是一个集**前端应用、业务中台、技术底座**于一体的综合性SaaS服务。

```mermaid
graph TD
    subgraph Layer_Presentation [用户呈现层]
        A[PC Web管理端]
        B[H5/移动签署端]
        C[微信小程序]
        D[API/SDK 开放平台]
    end

    subgraph Layer_Business [核心业务层]
        S1[合同全生命周期管理]
        S2[印章管理 (企业/个人)]
        S3[身份认证与权限(RBAC)]
        S4[模板中心]
        S5[证据链与存证]
        S6[AI智能服务]
    end

    subgraph Layer_Platform [平台支撑层]
        P1[统一账户中心]
        P2[计费与订单中心]
        P3[消息通知中心]
        P4[运营管理后台]
    end

    subgraph Layer_Infrastructure [基础设施层]
        I1[云原生底座 (Kubernetes)]
        I2[微服务框架 (Java+Spring/Go+Kratos)]
        I3[数据存储 (MySQL/PG, MongoDB, ES, Redis)]
        I4[消息队列 (RabbitMQ)]
        I5[任务调度 (XXL-Job)]
    end

    Layer_Presentation --> Layer_Business
    Layer_Business --> Layer_Platform
    Layer_Platform --> Layer_Infrastructure
```

#### **2.2 目标用户画像 (Target Persona)**

  * **个人用户**: C端用户，有租赁、借贷、劳动合同等签署需求。
  * **法律/专利从业者**: 律师、法务、专利代理人。对合同/文件的严谨性、合规性、检索效率要求极高。
  * **中小企业 (SME)**: 核心需求是降本增效，需要标准化的合同管理和签署流程。
  * **大企业/政府 (政企客户)**: 需求复杂，注重组织权限、审批流程、系统集成和信息安全。

#### **2.3 产品版本与价格策略 (Editions & Pricing)**

| 版本名称 | 价格 | 目标用户 | 核心功能 | 差异化卖点 |
| :--- | :--- | :--- | :--- | :--- |
| **个人版** | `¥399 / 年` | 个人、自由职业者 | 个人实名认证、手写签名、文件发起与签署、合同管理 | 简单易用，满足个人高频签署需求 |
| **企业标准版** | `¥5,999 / 年` | 中小企业、创业团队 | 含个人版所有功能、企业认证、组织架构、角色权限、电子印章、基础审批流、模板管理 | 数字化转型第一步，性价比之选 |
| **企业专业版** | `¥12,999 / 年` | 中大型企业、法律/专利机构 | 含标准版所有功能、高级审批流、API/SDK集成、**基础AI能力(智能审查、信息提取)**、用印审计 | AI赋能与业务集成，提升专业效率 |
| **企业旗舰版** | `按需定制` | 集团客户、政府机构 | 含专业版所有功能、**高级AI套件(智能生成、履约跟踪)**、专属客户成功服务、信创环境支持（若未来考虑）、深度定制开发 | 全方位智能合同解决方案，战略合作 |

-----

### **第三章：核心功能设计 (Core Features)**

#### **3.1 基础核心功能 (Foundation)**

1.  **统一账户中心**:
      * **个人**: 手机/微信注册登录，支持**个人实名认证**（通过对接权威数据源，如公安部、银行卡四要素）。
      * **企业**: 支持**企业认证**（对公打款、法人授权等），构建企业组织架构（树形结构），精细化的**RBAC角色权限管理**（超级管理员、合同管理员、印章管理员、业务员等）。
2.  **合同生命周期管理**:
      * **发起**: 支持本地文件（PDF/Word/Excel）上传、使用模板发起、空白合同发起。
      * **签署**:
          * **流程设置**: 支持顺序、并行（无序）、指定签等多重签署流程。
          * **意愿认证**: 采用**短信验证码、签署密码、人脸识别**等多重方式确保签署意愿真实性。
          * **操作**: 支持在PC和移动端进行可视化拖拽放置签名/印章位，支持手写签名、图片签名、电子印章盖章。
      * **归档与管理**: 所有合同自动加密归档，提供多维度（标题、签署方、时间、标签）检索，支持合同分类、下载、打印。
3.  **印章管理**:
      * 支持在线生成符合规范的个人签名和企业电子印章。
      * 支持企业印章的授权使用和用印审批流程，所有用印操作均有日志记录。
4.  **证据链保全**:
      * 自动固化从发起、认证、签署到完成的每一步操作日志，形成完整的证据链。
      * 签署完成后，生成包含所有操作记录和数字签名信息的**证据报告**。
      * （可选增强）对接第三方司法鉴定中心或**区块链存证**平台，增强法律效力。

#### \*\*3.2 **核心差异化：AI智能功能 (AI-Powered Features)**

这是超越竞品、打造护城河的关键。

1.  **AI智能生成 (旗舰版)**: 用户通过自然语言描述合同需求（如“生成一份软件开发外包合同，甲方是A公司，乙方是B，开发周期3个月，总金额20万”），AI自动生成结构化、条款完备的合同初稿。
2.  **AI智能审查 (专业版/旗舰版)**:
      * 上传合同后，AI自动进行**风险审查**，识别缺失条款（如违约责任、管辖法院）、不公平条款、潜在风险点，并给出修改建议。
      * **合规性审查**: 针对特定行业（如法律、金融），校验合同是否符合最新的法律法规。
3.  **AI信息提取 (专业版/旗舰版)**:
      * 自动从合同正文中提取关键信息（合同双方、金额、日期、标的物等），形成结构化数据，方便后续管理和数据分析。
4.  **AI智能检索**: 支持“用大白话找合同”，例如输入“查找去年跟XX公司签的所有金额超过10万的销售合同”，系统能理解并返回精确结果。

#### **3.3 开放平台 (Open Platform)**

  * 提供全面的 **RESTful API** 和多语言 **SDK (Java, Go, Python, PHP)**。
  * 支持与企业现有系统（OA、CRM、ERP、HRM）无缝集成，实现合同发起、签署状态同步等。
  * 提供**嵌入式（H5）签署组件**，可将签署环节无缝嵌入到企业自身的业务流程页面中，无需跳转。

-----

### **第四章：核心使用流程 (User Journeys)**

#### **4.1 个人用户签署流程**

```mermaid
sequenceDiagram
    participant User as 个人用户
    participant Platform as 本平台
    participant Landlord as 房东

    User->>Platform: 微信/手机号登录
    User->>Platform: 完成个人实名认证
    Landlord->>Platform: 上传租房合同PDF, 发起签署
    Platform->>Landlord: 设置自己和User的签名位置
    Platform-->>User: 发送签署邀请短信/微信通知
    User->>Platform: 点击链接, 查看合同
    User->>Platform: 进行人脸识别意愿认证
    User->>Platform: 在指定位置手写签名
    Platform-->>User: 签署完成
    Platform-->>Landlord: 通知所有方签署完毕, 合同生效
    Platform->>Platform: 自动归档合同并生成证据报告
```

#### **4.2 企业用户（含审批与AI审查）流程**

```mermaid
sequenceDiagram
    participant Employee as 业务员
    participant Manager as 部门经理
    participant Legal as 法务 (AI辅助)
    participant Platform as 本平台
    participant Customer as 客户

    Employee->>Platform: 登录企业账号, 使用模板发起销售合同
    Employee->>Platform: 填写客户信息, 设置审批流: 经理审批 -> 法务审批
    Platform-->>Manager: 发送审批待办通知
    Manager->>Platform: 审批通过
    Platform-->>Legal: 发送审批待办, 并触发AI审查
    Legal->>Platform: 查看合同, 右侧显示AI审查报告("提醒: 缺少明确的知识产权归属条款")
    Legal->>Platform: 根据AI建议, 在线修订合同条款
    Legal->>Platform: 审批通过
    Platform-->>Employee: 通知: 合同已就绪, 可发起给客户
    Employee->>Platform: 确认并发起签署
    Platform-->>Customer: 发送签署邀请
    Customer->>Platform: 完成签署
    Platform->>Platform: 自动加盖本方企业印章 (可配置自动签)
    Platform->>Platform: 合同完成, 通知所有方并归档
```

-----

### **第五章：业务运营后台 (Admin Console)**

运营后台是保障SaaS业务健康运转的关键，功能应包括：

  * **仪表盘 (Dashboard)**: 核心数据指标实时看板（注册用户数、活跃用户数、合同签署量、GMV、ARPU等）。
  * **用户管理**: 查询、管理平台所有个人和企业用户，处理账户异常。
  * **订单管理**: 查看所有套餐购买记录，处理支付、退款、开票申请。
  * **套餐管理**: 配置不同版本的产品功能、价格、合同份数。
  * **模板市场**: 管理官方标准合同模板，审核用户上传的共享模板。
  * **内容管理 (CMS)**: 发布公告、帮助文档、行业资讯。
  * **工单系统**: 接收和处理来自用户的各类问题反馈。

-----

### **第六章：技术架构体系设计 (Technical Architecture)**

#### **6.1 设计原则**

  * **云原生**: 100%基于云设计，不提供私有化部署，便于统一运维、快速迭代。
  * **微服务**: 遵循DDD（领域驱动设计）划分服务边界，高内聚、低耦合。
  * **安全第一**: 安全贯穿整个生命周期，遵循最小权限原则。
  * **自主可控**: 核心加密、签名环节优先考虑国密算法。

#### **6.2 技术选型**

| 模块 | 技术栈 | 备注 |
| :--- | :--- | :--- |
| **微服务框架** | Java (Spring Boot/Cloud) + Go (Kratos) | Java处理复杂业务逻辑（如订单、审批），Go处理高性能中间件（如网关、AI服务）。 |
| **数据库** | MySQL/PostgreSQL + MongoDB + Elasticsearch + Redis | **MySQL/PG**: 核心业务数据（用户、订单、合同元数据）。**MongoDB**: 存储半结构化数据（如操作日志、证据链）。**ES**: 全文检索、AI智能搜索。**Redis**: 缓存、分布式锁、会话。 |
| **消息队列** | RabbitMQ | 用于服务间异步解耦、事件通知、流量削峰。 |
| **定时任务** | XXL-Job | 处理定时任务，如账单生成、合同状态检查、数据同步。 |
| **前端** | React/Vue + Ant Design Pro | 成熟的企业级中后台解决方案。 |
| **移动端** | Uni-app / 微信小程序原生开发 | 快速跨端，覆盖移动场景。 |
| **部署运维** | Docker + Kubernetes (K8s) + GitLab CI/CD + Prometheus/Grafana | 标准的云原生运维体系。 |

#### **6.3 部署架构 (简图)**

  (示意图，实际应包含K8s集群、VPC、负载均衡、RDS、Redis实例等云资源拓扑)

-----

### **第七章：核心技术要点与难点 (Key Technical Challenges)**

1.  **安全性与合规性 (Top Priority)**:

      * **数字签名技术**: 必须深入理解PKI体系，安全地与权威CA中心对接，进行证书申请、签发和管理。整个签名过程需在HSM（硬件安全模块）中完成，确保私钥不落地。
      * **国密算法支持**: SM2/SM3/SM4的集成与应用，以满足政企客户的信创要求。
      * **数据安全**: 全链路数据加密（传输层TLS 1.3，存储层AES-256），数据库敏感字段（身份证、手机号）加密存储。
      * **防篡改**: 采用符合国际标准的PAdES（PDF高级电子签名）格式，将签名信息、时间戳嵌入PDF内部，任何修改都会导致签名失效。

2.  **AI能力集成与优化**:

      * **模型选择与微调**: 是直接调用通用大模型API（如GPT、文心一言），还是在开源模型（如ChatGLM、Llama）基础上进行私有化Fine-tuning？后者效果更好、数据更安全，但成本更高。建议初期采用API，后期积累数据后进行微调。
      * **性能与成本**: AI调用是主要成本中心和性能瓶颈。必须设计**异步化处理机制**，并通过缓存、提示词工程（Prompt Engineering）优化效果和降低Token消耗。
      * **数据隐私**: 在调用外部AI服务时，必须进行数据脱敏，防止核心合同信息泄露。

3.  **高可用与数据一致性**:

      * **数据库扩展**: 核心业务库未来需要考虑分库分表方案。
      * **分布式事务**: 在微服务架构下，使用**Saga模式**或**基于消息队列的最终一致性**方案来保证跨服务的原子操作（例如，创建合同成功后，必须成功扣减套餐份数）。

4.  **PDF/文档处理的可靠性**:

      * **格式转换**: 如何将用户上传的各类文件（Word, Excel, JPG）完美、高保真地转换为统一的PDF格式，是一个工程难点。
      * **坐标定位**: 在不同分辨率、不同尺寸的屏幕上，精确获取用户签名/盖章的坐标，并准确无误地渲染到PDF的对应位置，需要大量的客户端适配工作。

-----

### **结论**

本研究方案提供了一个清晰的、从商业模式到技术实现的完整蓝图。项目的成功关键在于：**（1）聚焦法律、专利等高价值垂直领域，形成壁垒；（2）将AI能力深度融合到核心流程，打造真正的“智能”体验；（3）在安全合规上做到极致，赢得客户信任。** 建议按照产品版本规划，采用敏捷开发模式，从最小可行性产品（MVP）开始，快速验证市场，持续迭代优化。# 路浩AI电子签 - 用户与核心流程设计（V4.2）

本文档旨在明确"路浩AI电子签"平台的所有核心用户角色，并以流程图和文字描述相结合的方式，详细阐述关键业务流程，确保产品设计与开发团队对用户操作路径有统一和清晰的理解。

## 1. 核心用户角色定义 (System Roles)

V4.2版本对用户角色进行了扩展和细化，以覆盖更广泛的业务场景。

| 角色分类 | 角色名称 | 核心职责与权限范围 |
| :--- | :--- | :--- |
| **外部用户** | **个人用户** | **职责**: 作为独立的法律主体，签署个人相关的合同、协议、文件。 <br> **权限**: 拥有个人账户管理、实名认证、个人签名/印章管理、发起/签署个人合同、管理个人合同等能力。 |
| | **签署人 (Signatory)** | **职责**: 代表个人或企业，完成指定合同的签署操作。是临时的、任务性的角色。<br>**权限**: 仅限于查看和签署被指定的合同，无法访问平台其他功能。 |
| **企业内部用户** | **超级管理员 (Super Admin)** | **职责**: 企业平台的最高权限所有者，负责企业认证、购买服务、初始化配置、分配系统级管理员等。<br>**权限**: 拥有平台所有管理权限，是企业安全的最后一道防线。 |
| | **企业管理员 (Admin)** | **职责**: 负责企业的日常运营管理，包括组织架构维护、员工账号管理、角色与权限分配等。<br>**权限**: 由超管授予，通常管理除"超管设置"外的所有功能。 |
| | **法务人员 (Legal)** | **职责**: 负责企业合同的合规性审查、标准模板的制定与管理、处理合同纠纷。<br>**权限**: 通常被授予查看所有合同、管理合同模板、配置审批流、处理出证申请等权限。 |
| | **财务人员 (Finance)** | **职责**: 负责企业的费用管理、发票申请与核销、对公打款认证、合同金额相关的审批。<br>**权限**: 拥有费用中心访问权限，通常作为高金额合同审批流中的关键节点。 |
| | **业务人员 (Employee)** | **职责**: 企业内业务的执行者，在被授予的权限内发起和管理与自身业务相关的合同。<br>**权限**: 权限范围由其角色决定，如发起合同、申请用印、查看自己参与的合同等。 |
| **政务用户** | **机构管理员** | **职责**: 类似于企业管理员，负责政务机构的组织与人员管理，以及公文模板、签章的管理。<br>**权限**: 管理机构内部的组织树、人员账号，配置公文流转规则，管理机构电子签章。 |
| **平台后台** | **运营后台人员** | **职责**: 路浩AI电子签平台的内部运营和支持人员。<br>**权限**: 管理官方模板市场、处理用户申诉、审核特殊认证（如企业更名）、监控平台运行状态等，不接触客户业务数据。 |

---

## 2. 核心业务流程 (Core Business Flows)

### 2.1 流程一：标准合同签署全流程 (从起草到归档)

此流程覆盖了一份标准合同从无到有、从草稿到法律文件的完整生命周期。

```mermaid
graph TD
    subgraph A [合同拟定]
        A1[用户选择发起方式] --> A2{AI生成/模板发起/本地上传};
        A2 -- AI生成 --> A3[与AI助手多轮对话, 生成合同初稿];
        A2 -- 模板发起 --> A4[选择模板, 填写业务信息];
        A2 -- 本地上传 --> A5[上传Word/PDF, 系统自动转换格式];
    end

    subgraph B [内部协同与审批]
        A3 & A4 & A5 --> B1[在线协同编辑<br>多人修改/批注/版本管理];
        B1 --> B2{是否需要审批?};
        B2 -- 是 --> B3[提交审批流<br>法务/财务/主管审批];
        B3 --> B4{审批通过?};
        B4 -- 否 --> B1;
    end
    
    subgraph C [签署流程配置]
        B2 -- 否 --> C1;
        B4 -- 是 --> C1[设置签署方及签署顺序(顺序/并行)];
        C1 --> C2[拖拽添加签署区/填写控件];
        C2 --> C3[设置签署意愿认证方式(人脸/密码)];
    end

    subgraph D [多方签署]
        C3 --> D1[发起合同, 通知第一顺位签署人];
        D1 --> D2[签署人通过H5/小程序<br>完成信息填写和签署];
        D2 --> D3{所有方是否签署完毕?};
        D3 -- 否 --> D1;
    end

    subgraph E [完成与归档]
        D3 -- 是 --> E1[合同签署完成, 通知所有参与方];
        E1 --> E2[生成带数字签名的最终PDF文件];
        E2 --> E3[**自动归档**<br>加密存储, 上链存证(可选)];
        E3 --> E4[用户可随时检索、下载、申请出证];
    end
```

**操作页面视角描述**:
1.  **拟定**: 业务员在PC后台点击"发起合同"，选择"AI生成"，在弹出的对话框中回答AI提问。AI生成草稿后，页面跳转至在线编辑器，业务员可手动修改文本。
2.  **审批**: 编辑器右侧有"提交审批"按钮。点击后，选择预设的"高金额合同审批流"，系统将任务推送给财务总监。财务总监在手机端收到通知，打开合同预览，点击"同意"。
3.  **配置**: 业务员收到审批通过的通知，返回合同编辑页面，从右侧工具栏拖拽"签署区"控件到文件末尾，并从通讯录选择乙方签署人。
4.  **签署**: 乙方签署人收到短信，点击链接进入手机H5页面，在指定位置手写签名，系统提示进行人脸识别，完成后提示"签署成功"。
5.  **归档**: 流程结束后，业务员在"我的合同-已完成"列表中能看到该合同，并可点击"下载"或"申请出证"。

### 2.2 流程二：企业认证与印章管理流程

此流程是企业首次入驻平台并完成基础配置的核心步骤。

```mermaid
graph TD
    A[企业管理员首次登录] --> B(第一步：企业认证);
    B --> C{选择认证方式};
    C -- 法定代表人授权 --> D[填写企业名和信用代码<br>系统发送链接给法人];
    D --> E[法人扫码人脸识别, 认证成功];
    C -- 对公打款 --> F[填写企业对公账户信息];
    F --> G[向该账户打一笔随机金额];
    G --> H[管理员回填准确金额, 认证成功];
    
    E & H --> I(第二步：印章创建与管理);
    I --> J{选择创建方式};
    J -- AI抠图上传 --> K[上传清晰的实体印章盖印图片];
    K --> L[系统AI自动抠图、优化、生成预览];
    J -- 标准模板生成 --> M[选择印章类型(公章/合同章)<br>系统根据认证信息生成国标样式印章];
    
    L & M --> N[管理员确认, 印章创建成功];
    N --> O(第三步：印章授权);
    O --> P[选择印章, 点击"授权"];
    P --> Q[选择授权对象(员工/部门/角色)];
    Q --> R[设置授权期限(长期/指定时间)];
    R --> S[完成授权, 相关人员即可在签署时使用该印章];

```
**操作页面视角描述**:
1.  **认证**: 企业管理员在后台"企业设置-企业认证"页面，选择"法定代表人授权"方式，输入企业名称后，点击"发送授权邀请"，并将弹出的二维码截图发给法人。
2.  **印章创建**: 认证成功后，进入"印章管理"页面，点击"新增印章"，选择"上传图片创建"。上传一张在白纸上盖的公章图片，页面loading几秒后，显示出一个背景透明的、红色的印章预览图，点击"确认创建"。
3.  **印章授权**: 在印章列表，找到刚刚创建的"合同专用章"，点击操作栏的"授权"按钮。在弹出的窗口中，左侧是组织架构树，勾选"销售部"，点击"确定"，即完成对整个销售部的授权。

### 2.3 流程三：发票申请与处理流程

此流程展示了企业在购买套餐后如何申请发票。

```mermaid
sequenceDiagram
    participant Admin as 企业管理员/财务
    participant Platform as 平台后台
    participant BillingSvc as 计费中心
    participant FinanceSys as 企业财务系统

    Admin->>Platform: 1. 进入"费用中心-订单管理"
    Admin->>Platform: 2. 勾选一个或多个可开票的订单
    Admin->>Platform: 3. 点击"申请发票"
    
    Note right of Admin: 页面跳转至发票信息填写页

    Admin->>BillingSvc: 4. 填写发票抬头、税号、地址等信息, 选择发票类型(专票/普票), 提交申请
    BillingSvc-->>Admin: 5. 提示"申请已提交, 等待审核"
    
    BillingSvc->>运营后台: 6. 生成开票审核任务
    运营后台->>BillingSvc: 7. 审核通过, 触发开票
    
    alt 电子发票
        BillingSvc->>第三方税务接口: 8a. 请求开具电子发票
        第三方税务接口-->>BillingSvc: 9a. 返回发票PDF文件
        BillingSvc->>Admin: 10a. 发送通知, 管理员可在线下载/查看
        Admin->>FinanceSys: 11a. 下载电子发票, 导入财务系统报销
    else 纸质发票
        BillingSvc->>运营后台: 8b. 生成邮寄任务
        运营后台-->>Admin: 9b. 填写快递单号, 更新发票状态为"已邮寄"
        Admin->>FinanceSys: 10b. 收到纸质发票, 进行线下核验报销
    end

```
**操作页面视角描述**:
1.  **申请**: 财务人员登录后台，进入"费用中心-发票管理"，点击"索取发票"。在列表中勾选了上个月购买的"1000份合同套餐"订单，点击"下一步"。
2.  **填写**: 在发票信息页面，系统已自动带出企业名称和税号。财务人员补充了开户行和地址信息，选择了"增值税专用发票"，点击"提交"。
3.  **查看**: 两天后，财务人员收到平台短信通知，提示发票已开具。登录后台在"发票管理-历史记录"中，看到该条记录状态变为"已开具"，并可点击"下载"按钮获取PDF电子发票。

### 2.4 流程四：合同作废审批流程

对于已签署完成但需要作废的合同，平台提供合规的线上作废流程。

```mermaid
graph TD
    A[业务员在"已完成"合同列表中] --> B[找到需作废的合同, 点击"更多-申请作废"];
    B --> C[填写作废原因, 如"业务取消, 双方协商一致"];
    C --> D[选择预设的"合同作废审批流程"];
    D --> E[提交申请];
    
    subgraph Approval [审批环节]
        E --> F[任务流转至第一级审批人(部门主管)];
        F --> G{主管是否同意?};
        G -- 同意 --> H[任务流转至第二级审批人(法务)];
        H --> I{法务是否同意?};
        G -- 拒绝 --> J[流程终止, 通知申请人"作废被驳回"];
        I -- 拒绝 --> J;
    end
    
    I -- 同意 --> K[审批通过, 系统自动创建一份《合同作废协议》];
    K --> L[自动将原合同各签署方添加为作废协议的签署方];
    L --> M[向各方发送签署《作废协议》的通知];
    
    subgraph Signing [签署作废协议]
        M --> N[各方通过手机完成《作废协议》的签署];
        N --> O{是否全部签署完毕?};
        O -- 否 --> N;
    end
    
    O -- 是 --> P[原合同状态自动更新为"已作废"];
    P --> Q[所有操作被记录, 形成完整证据链];
```
**操作页面视角描述**:
1.  **发起申请**: 销售员在"我的合同"中找到一份已完成的合同，因客户业务调整需作废。他点击"申请作废"，在弹窗中写明原因并提交。
2.  **审批**: 销售主管和法务先后收到待办通知，在手机端查看作废申请和原合同信息后，均点击了"同意"。
3.  **签署作废协议**: 销售员和原合同的客户方法定代表人，都收到了签署一份名为"关于XXX合同的作废协议"的新通知，各自完成签署。
4.  **结果**: 签署完成后，销售员返回合同列表，看到原合同上已被标记了一个红色的"已作废"戳记。 # 电子签行业产品设计与技术架构深度研究报告

## 一、行业概览与市场格局

电子签名行业正经历前所未有的发展机遇。**2023年中国电子签名市场规模达297.32亿元，预计2030年将增长至926.58亿元，年复合增长率超过26%**。这一高速增长背后，是数字化转型浪潮推动下企业对无纸化办公和智能化合同管理的迫切需求。

**头部厂商竞争格局已基本确立**。e签宝凭借市场占有率最高和连续4年入选胡润独角兽榜位居第一梯队；法大大获腾讯9亿元D轮投资，专注法律科技差异化发展；上上签聚焦公有云业务，在中小企业市场表现突出；契约锁依托泛微背景在中大型企业客户中优势明显；腾讯电子签则利用微信生态在个人用户和小微企业中快速渗透。

市场呈现明显的差异化竞争特征：技术创新、生态整合、行业专业化和服务能力成为核心竞争要素。各厂商在保持技术领先的同时，正通过深度行业定制和生态合作构建护城河。

## 二、产品功能体系深度解析

### 核心业务架构对比

**腾讯电子签**基于微信生态构建了独特的产品体系。其**15秒极速签署流程**和**99.80%准确率的人脸核身技术**，配合至信链区块链存证，在用户体验和法律效力认定方面建立了差异化优势。个人版功能涵盖借条、收据等生活场景，企业版则提供30+常用合同模板和3天平均API接入周期。

**e签宝**构建了最完整的产品矩阵，包括公有云SaaS、混合云、API开放和智能合同管理四大体系。其**十种核心认证服务**和**智能合同审核能力**体现了技术深度，1000+标准合同模板则满足了广泛的业务场景需求。

**法大大**以法律科技为核心特色，通过**首批接入杭州互联网法院司法区块链**和**线上司法鉴定服务闭环**，在法律效力保障方面建立了独特优势。其音视频双录签名模式为高风险交易提供了额外保障。

**契约锁**专注企业级市场，其**UKey电子印章**支持断网环境签署，**光学水印防伪打印技术**适配传统业务习惯，在政企客户中建立了稳固地位。

### 个人版与企业版差异化设计

产品分层体现了精准的市场细分策略。**个人版主要提供基础签署功能**，通过免费或低价策略获取用户，培养使用习惯。**企业版则按照标准版、专业版、旗舰版递进设计**，在用户数量、功能权限、API调用次数等维度形成明确差异。

这种分层设计的核心逻辑是**按需付费和价值递进**。标准版满足基础需求，专业版增加批量处理和高级管理功能，旗舰版提供完整解决方案和定制化服务。用户数量限制从10-50用户递增到无限制，存储空间从GB级扩展到TB级，API调用从千次级提升到百万次级。

### 行业特殊需求解决方案

**法律行业**对电子签名提出了更严格的要求。司法鉴定需求要求多重身份认证确保真实性，防篡改技术保证完整性，权威时间戳证明时效性，完整操作日志支持可追溯性。法大大通过专业法务团队、公证处直连和音视频双录技术构建了完整的司法支持体系。

**专利行业**面临技术保密性、多方协作、版本管理和长期保存等特殊挑战。专利申请需要使用国家知识产权局签发的数字证书，支持PCT等国际标准，并实现20年以上的长期保存。这要求电子签名系统采用OFD、PDF/A等标准格式，建立多地备份和容灾机制。

## 三、云原生技术架构设计

### 微服务架构划分

现代电子签名系统普遍采用**微服务架构**，实现业务解耦和系统扩展性。核心微服务包括：用户服务负责身份认证和授权管理；签署服务处理电子签名创建和验证；文档服务管理文档存储和处理；证书服务提供数字证书全生命周期管理；时间戳服务确保签署时间可信；存证服务实现区块链存证和哈希校验。

基础设施微服务则提供统一认证、消息通知、配置管理、日志审计等支撑能力。这种架构设计能够**支持独立开发、部署和扩展**，提高系统整体的可维护性和可靠性。

### 容器化与Kubernetes编排

**容器化部署**已成为行业标配。通过Docker多阶段构建优化镜像大小，使用Kubernetes实现服务编排和自动扩缩容。Istio服务网格提供流量管理、安全策略和可观测性支持，Kong或Istio Gateway作为API网关实现统一流量入口。

**分布式数据库架构**采用分层存储策略：PostgreSQL集群存储元数据，对象存储(S3/MinIO)处理文档文件，InfluxDB记录时序数据，Elasticsearch提供搜索能力。多级缓存（应用内存缓存、Redis集群、CDN）确保系统性能。

### Java Spring与Go Kratos最佳实践

**Java Spring技术栈**通过Spring Boot构建微服务，Spring Cloud提供服务发现和配置管理，使用OpenFeign实现服务间通信。国密算法支持通过BouncyCastle库实现，确保符合中国密码标准。

**Go Kratos框架**以其高性能和简洁架构在高并发场景中表现优异。通过protobuf定义API，Wire实现依赖注入，内置的中间件支持恢复、追踪、日志和监控功能。Goroutine池控制并发数量，Context实现请求超时和取消处理。

## 四、电子签名核心技术攻关

### PKI数字证书体系

**数字证书是电子签名法律效力的基础**。PKI架构采用分层设计：根CA提供信任根，中间CA签发各类证书，包括服务器证书、用户证书和代码签名证书。时间戳CA独立运行，确保时间戳服务的可信性。

证书生成使用**SM2/SM3国密算法**，支持从密钥对生成到证书签发的完整流程。证书管理包括申请、审核、签发、更新、吊销全生命周期管理，确保证书安全可控。

### 防篡改与完整性保护

**多级哈希验证机制**是防篡改的核心技术。文档上传时计算SM3哈希值，签名时对哈希值进行SM2数字签名，时间戳服务为签名添加可信时间证明。任何对文档或签名的修改都会导致哈希值变化，从而被检测发现。

数字签名流程包括：文档哈希计算(SM3)、私钥签名(SM2)、时间戳添加(TSA)、证书链验证、区块链存证。这个完整流程确保了签名的**不可否认性、完整性和时效性**。

### 区块链存证技术

**区块链存证**为电子签名提供了不可篡改的证据保全。存证数据结构包含文档哈希、签名哈希、时间戳、签名者证书、默克尔根等关键信息。智能合约自动执行存证逻辑，确保数据上链后无法篡改。

主流厂商采用不同的区块链方案：腾讯电子签使用至信链，e签宝与蚂蚁区块链合作，法大大接入司法区块链平台。这些区块链平台都获得了法院认可，为电子证据的司法采信提供了技术保障。

### PDF签名域管理

**PDF签名技术**需要处理签名域位置管理、多重签名、签名外观定制等复杂需求。通过iText等PDF处理库实现CMS标准签名，支持LTV(Long Term Validation)长期验证。签名域管理支持可视化签名位置配置，多个用户可在同一文档的不同位置依次签名。

## 五、AI技术创新应用

### 智能合同生成与审查

**大语言模型在合同生成中发挥关键作用**。通过法律领域数据微调的GPT模型，能够根据业务场景自动生成个性化合同内容。结合RAG(Retrieval Augmented Generation)技术，确保生成内容的准确性和合规性。合同生成时间从数小时缩短至数分钟，准确率达95%以上。

**智能合同审查**利用NLP技术分析合同条款，自动识别风险点并提供修改建议。通过机器学习算法训练的风险识别模型，能够检测不合规、不明确或对企业不利的条款。版本比对功能自动高亮显示修改内容，大幅提高审查效率。

### OCR与文档智能处理

**OCR识别技术**将扫描或拍照的合同文档转换为可编辑文本，支持PDF、图片、Word等多种格式。**39个以上合同关键要素**能够自动提取，包括当事人信息、金额、日期、条款等核心内容。

结合计算机视觉技术，系统能够识别和验证印章、签名等视觉元素。**签名真实性检测准确率达92%以上**，通过分析笔迹特征、压力变化等参数识别伪造签名。

### 智能风险识别

**异常行为检测**通过机器学习算法分析签署模式，识别可能的欺诈行为。多模态AI技术同时分析签名的视觉特征和行为特征，提供更精准的身份验证。风险评估模型根据历史数据和行为特征，为每个签署行为提供风险评级。

**AI驱动的客服系统**基于大模型技术，提供7×24小时智能对话服务。问题识别和分类准确率达95%以上，支持多语言和实时翻译，大幅提升用户体验。

## 六、法律合规与风险控制

### 电子签名法律框架

**《电子签名法》确立了电子签名的法律地位**。可靠电子签名的四要素要求：签名制作数据专有、签署时仅由签名人控制、签名改动可被发现、文档改动可被发现。这些技术要求通过PKI体系、数字证书、哈希算法和时间戳服务得以实现。

**电子认证服务机构**需获得工信部颁发的许可证，满足注册资本3000万元、专业技术人员30名以上等条件。目前获得许可的机构包括CFCA、上海CA、广东CA等，为电子签名提供可信的身份认证服务。

### 数据安全与隐私保护

**《网络安全法》、《数据安全法》、《个人信息保护法》**构成了数据保护的法律框架。电子签名平台需建立数据安全治理体系，实施分类分级保护，制定应急预案，定期开展风险评估。

个人信息处理需遵循最小必要原则，保障用户知情权、决定权、查询权、更正权和删除权。跨境数据传输需通过安全评估或签订标准合同，目前尚无境外认证服务提供者获得核准。

### 行业特殊合规要求

**金融行业**要求建立严格的客户身份验证机制，采用多重交叉验证，确保签约主体身份真实可信。电子保单年签名量达4.5亿次，已成为重要应用场景。

**医疗行业**的电子病历签名需与执业资格绑定，符合《病历书写基本规范》，实现医疗责任可追溯。**政务领域**基于国家电子政务电子认证基础设施，采用国产密码算法，建立统一的电子印章管理平台。

## 七、商业模式与定价策略

### 分层定价体系

**电子签名行业形成了成熟的分层定价模式**。个人版399元/年定位于培养用户习惯，提供基础功能；企业标准版5999元/年满足中小企业需求，增加批量处理和模板管理；专业版129999元/年面向大型企业，提供完整解决方案和定制化服务。

这种定价策略体现了**价值导向和差异化竞争**的理念。不同版本在用户数量（10-50用户到无限制）、存储空间（GB级到TB级）、API调用次数（千次级到百万次级）等维度形成明确差异，满足不同规模企业的需求。

### 盈利模式创新

**SaaS订阅模式**是主流商业模式，通过年费订阅保证稳定现金流。**按使用量计费**降低了企业使用门槛，适合签署频次不固定的场景。**API调用增值服务**为开发者生态提供收入来源，通过生态分成实现共赢。

**企业定制化服务**提供高价值增值业务，包括私有化部署、定制开发、专业服务等。这些服务的利润率通常高于标准化产品，是企业级客户的重要收入来源。

### 运营数据与增长模型

**核心KPI包括**ARR（年度经常性收入）、用户留存率、CAC（客户获取成本）、LTV（客户生命周期价值）等。健康的SaaS企业LTV/CAC比率应维持在3:1以上，续费率应超过90%。

**2023年市场规模297.32亿元，预计2030年达926.58亿元**，年复合增长率26.4%。增长驱动因素包括数字化转型加速、法律法规完善、技术创新应用等。市场渗透率仍有巨大提升空间，为行业发展提供了充足动力。

## 八、技术发展趋势与建议

### 云原生架构演进

**容器化、微服务化、服务网格化**将成为技术架构标配。Kubernetes编排能力的提升，Istio服务网格的普及，以及云原生安全体系的完善，将推动电子签名系统向更高水平的云原生架构演进。

**多云和混合云部署**成为大型企业的必然选择。通过统一的云管理平台，企业可以灵活选择不同云服务商的优势服务，同时保持系统的一致性和可移植性。

### AI与区块链深度融合

**大语言模型**将在合同生成、审查、风险识别等环节发挥更重要作用。多模态AI技术的发展将支持文本、图像、语音的统一处理，提供更丰富的交互方式。

**区块链技术**将从简单存证向智能合约执行演进。通过区块链实现合同条款的自动执行、支付结算的自动触发，构建更完整的数字化合同生态。

### 行业标准化与国际化

**技术标准统一**是行业发展的必然趋势。在国密算法、数据格式、接口协议等方面建立统一标准，将降低企业集成成本，提高系统互操作性。

**跨境互认机制**的建立将为国际业务提供支撑。通过与欧盟eIDAS、美国ESIGN等法律框架的对接，实现电子签名的跨境法律效力认定。

## 结论

电子签名行业正处于技术创新与商业模式变革的关键时期。**云原生架构、AI技术应用、区块链存证、国密算法**等核心技术的成熟，为行业发展提供了坚实的技术基础。**分层定价、生态合作、行业定制**等商业模式创新，则为企业可持续发展指明了方向。

面向未来，电子签名将从简单的"签字盖章"工具，演进为智能化的合同全生命周期管理平台。**技术标准化、服务生态化、应用智能化**将成为行业发展的主要趋势。企业需要在技术投入、生态建设、合规管理等方面持续发力，以在激烈的市场竞争中建立可持续的竞争优势。# 路浩AI电子签 - 系统模块与链路设计（V4.2）

## 1. 系统总体架构与分层

路浩AI电子签平台采用先进的云原生微服务架构，确保系统的高可用、高并发和可扩展性。整体架构遵循领域驱动设计（DDD）思想，分为四个主要层次：

```mermaid
graph TD
    subgraph Layer_Frontend [前端应用层 (Frontend Applications)]
        direction LR
        A1[PC Web 管理端 (React + ShadCN)]
        A2[H5 移动端]
        A3[微信小程序]
        A4[嵌入式组件 (Embed SDK)]
    end

    subgraph Layer_Gateway [API网关层 (API Gateway)]
        B1[统一API网关 (Spring Cloud Gateway / Go-Kratos Gateway)]
    end

    subgraph Layer_Service [后台服务层 (Backend Microservices)]
        direction TB
        subgraph Domain_Core [核心业务域]
            C1[**合同服务 (contract-service)**<br>生命周期管理、模板、草稿]
            C2[**签署服务 (signing-service)**<br>签署流程执行、意愿认证、批量签署]
            C3[**印章服务 (seal-service)**<br>印章创建、管理、授权、用印日志]
        end

        subgraph Domain_Mgmt [企业管理域]
            C4[**账户服务 (account-service)**<br>个人/企业用户、实名认证]
            C5[**组织权限服务 (org-permission-service)**<br>组织架构、RBAC、集团管控]
            C6[**审批服务 (approval-service)**<br>独立的审批流引擎、用印/发文审批]
        end

        subgraph Domain_Support [业务支撑域]
            C7[**计费中心 (billing-service)**<br>套餐管理、订单、支付回调]
            C8[**消息中心 (message-service)**<br>短信、邮件、微信通知、Webhook]
            C9[**履约提醒服务 (fulfillment-service)**<br>履约节点管理、到期提醒]
        end

        subgraph Domain_AI [AI赋能域]
            C10[**AI服务 (ai-service)**<br>合同生成、审查、智能检索、OCR]
        end
    end

    subgraph Layer_Infra [基础设施层 (Infrastructure)]
        direction LR
        D1[**数据库**<br>MySQL, MongoDB, Elasticsearch]
        D2[**中间件**<br>Redis, RabbitMQ]
        D3[**存储**<br>MinIO / S3]
        D4[**可观测性**<br>Prometheus, ELK]
        D5[**安全设施**<br>CA/TSA, HSM, KMS]
    end

    Layer_Frontend --> B1
    B1 --> Layer_Service
    Layer_Service --> Layer_Infra
```

## 2. 核心模块说明

基于V3版本的设计，V4.2对服务进行了进一步的拆分与扩展，以支持更复杂的功能和更高的内聚性。

| 模块/服务名 | 核心职责 | V4.2 新增/强化功能 |
| :--- | :--- | :--- |
| **合同服务 (contract-service)** | 管理合同的元数据、草稿、版本、模板。 | 强化模板管理功能，增加官方模板市场；增加合同对比、履约计划关联等。 |
| **签署服务 (signing-service)** | 负责驱动整个签署流程，处理签署意愿认证。 | 从原合同服务中拆分，独立处理复杂的签署顺序（串行/并行）、自动签署、批量签署、骑缝章逻辑。 |
| **印章服务 (seal-service)** | 管理个人签名与企业印章，包括其生命周期和授权。 | 增加AI辅助的印章OCR识别与优化；强化用印日志的审计能力。 |
| **账户服务 (account-service)** | 统一管理个人与企业的基础信息和实名认证状态。 | 扩展对政务用户的支持；对接更多第三方认证源。 |
| **组织权限服务 (org-permission-service)** | 负责企业的组织架构、员工管理、RBAC角色权限体系。 | 新增集团管控模型，支持跨企业授权；增加"本人及下属"等更精细的数据权限。 |
| **审批服务 (approval-service)** | **[新增]** 独立的、通用的审批流引擎。 | 提供图形化的审批流配置界面，支持多级、会签、或签等复杂流程，服务于用印申请、合同发起审批等场景。 |
| **计费中心 (billing-service)** | **[新增]** 管理产品套餐、用户订单、支付与发票。 | 对接微信、支付宝、对公转账，处理支付回调，管理套餐余量，提供发票申请与下载功能。 |
| **消息中心 (message-service)** | 统一处理所有需要触达用户的通知。 | 强化Webhook机制，支持消息重试和签名验证，确保对客户业务系统通知的可靠性。 |
| **履约提醒服务 (fulfillment-service)** | **[新增]** 负责合同的后管理阶段。 | 支持用户设置关键履约节点（如付款、交付日），并通过消息中心在到期前发送提醒。 |
| **AI服务 (ai-service)** | 封装所有AI能力，为其他业务服务提供接口。 | 强化RAG能力，对接企业私有知识库；优化合同审查和生成模型；提供自然语言搜索合同的能力。 |

## 3. 核心系统链路图说明

以下Mermaid图描述了两个最核心的业务流程："AI合同生成与多方签署"和"需要审批的用印流程"，展示了各微服务之间的调用关系。

### 3.1 链路一：用户通过AI生成合同并发起多方签署

```mermaid
sequenceDiagram
    participant FE as 前端应用
    participant Gateway as API网关
    participant ContractSvc as 合同服务
    participant AISvc as AI服务
    participant SigningSvc as 签署服务
    participant MsgSvc as 消息中心

    FE->>Gateway: 1. 发起AI生成合同请求 (含对话历史)
    Gateway->>ContractSvc: 2. 转发请求
    ContractSvc->>AISvc: 3. 调用AI生成合同(GenerateContract)
    AISvc-->>ContractSvc: 4. 返回合同文本初稿
    ContractSvc-->>FE: 5. 返回合同草稿ID和内容
    
    Note right of FE: 用户在线编辑、确认合同内容<br/>并添加签署方、配置签署流程
    
    FE->>Gateway: 6. 确认发起签署(含签署方、流程信息)
    Gateway->>SigningSvc: 7. 创建签署任务(CreateSigningTask)
    SigningSvc->>ContractSvc: 8. 更新合同状态为"签署中"
    SigningSvc->>MsgSvc: 9. 请求发送签署通知 (To 签署人A)
    MsgSvc-->>签署人A: 10. 发送短信/微信通知
    
    Note right of FE: 签署人A完成签署...<br/>签署人B收到通知并完成签署...

    SigningSvc->>MsgSvc: 11. 请求发送合同完成通知 (To 所有参与方)
    MsgSvc-->>所有参与方: 12. 发送短信/微信通知
    SigningSvc->>ContractSvc: 13. 更新合同状态为"已完成"
    SigningSvc->>MsgSvc: 14. [Webhook] 推送合同完成事件
```

### 3.2 链路二：业务员申请使用"合同专用章"签署合同

```mermaid
sequenceDiagram
    participant FE as 前端应用
    participant Gateway as API网关
    participant SigningSvc as 签署服务
    participant ApprovalSvc as 审批服务
    participant SealSvc as 印章服务
    participant MsgSvc as 消息中心
    participant 印章管理员 as 印章管理员(审批人)

    FE->>Gateway: 1. 业务员在签署页选择"合同章"并发起
    Gateway->>SigningSvc: 2. 请求签署(ExecuteSign)
    SigningSvc->>SealSvc: 3. 检查用印权限(CheckSealPermission)
    SealSvc-->>SigningSvc: 4. 返回"无权限"，但告知需审批
    
    SigningSvc->>ApprovalSvc: 5. 创建用印审批申请(CreateApprovalRequest)
    ApprovalSvc->>MsgSvc: 6. 请求发送审批通知
    MsgSvc-->>印章管理员: 7. 发送待审批任务通知
    
    Note right of 印章管理员: 印章管理员在PC或手机上<br/>查看合同并点击"同意"
    
    印章管理员->>Gateway: 8. 提交审批意见(Approve)
    Gateway->>ApprovalSvc: 9. 转发审批意见
    ApprovalSvc->>SigningSvc: 10. 回调通知：用印申请已通过(ApprovalPassed)
    
    Note left of SigningSvc: 收到回调后，系统自动完成盖章
    
    SigningSvc->>SealSvc: 11. 记录用印日志(LogSealUsage)
    SigningSvc->>ContractSvc: 12. 更新合同签署状态
    SigningSvc-->>FE: 13. 返回签署成功
``` # 路浩AI电子签 - 详细功能列表（V4.2）

本文档旨在提供一份全面、详尽、结构化的产品功能规格说明。所有功能均按照"一级系统-二级模块-三级功能"的结构进行组织，并明确标注了AI赋能点。

---

## 1. 平台基础支撑系统 (Platform Foundation)

### 1.1 统一账户中心 (Account Center)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **个人用户体系** | 多渠道注册/登录 | 支持用户通过"手机号+验证码"、"微信一键授权"等多种方式进行注册和登录。首次登录即自动创建账户，流程顺滑，降低使用门槛。 | 否 |
| | 统一实名认证 | 提供多通道实名认证，包括"姓名+身份证号+人脸识别"(对接公安部/微信支付)、港澳台及护照认证。确保签署主体身份的真实性与合法性。 | 否 |
| | 个人账户管理 | 用户可修改绑定的手机、邮箱，设置或重置签署密码。支持通过线上申请、上传证明、人脸识别等方式进行个人法定名称的变更。 | 否 |
| **企业用户体系** | 多通道企业认证 | 提供法人授权认证、对公打款认证、微信支付商户号授权、上传营业执照+授权书等多种方式，以适应不同企业的操作便利性和合规要求。 | **是** (OCR识别营业执照) |
| | 企业信息管理 | 展示企业名称、信用代码、认证状态等。支持企业因工商变更而进行的名称、法人等信息的在线变更流程，变更时需进行强验证并引导重新生成印章。 | 否 |
| | 超级管理员与法人 | 明确超管和法定代表人的角色与权限。提供安全的超管变更流程，需法人人脸识别或上传盖章授权书，确保企业最高管理权限交接的安全性。 | 否 |
| **集团企业解决方案** | 集团组织构建 | 支持已认证企业创建"集团"，并邀请其他已认证企业作为"成员子企业"加入。子企业需授权主企业对其的管理权限。 | 否 |
| | 资源与权限管控 | 集团主企业可将购买的套餐包、模板等资源共享给子企业。集团管理员可根据授权，统一查看和管理所有子公司的合同、印章和数据。 | 否 |

### 1.2 组织与权限中心 (Organization & Permission Center)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **组织架构管理** | 部门与员工管理 | 支持以树状结构创建和管理多层级部门。支持手动、批量导入、邀请链接等多种方式添加员工，并可对员工进行部门调整、禁用、离职交接等操作。 | 否 |
| **角色与权限(RBAC)** | 系统预设角色 | 内置"超级管理员"、"合同管理员"、"法务"、"财务"等多种常用角色及其权限组合，方便企业开箱即用，快速完成权限分配。 | 否 |
| | 自定义角色 | 企业可创建新角色，并从权限池中精细勾选操作权限(增删改查、下载、授权等)。 | 否 |
| | 数据权限控制 | 为角色配置数据可见范围，支持"全公司"、"本部门及子部门"、"仅本人"、"本人及下属"等多种数据隔离维度，实现最小权限原则。 | 否 |
| **审批流引擎** | 审批流程配置 | **[核心]** 提供图形化的审批流设计器，支持设置多级审批、会签(所有人通过)、或签(一人通过即可)等复杂流程。 | 否 |
| | 审批场景绑定 | 可将创建的审批流与"发起合同"、"用印申请"、"模板使用"、"合同作废"等多种业务场景进行绑定，实现"先审后用/签"。 | 否 |

---

## 2. 合同全生命周期系统 (Contract Lifecycle)

### 2.1 合同拟定与模板 (Drafting & Template)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **合同发起** | 多源文件发起 | 支持用户通过上传本地Word/PDF/图片文件，或从企业/官方模板库选择模板来发起合同。所有文件统一转换为PDF格式处理。设有草稿箱。 | 否 |
| | AI合同生成 | **[核心]** 支持通过与AI助手进行多轮问答，快速生成一份完整的合同初稿。AI会根据对话内容，动态填充条款。 | **是** (RAG+LLM生成) |
| **在线编辑** | 协同编辑与评论 | 支持多人同时在线编辑一份合同草稿，修改实时同步。协作者可对具体条款进行评论和@相关人员，高效完成内部审核。 | 否 |
| | 版本历史追溯 | 系统自动保存所有历史版本，可随时查看、比较不同版本间的差异，并能一键恢复到任一历史版本。 | 否 |
| | AI文本辅助 | **[核心]** 在编辑合同时，AI可根据上下文推荐标准条款(如保密、争议解决)，提供"一键插入"功能。支持对选中段落进行"续写"或"润色"。 | **是** (条款推荐/续写/润色) |
| | AI合同审查 | **[核心]** 上传或编辑合同时，AI实时审查文本，识别潜在风险(如缺少关键条款、权利义务不对等、定义不清等)，并以高亮和批注形式提示。 | **是** (风险识别/条款分析) |
| **合同模板** | 模板制作与配置 | 管理员可将定稿的合同文件制作成模板，通过拖拽方式添加文本、日期、选择框等动态填写控件，并预设签署流程和参与方角色。 | **是** (智能识别控件位置) |
| | 企业模板库 | 企业内部共享的模板库，可按部门或业务线进行分类管理。支持对模板进行编辑、停用、分享等操作。 | 否 |
| | 官方模板市场 | 由平台法务团队提供的、覆盖各行业的高质量付费/免费合同模板。 | **是** (AI根据用户画像智能推荐) |

### 2.2 签署与流转 (Signing & Workflow)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **签署流程配置** | 灵活的签署顺序 | 支持无序、顺序、混合签署流程。发起人可通过拖拽方式灵活调整签署方的顺序。 | 否 |
| | 自动签署(本方) | 可配置本企业在满足特定条件(如其他方签署完毕)后，使用预设印章自动完成签署，无需人工操作，极大提升效率。 | 否 |
| | 抄送与关注方 | 可添加非签署方的内部或外部人员作为合同关注方，使其能接收合同进度通知并查看最终合同，但无权签署。 | 否 |
| **意愿认证** | 多重认证方式 | 发起人可为签署方独立设置签署时的验证方式，包括人脸识别、签署密码、短信验证码、指纹/面容ID等，以平衡安全与便捷。 | 否 |
| **签署体验** | 全场景签署 | 支持在PC网页、H5、微信小程序内无缝完成签署。提供统一的"待我处理"中心，方便用户快速找到待办合同。 | 否 |
| | 批量签署 | 对于同一签署人有多份待签合同时，支持一键批量签署。只需一次身份验证，即可完成所有合同的签署操作。 | 否 |
| | 拒签与撤销 | 签署方可填写理由拒签合同，流程即告终止。发起方在合同未全部完成前可随时撤销。 | 否 |
| | 一码多签 | 针对标准协议(如入职登记表)，可生成一个固定的签署二维码。任何人扫码后即可发起一份新协议并完成签署，适用于一对多场景。 | 否 |
| **特色签约方案** | 视频会议签 | 作为腾讯会议/飞书的插件，可在会议中实时投屏合同并邀请参会方扫码签约，实现"边谈边签"。 | 否 |
| | 战略会议签 | 专为线下签约仪式设计。支持自定义签约背景，签约代表在Pad上签名，笔迹实时投射到大屏幕，营造隆重仪式感。 | 否 |

### 2.3 归档与管理 (Archiving & Management)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **合同管理** | 智能归档与分类 | 已完成合同自动归档至档案库。支持自定义合同类型和标签，并可设置规则，将符合条件的合同自动分类。 | **是** (AI自动提取标签/分类) |
| | 多维度智能检索 | 支持关键字全文检索。提供按合同类型、状态、金额、签署方、日期等多维度筛选。 | **是** (支持自然语言搜索) |
| | 合同全链路视图 | 完整记录合同从创建到归档的所有操作日志(人、事、时、地)，形成不可篡改的证据链。支持关联主合同与补充协议。 | 否 |
| **合同后处理** | 下载与打印 | 支持下载带有防伪水印和完整数字签名的PDF文件。 | 否 |
| | 合同验签 | 提供独立的验签功能入口，上传已签署的PDF文件，系统可验证其数字签名是否有效、内容是否自签署后被篡改。 | 否 |
| | 合同对比 | 上传两份合同文件，系统可智能对比并高亮显示文本内容的差异之处。 | **是** (语义对比，分析条款变更风险) |
| | 合同作废/解除 | 提供规范的线上作废/解除流程。通过签署《作废/解除协议》的方式使原合同失效，流程需经审批。 | 否 |
| | 履约管理 | 用户可在合同中设置关键履约节点(如付款日、交付日)，系统会在到期前通过消息中心自动发送提醒。 | **是** (AI自动识别履约节点) |
| **安全与合规** | 证据链报告 | 可一键申请由权威CA机构出具的、具有法律效力的《电子文件签署报告》，包含所有操作日志、时间戳、区块链存证等信息。 | 否 |
| | 区块链存证 | 可选将合同签署过程中的关键信息(文件哈希、签署人身份、时间戳等)同步至权威区块链，增强防篡改和公信力。 | 否 |
| | 国密算法支持 | 平台支持SM2/SM3/SM4国密算法，满足金融、政务等领域对信创环境的安全要求。 | 否 |

---

## 3. 业务支撑系统 (Business Support)

### 3.1 印章管理中心 (Seal Center)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **个人印章/签名** | 手写签名创建 | 提供在线签名板，用户可手写签名，系统支持压感以模拟真实笔迹。可保存多个签名样式。 | 否 |
| | 图片签名上传 | 支持用户上传个人签名图片，系统自动抠图优化。 | **是** (AI智能抠图) |
| **企业印章** | 多类型印章创建 | 支持创建公章、合同章、财务章、人事章、法人章等。支持通过"标准模板生成"或"上传实体印章图片"两种方式。 | **是** (AI智能抠图/辅助真伪识别) |
| **授权与使用** | 印章授权 | 印章管理员可将印章使用权授权给指定员工、部门或角色，并可设定授权期限。 | 否 |
| | 用印申请与审批 | 未获授权的员工需要用印时，可发起"用印申请"，流转至印章管理员或指定审批人进行审批。 | 否 |
| | 用印日志 | 所有印章的使用都会被系统自动记录，形成不可篡改的审计日志，包含使用者、时间、合同、IP地址等信息。 | 否 |
| | 骑缝章支持 | 在配置合同时，可添加骑缝章控件。签署后，该印章将自动均等地分布在合同文件的每一页侧边。 | 否 |

### 3.2 计费与消息 (Billing & Messaging)

| 二级模块 | 三级功能 | 功能说明 | AI辅助 |
| :--- | :--- | :--- | :--- |
| **计费与订单** | 套餐与定价 | 提供多种规格的合同签署套餐包(按份数/按年)。提供短信、实名认证、AI服务等增值资源包。 | 否 |
| | 订单与支付 | 用户可在线下单购买套餐。支持微信支付、支付宝、对公转账等多种支付方式。 | 否 |
| **发票管理** | 发票申请与开具 | 用户可在线提交开票申请，填写信息。系统支持开具电子或纸质的增值税普通/专用发票。 | 否 |
| **消息中心** | 多渠道通知 | 统一管理所有系统通知。通过短信、邮件、微信模板消息、系统内站内信等方式，将各类消息(如待办、状态变更)触达用户。 | 否 |
| **开放平台** | API与SDK | 提供覆盖全业务流程的RESTful API和多语言SDK，支持与企业OA、ERP、CRM等系统深度集成。 | 否 |
| | 嵌入式组件 | 提供标准化的前端组件(Embed SDK)，可嵌入企业自有应用，实现"零跳转"的无缝签署体验。 | 否 |
| | 事件回调(Webhook) | 企业可配置回调URL，当合同状态变化时(如已签署、已完成)，平台会实时向该URL推送事件消息，支持重试和签名验证。 | 否 |
</rewritten_file> # 路浩AI电子签 - 产品功能列表（V3.0）

## 文档引言

本文件旨在为“路浩AI电子签”项目提供一份具备行业顶尖水准的、极度详尽的产品功能规格说明书。本文档在深度对标腾讯电子签、e签宝、法大大、上上签、契约锁等国内主流电子签名SaaS产品的基础上，结合AI赋能的创新思路，对产品功能进行全面、细致的梳理与定义。

文档中的每一个功能点都将力求描述其**核心价值**、**主要用户场景**、**关键操作流程**和**预期效果**，以确保产品设计、技术研发、测试及市场推广等各环节都有清晰、统一的理解和依据。

---

## 第一部分：平台核心与基础 (Platform Core & Foundation)

平台核心是支撑整个电子签业务的基石，包含多租户体系、统一账户中心、认证服务、计费与套餐管理等。

### 1. 统一账户中心 (Unified Account Center)

为个人与企业用户提供统一、安全、便捷的账户服务，是所有业务流程的起点。

#### 1.1 个人用户账户体系

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **多渠道注册/登录** | - **手机号+验证码**: 用户输入手机号，获取并回填短信验证码，系统验证通过后完成注册或登录。首次登录即自动创建账户。<br>- **微信一键授权**: 在微信生态内（小程序、公众号），用户点击授权按钮，拉取微信绑定的手机号或UnionID，快速完成注册/登录。<br>- **账号密码登录**: 作为辅助方式，允许已设置密码的用户通过“手机号/邮箱+密码”登录。<br>- **社交账号绑定**: 用户可在个人中心将账户与微信、企业微信等进行绑定或解绑，方便多渠道登录。 | **价值**: 提供灵活、便捷的登录方式，降低用户使用门槛。<br>**场景**: C端用户在小程序/H5签署个人合同；企业员工首次被邀请加入企业。 |
| **统一实名认证** | - **认证流程**: 用户进入实名认证流程 -> 选择认证方式（推荐微信支付认证，或手动输入） -> 如手动输入，则填写“姓名+身份证号” -> 系统调起人脸识别（对接微信人脸核身或公安部CTID） -> 用户按提示完成动作（如读数字、眨眼） -> 认证成功/失败。<br>- **多证件支持**: 除身份证外，需支持港澳居民来往内地通行证、台胞证、护照等，并有相应认证通道。<br>- **认证状态管理**: 认证成功后，用户账户状态变更为“已实名”，并生成唯一的数字身份标识。该状态在平台内全局唯一且通用。 | **价值**: 确保签署主体身份的真实性、合法性，是电子签名有效性的法律基础。<br>**场景**: 首次签署任何具有法律效力的文件前；作为企业法定代表人或管理员进行企业认证前。 |
| **个人账户管理** | - **信息修改**: 用户可查看和修改绑定的手机号、邮箱地址。修改敏感信息（如手机号）时，需通过“原手机验证码+人脸识别”或“人脸识别+新手机验证码”等强验证方式。<br>- **个人更名**: 针对用户因法定程序变更姓名的情况，提供线上更名通道。流程：用户申请 -> 阅读并同意《实名变更协议》 -> 系统提示未完成合同将失效 -> 用户确认 -> 上传户籍管理部门的更名证明 -> 人脸识别验证 -> 平台人工审核 -> 审核通过后更新实名信息，并自动失效旧签名/印章，引导用户重新生成。<br>- **签署密码/生物特征**: 用户可设置、修改、重置6位数字签署密码（需人脸识别验证）。在移动设备上，可授权开启指纹/面容ID支付，作为签署密码的便捷替代方案。<br>- **账号注销**: 用户在确保已退出所有企业、无进行中合同时，可申请注销。流程：申请 -> 风险告知 -> 意愿确认（人脸识别） -> 账号冻结并公示 -> 规定时限后数据脱敏/删除。 | **价值**: 保障用户账户的自主管理权和安全性，应对各类现实变更情况。<br>**场景**: 用户更换手机号；因婚嫁等原因更改姓名；忘记签署密码；决定不再使用平台服务。 |
| **个人签名/印章管理** | - **手写签名创建**: 提供签名板，用户可在线手写签名，系统支持压力感应以模拟真实笔迹。可保存多个签名样式，并设其中一个为默认。<br>- **模板签名生成**: 系统根据用户实名信息，自动生成多种艺术字体（如正楷、行书）的签名供用户选择使用。<br>- **图片印章上传**: 支持用户上传手写签名或私章的图片（白底黑字），系统通过AI图像处理技术自动抠图、去除背景、调整为标准样式。<br>- **统一管理**: 用户可在“我的印章”中查看、启用、停用、删除名下所有签名和印章。 | **价值**: 满足不同用户对签名样式的偏好和需求，提供便捷的签名生成方式。<br>**场景**: 签署合同时，从个人签名库中选择一个合适的签名样式。 |

#### 1.2 企业用户账户体系

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **多通道企业认证** | - **法人授权认证 (推荐)**: 超管（可为非法定代表人）填写企业名称、统一社会信用代码 -> 系统通过工商数据校验 -> 超管通过微信/短信将授权链接发送给法定代表人 -> 法定代表人点击链接，进行人脸识别 -> 认证成功，超管身份确认。<br>- **对公打款认证**: 超管填写企业信息及对公银行账户 -> 平台向该账户打一笔随机小额款项 -> 企业财务查收后，由超管在页面回填准确金额 -> 金额正确则认证通过。<br>- **微信支付商户号授权**: 若企业已开通微信支付商户号，可直接授权拉取已认证的企业信息，快速完成认证。<br>- **上传营业执照+授权书**: 作为补充方式，上传清晰的营业执照照片，系统OCR识别信息，同时下载《超管授权书》，由法定代表人签字并加盖公章后上传，由平台人工审核。 | **价值**: 提供多种认证路径，适应不同企业的组织架构和操作便利性，确保企业主体真实有效。<br>**场景**: 企业首次入驻平台；平台需验证合作企业的真实性。 |
| **超级管理员与法定代表人** | - **超级管理员 (超管)**: 企业在平台的最高权限管理者，负责初始化配置、员工管理、角色授权、购买服务等。首位完成企业认证的员工默认为超管。<br>- **法定代表人**: 根据工商信息自动识别，拥有天然的管理权限，如批准超管变更、企业信息变更等。法人本人也是企业员工，可被赋予其他角色。<br>- **超管变更**: 原超管或法人可发起 -> 选择新超管（必须是已实名员工） -> 法定代表人扫码人脸识别授权 -> 变更成功，新超管收到通知，原超管权限自动降级。若法人无法操作，则需上传加盖公章的变更申请书，人工审核处理。 | **价值**: 明确企业在平台内的权责体系，保障企业资产安全。<br>**场景**: 企业IT负责人作为超管进行日常管理；超管离职，需要进行权限交接。 |
| **企业信息管理** | - **基本信息**: 展示企业名称、统一社会信用代码、认证状态、所属行业等。<br>- **企业名称/法人变更**: 与个人更名类似，需超管或法人发起，上传最新的营业执照，系统通过工商数据核验后，进行人脸识别确认意愿，变更后，原企业印章将失效，并引导重新生成。 | **价值**: 确保企业信息与工商登记信息同步，保障业务合规性。<br>**场景**: 企业完成工商变更后，需在平台同步更新信息。 |

---

下一步，我将继续细化 **第二部分：组织、权限与印章管理 (Organization, Permission & Seal Management)**，并按照您的要求，逐步完成所有文档的V3版本升级。
## 第二部分：组织、权限与印章管理 (Organization, Permission & Seal Management)

为企业提供强大而灵活的内部管理工具，实现对员工、权限和印章的精细化、安全化管控。

### 2.1 组织架构管理 (Organizational Structure)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **部门与层级管理** | - **树状结构**: 支持企业以树状结构创建和管理多层级部门，无限层级，直观反映公司实际组织架构。<br>- **操作**: 超管或有权限的管理员可在PC端通过拖拽方式调整部门顺序、改变部门层级关系。支持新建子部门、编辑部门名称、删除部门（删除时需指定该部门下员工及资产的归属新部门）。 | **价值**: 精准映射企业内部结构，为后续按部门进行权限和数据隔离提供基础。<br>**场景**: 公司新成立部门、组织架构调整、部门合并或撤销。 |
| **员工管理** | - **多种添加方式**: <br>  1. **手动添加**: 逐个输入员工姓名、手机号，并直接分配初始角色。<br>  2. **批量导入**: 下载标准Excel模板，填写员工信息（姓名、手机号、所属部门、角色等）后一次性导入。<br>  3. **邀请码/链接加入**: 生成带有效期和部门归属的邀请二维码或链接，员工通过微信扫码或点击链接，自行填写信息后提交加入申请，待管理员审批。<br>  4. **企业微信/钉钉同步**: 若企业使用企微/钉钉，可授权同步组织架构，一键导入员工信息。<br>- **员工状态**: 员工有“待激活”、“已激活”、“已禁用”、“已离职”等状态。新加入员工为“待激活”，需通过短信或微信通知，点击链接完成个人实名认证后转为“已激活”。<br>- **员工信息维护**: 管理员可查看员工信息、修改其所属部门、调整角色。<br>- **员工离职**: 管理员对某员工执行离职操作 -> 系统提示该员工名下待办合同、资产（如其创建的模板）的处理方式 -> 指定交接人 -> 确认后，该员工账号被禁用，所有待办事项和资产自动转移给交接人。 | **价值**: 提供高效、灵活的员工入职、变动和离职管理，确保业务连续性。<br>**场景**: 新员工入职、员工岗位调动、员工离职交接。 |

### 2.2 角色与权限体系 (RBAC - Role-Based Access Control)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **系统预设角色** | - **内置角色**: 系统预置“超级管理员”、“法定代表人”、“合同管理员”、“印章管理员”、“财务”、“法务”、“业务员”等多种常用角色，并预设了符合其岗位职责的权限组合。<br>- **角色说明**: 每个预设角色都有清晰的权限范围说明，方便企业快速理解和使用。 | **价值**: 开箱即用，满足大部分企业标准化的权限管理需求，降低配置复杂度。<br**场景**: 中小企业快速启用平台，直接为员工分配系统预设角色。 |
| **自定义角色** | - **创建新角色**: 超管可创建新的角色，如“销售总监”、“人事经理”等。<br>- **权限配置**: 为自定义角色从权限池中勾选具体权限。权限池设计需极度精细，覆盖到每个功能的“查看”、“创建”、“编辑”、“删除”、“下载”、“授权”等操作。<br>- **数据权限**: 核心功能。支持为角色配置数据可见范围，分为三个维度：<br>  1. **全公司**: 可查看和管理公司所有相关数据（如所有合同、所有模板）。<br>  2. **本部门及子部门**: 可查看和管理自己所在部门及所有下级子部门的数据。<br>  3. **仅本人**: 只能查看和管理由自己创建或参与的数据。 | **价值**: 极高的灵活性，满足企业个性化、复杂的权责划分需求，实现最小权限原则。<br>**场景**: 集团公司需要设置区域负责人，只管理其所在区域分公司的合同；法务部门需要查看所有合同，但不能发起。 |

### 2.3 企业印章管理 (Seal Management)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **多类型印章创建** | - **印章类型**: 支持创建公章、合同专用章、财务专用章、人事专用章、法定代表人名章、以及自定义业务章（如“质检专用章”）。<br>- **模板印章 (推荐)**: 根据企业认证信息和所选印章类型，系统自动生成符合公安部规范的标准电子印章（圆形、椭圆形可选），并可微调尺寸。<br>- **本地上传 (AI抠图)**: 上传实体印章在白纸上的清晰盖印图片 -> 系统利用AI视觉算法（OCR+图像分割）自动识别印章边缘，去除背景，并进行像素优化，生成高保真电子印章 -> 管理员确认后启用。<br>- **法人名章**: 只能由法定代表人本人，或经其授权的超管创建。系统根据法人实名信息自动生成标准人名章。 | **价值**: 提供合规、便捷的印章生成方式，同时兼顾对现有实体印章的数字化需求。<br>**场景**: 企业首次配置印章；业务发展需要新增特定用途的业务章。 |
| **印章授权与用印** | - **印章授权**: 印章管理员可将印章的使用权授权给指定员工或角色。可设置授权期限（长期或指定时间段）。<br>- **用印申请与审批**: 未获授权的员工在签署需要盖章的合同时，可发起“用印申请” -> 选择所需印章，填写用印事由 -> 申请流转至印章管理员或指定审批人 -> 审批人可在移动端/PC端查看合同并审批 -> 审批通过后，该员工获得本次签署的用印权限。<br>- **用印日志**: 所有印章的使用（谁在、何时、在哪份合同上、使用了哪个印章）都会被系统自动记录，形成不可篡改的用印日志，支持查询和审计。 | **价值**: 严格管控企业印章的使用，防范萝卜章风险，所有操作有据可查。<br>**场景**: 销售人员签署合同，需申请使用“合同专用章”；法务人员审核后批准用印。 |
| **印章生命周期管理** | - **启用/停用**: 管理员可随时启用或停用某个印章。停用后的印章无法在新的合同中使用，但已用印的合同不受影响。<br>- **删除**: 印章被删除后不可恢复。为防止误操作，删除前系统会校验该印章是否关联了有效的合同模板或审批流，若有关联则禁止删除。<br>- **变更记录**: 印章的所有操作，包括创建、授权、停用、修改信息等，都会被详细记录。 | **价值**: 对印章进行全生命周期的管理，确保操作安全可追溯。<br>**场景**: 公司某业务印章废弃不用，管理员将其停用或删除。 |

---
## 第三部分：合同全生命周期管理 (Contract Lifecycle Management)

覆盖合同从拟定、签署、归档到履约、检索、处置的全过程，并以AI能力赋能，提升效率与风控水平。

### 3.1 合同拟定与发起 (Drafting & Initiation)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **多源合同发起** | - **模板发起**: 从企业模板库或官方模板库选择模板，填写预设信息后快速发起。<br>- **本地文件发起**: 支持拖拽或选择本地文件（PDF, Word, Excel, 图片等），系统自动转换为统一的PDF格式进行处理。<br>- **合同草稿箱**: 任何未完成的发起流程都可以保存至草稿箱，方便后续继续编辑和发起。 | **价值**: 提供灵活的发起方式，适应不同来源和准备程度的合同文件。<br>**场景**: 使用公司法务制定的标准模板发起销售合同；将客户发来的Word版合同直接上传并发起签署。 |
| **AI合同生成 (核心)** | - **智能问答生成**: 用户通过与AI助手进行多轮对话，回答关于合同类型、甲乙方、标的、金额、期限等关键问题，AI根据知识库和预设模板动态生成一份完整的合同初稿。<br>- **条款库选用**: 在线编辑合同时，AI可根据上下文推荐标准条款（如保密条款、争议解决条款），用户可一键插入。<br>- **风险审查与提示**: 在线编辑或上传合同时，AI可实时审查合同文本，识别潜在风险点（如缺少违约责任、管辖约定不清、权利义务不对等），并以高亮和批注形式向用户提示。 | **价值**: 极大降低合同起草门槛和时间成本，并提供初步的法务风控能力。<br>**场景**: 业务人员不熟悉合同条款，通过AI问答快速生成一份相对规范的采购合同；法务人员利用AI审查功能，快速定位外部合同的风险点。 |
| **在线协同编辑** | - **多人实时编辑**: 类似腾讯文档/Google Docs，支持多人同时在线编辑一份合同草稿，所有修改实时同步，并显示不同编辑者的光标。<br>- **版本历史与追溯**: 自动保存所有历史版本，可随时查看、比较不同版本间的差异，并可一键恢复到任一历史版本。<br>- **评论与批注**: 任何协作者都可以对具体条款进行评论和@相关人员，方便团队内部沟通和审核。 | **价值**: 解决传统邮件来回修改合同版本的混乱与低效，实现高效的合同协同定稿。<br>**场景**: 法务、业务、财务等多部门人员需要共同审核一份重要合同。 |
| **动态填写控件** | - **丰富控件类型**: 提供单行文本、多行文本、数字、日期、勾选框、选择器、附件、图片、地址等多种控件。<br>- **智能识别添加**: 上传合同时，系统可智能识别文件中的下划线、方框等，并推荐添加合适的填写控件。<br>- **控件属性设置**: 可为每个控件设置填写方、是否必填、填写提示、数据格式校验（如手机号、邮箱格式）等。 | **价值**: 将非结构化的合同文本结构化，确保签署方按要求填写必要信息，避免遗漏。<br>**场景**: 劳动合同中，需要员工填写身份证号、家庭住址、紧急联系人等信息。 |

### 3.2 签署流程配置与执行 (Signing Process & Execution)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **灵活的签署流程** | - **签署方角色**: 可定义签署方角色（如甲方、乙方、丙方），并将控件分配给指定角色。<br>- **签署顺序**: 支持无序签署（所有签署方同时收到通知）、顺序签署（按预设顺序依次签署）、混合签署（部分并行，部分串行）。支持通过拖拽调整顺序。<br>- **自动签署 (本方)**: 可配置本企业在满足特定条件（如其他方签署完毕）后，使用预设印章自动完成签署，无需人工操作。<br>- **抄送与关注方**: 可添加非签署方的企业内部或外部人员作为合同关注方，使其可以接收合同进度通知并查看最终合同，但无权签署。 | **价值**: 适应各种复杂的签约场景，实现流程自动化，提升流转效率。<br**场景**: 多方合作协议需要按甲乙丙顺序签署；大量标准协议在对方签署后，公司法务印章自动盖章。 |
| **多重意愿认证** | - **认证方式配置**: 发起人可为每位签署方独立设置签署时的验证方式，以平衡安全与便捷。<br>- **认证方式**: <br>  1. **强认证 (推荐)**: 人脸识别（法律效力最高）。<br>  2. **标准认证**: 签署密码。<br>  3. **便捷认证**: 短信验证码、指纹/面容ID。<br>- **组合认证**: 可设置需两种或以上方式组合验证，如“签署密码+人脸识别”。 | **价值**: 提供多层级的安全保障，确保签署意愿的真实性，满足不同风险等级合同的需求。<br>**场景**: 重大金额合同强制要求人脸识别；内部审批文件可使用签署密码快速完成。 |
| **全场景签署体验** | - **多端支持**: 支持在PC网页端、H5移动端、微信小程序内无缝完成签署。<br>- **批量签署**: 对于同一签署人有多份待签合同时，支持一键批量签署，只需一次身份验证即可完成所有合同的签署。<br>- **拒签与撤销**: 签署方可填写理由拒签合同，流程终止。发起方在合同未全部完成前可随时撤销。<br>- **转交他人**: 企业经办人若无权或不便处理，可将待办合同安全地转交给公司内其他有权限的同事。 | **价值**: 优化用户签署体验，无论何时何地都能高效、便捷地完成签署。<br>**场景**: 销售总监在出差途中，通过手机小程序批量签署当日的多份销售合同。 |

### 3.3 合同归档与管理 (Archiving & Management)

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **智能归档与分类** | - **自动归档**: 所有已完成签署的合同自动归档至合同管理中心，形成数字档案库。<br>- **合同类型管理**: 管理员可自定义合同类型（如销售合同、采购合同、劳动合同），并为合同打上标签。<br>- **自动分类**: 可设置规则，如“合同名称包含‘采购’的自动归类为采购合同”。 | **价值**: 变无序为有序，建立结构化的合同档案库，便于管理和查找。<br>**场景**: 公司需要按季度统计所有销售类合同的总金额。 |
| **多维度智能检索** | - **关键字检索**: 支持全文检索，输入任意关键字可搜索合同正文、附件、标题、签署方等信息。<br>- **高级筛选**: 提供多维度筛选条件，如合同类型、合同状态、签署方、合同金额范围、签署日期范围等。<br>- **自定义报表**: 可根据筛选条件生成合同数据报表，并支持导出为Excel。 | **价值**: 在海量合同中快速、精准地定位到所需合同，提供数据洞察。<br>**场景**: 法务需要查找所有与“XX公司”在近一年内签订的、金额超过10万元的合同。 |
| **合同全链路视图** | - **操作日志**: 完整记录一份合同从创建、修改、发起、每次查看、签署、下载等所有操作的时间、操作人、IP地址等信息，形成完整的证据链。<br>- **关联文件**: 支持将一份主合同与其他附件、补充协议、解除协议等进行关联，形成合同簇，方便统一查看。 | **价值**: 提供完整的审计追踪能力，增强合同的证据力。<br>**场景**: 发生合同纠纷时，可一键导出包含所有操作日志的证据报告。 |
| **合同后处理** | - **合同下载/打印**: 支持下载带有防伪水印和完整数字签名的PDF文件。<br>- **申请出证**: 可一键申请由CA机构或公证处出具的、具有法律效力的《电子文件签署报告》或《公证处核验报告》。<br>- **合同作废/解除**: 提供标准化的解除协议模板，通过线上签署解除协议的方式，使原合同状态变更为“已解除”。 | **价值**: 满足合同的司法、审计及业务终止等后续处理需求。<br>**场景**: 需向法院提交证据，在线申请合同出证报告；合作终止，双方在线签署解除协议。 |

---
## 第四部分：高级功能与集成 (Advanced Features & Integration)

提供超越标准签约流程的增值功能和强大的开放能力，满足大型企业、复杂业务场景和深度系统集成的需求。

### 4.1 集团企业解决方案

为拥有多个分子公司或分支机构的集团型企业提供统一管控平台。

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **集团组织构建** | - **创建集团**: 任何一个已认证的企业均可升级为“集团主企业”，并创建一个虚拟的“集团组织”。<br>- **邀请成员**: 主企业可生成邀请链接或二维码，邀请其他已认证的企业作为“成员子企业”加入集团。<br>- **授权加入**: 子企业的超管或法人点击邀请链接，确认主企业对其的管理权限范围（如合同管理、印章管理等）后，扫码授权即可加入。 | **价值**: 建立一个统一的、跨企业的管理视图，实现总对总的管控。<br>**场景**: 大型控股集团需要统一管理旗下所有子公司的合同签署事宜。 |
| **集中管控** | - **合同统一管理**: 集团管理员可根据授权，查看、管理所有成员子企业的合同，进行统一的查询、统计和审计。<br>- **印章统一管理**: 集团管理员可代子企业创建和管理印章，并可跨企业为员工授权印章。<br>- **模板统一分享**: 主企业可将标准合同模板一键分享给所有或指定的子企业使用，确保集团范围内合同范本的统一与合规。<br>- **资源共享**: 集团主企业购买的合同套餐包、短信包等资源，可设置为集团内所有成员企业共享，统一结算，降低采购成本。 | **价值**: 提升集团总部的管控力度，降低合规风险，实现资源集约化利用。<br>**场景**: 集团法务部需要审计所有子公司的对外销售合同；集团统一采购合同套餐，分发给各子公司使用。 |

### 4.2 场景化签约方案

针对特定业务场景提供深度优化的、开箱即用的解决方案。

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **一码多签** | - **生成签署码**: 针对某个特定模板（通常是签署方只有一方为动态的，如入职登记表、活动报名表），生成一个唯一的签署二维码。<br>- **扫码签署**: 任何人通过微信扫描该二维码，即可拉起一份基于该模板的新合同，并作为签署方自行填写信息并完成签署。<br>- **名单限制 (可选)**: 可上传一份包含姓名、手机号的白名单，只有名单内的人员扫码才能签署。<br>- **数量与有效期**: 可设置该签署码的总可用次数和有效期。 | **价值**: 极大简化批量、一对多协议的签署流程，无需逐一发起。<br>**场景**: HR部门在校招时，让所有新员工扫码签署《入职信息登记表》；市场部门举办活动，让所有参会者扫码签署《活动安全须知》。 |
| **视频会议签** | - **会议应用集成**: 作为腾讯会议、飞书会议等的原生应用集成。会议主持人可在会议中直接打开电子签应用。<br>- **实时投屏与讲解**: 主持人选择合同后，可将合同内容实时投屏给所有参会方，并进行讲解。<br>- **扫码签约**: 讲解无误后，投屏画面上会显示签署二维码，各参会方使用手机微信扫码，即可在自己的手机上完成签署操作。<br>- **过程录制**: 结合会议的录屏功能，可将整个合同讲解和签署过程录制下来，作为辅助证据。 | **价值**: 在远程商务谈判、在线招投标等场景下，实现“边谈边签”，快速锁定交易。<br>**场景**: 销售与异地客户通过视频会议敲定合同细节后，立即在线完成签约，无需等待快递。 |
| **战略会议签** | - **仪式感签约**: 专为线下或线上的大型签约仪式设计。支持自定义签约背景（如双方公司Logo、签约主题）。<br>- **Pad签名与大屏同步**: 签约代表在专用的iPad上进行手写签名，其笔迹会实时、流畅地投射到现场的大屏幕上，营造隆重的签约仪式感。<br>- **多轮签约**: 支持多组签约方分批次上台进行签约，由现场导播控制切换。 | **价值**: 提升签约的仪式感和品牌宣传效果，适用于发布会、战略合作等重要场合。<br>**场景**: 两家公司举办战略合作发布会，双方CEO在舞台上通过大屏幕同步签署合作协议。 |

### 4.3 开放平台与API/SDK集成

提供全面的API接口和开发者工具，支持与企业现有业务系统无缝集成。

| 功能点 | 详细描述与流程 | 用户价值与场景 |
| :--- | :--- | :--- |
| **全功能API覆盖** | - **接口范围**: 提供覆盖账户、认证、组织、印章、模板、合同发起、签署、下载、证据链等全业务流程的RESTful API。<br>- **文档与调试**: 提供清晰、详尽的在线API文档、各语言（Java, Go, Python, PHP, .NET等）的SDK，以及在线API调试工具。 | **价值**: 将电子签能力深度嵌入到企业自身的业务流程中，实现数据和操作的闭环。<br>**场景**: 将电子签集成到企业的OA、ERP、CRM、HRM等系统中。 |
| **嵌入式组件 (Embed SDK)** | - **前端组件**: 提供标准化的前端组件，可以嵌入到企业自有的Web应用或H5页面中。<br>- **功能**: 可实现如“在线合同预览”、“在线签署”、“模板填写”等功能，用户无需跳出企业自有系统即可完成操作。<br>- **样式可定制**: 支持一定程度的UI样式定制，以匹配企业应用的视觉风格。 | **价值**: 提供与企业自有系统融为一体的无缝用户体验。<br>**场景**: 在企业的HR系统中，员工点击“签署劳动合同”按钮，直接在系统内弹出合同页面进行签署，而非跳转到电子签官网。 |
| **事件回调 (Webhook)** | - **实时通知**: 企业可配置一个回调URL，当合同状态发生变化时（如一方已签署、合同已完成、已拒签等），电子签平台会实时向该URL推送一个包含事件详情的HTTP请求。<br>- **可靠机制**: 支持消息重试和签名验证机制，确保回调通知的可靠性和安全性。 | **价值**: 实现业务系统的实时联动，自动触发后续业务流程。<br>**场景**: 合同签署完成后，自动触发Webhook通知企业的ERP系统，ERP系统自动将合同状态更新为“已生效”并开始排产。 |

---# 路浩AI电子签 - 产品框架和架构设计（V3.0）

## 1. 产品愿景与战略定位

### 1.1 产品愿景
成为中国领先的、以AI驱动的智能合同全生命周期管理平台，让合同签署与管理从事务性工作，升级为企业的数据资产与智能决策依据。

### 1.2 战略定位
**“路浩AI电子签”** 不仅仅是一个电子签名工具，而是一个集**合规签署**、**智能管理**与**业务赋能**于一体的企业级SaaS解决方案。我们以安全合规的电子签名为基础，深度融合AI能力，切入合同管理的每一个环节，致力于成为企业数字化转型中不可或缺的一环。

- **市场定位**: 主攻对合同管理效率、合规性及智能化有较高要求的中大型企业，同时为小微企业和个人提供标准化的、高性价比的优质服务。
- **差异化优势**:
  - **AI原生**: 将AI能力作为产品的核心驱动力，而非辅助功能，尤其在合同智能生成、审查、归档和数据洞察方面建立壁垒。
  - **开放生态**: 提供强大的API和集成能力，轻松融入企业现有业务流，成为连接器而非信息孤岛。
  - **极致安全**: 采用国密算法、区块链存证、多副本分布式存储等金融级安全标准，保障企业核心数据资产安全。

### 1.3 目标用户画像 (Target Persona)

| 用户群体 | 核心诉求 (Pains & Gains) | 典型场景 |
| :--- | :--- | :--- |
| **法务/合规负责人** | **痛点**: 合同审查工作量大、风险点易遗漏、线下盖章流程繁琐、合同归档查找困难。<br>**期望**: 提升审查效率、标准化合同模板、固化证据链、便捷的审计与检索。 | - 使用AI审查外部合同，快速识别不利条款。<br>- 制定公司标准合同模板，并分享给业务部门使用。<br>- 对即将到期的重要合同进行续约或风险预警。 |
| **销售/业务负责人** | **痛点**: 签约周期长影响业绩、合同版本混乱、客户异地签署不便。<br>**期望**: 快速成交、简化签约流程、随时随地移动签署。 | - 通过视频会议签，在远程会议中锁定客户，当场签约。<br>- 使用手机小程序，让客户在几分钟内完成合同签署。<br>- 批量向数百个渠道商发起年度合作协议。 |
| **HR/人事负责人** | **痛点**: 入职季劳动合同签署量巨大、员工档案管理复杂、离职交接流程不清。<br>**期望**: 批量处理人事合同、员工档案电子化、简化入离职手续。 | - 使用“一码多签”功能，让新员工批量扫码签署劳动合同和保密协议。<br>- 员工的电子劳动合同自动归档至其个人电子档案下。<br>- 员工离职时，一键完成相关协议的签署和文件交接。 |
| **企业决策者 (CEO/CFO)** | **痛点**: 对公司整体合同风险敞口不明、合同履约情况难以追踪、业务数据分散在合同中无法利用。<br>**期望**: 数据驱动决策、全局风险监控、合同数据价值挖掘。 | - 通过数据驾驶舱，查看公司本季度销售合同总金额、回款进度等核心指标。<br>- 设定预警规则，当高风险合同数量超过阈值时自动告警。<br>- 分析合同数据，优化采购成本和销售策略。 |

## 2. 产品整体功能架构 (Functional Architecture)

产品架构采用分层设计，确保各层职责清晰，易于扩展和维护。

```mermaid
graph TD
    subgraph Layer_Presentation [表示层 - 多端触达]
        direction LR
        App_PC[PC Web管理端]
        App_H5[H5/移动端]
        App_MiniApp[微信/企微小程序]
        App_Meeting[会议插件 (腾讯会议/飞书)]
        App_Embed[嵌入式SDK]
    end

    subgraph Layer_Business [业务服务层 - 核心能力]
        direction TB
        subgraph Service_Core [核心签约域]
            direction LR
            S1[合同生命周期管理]
            S2[模板中心]
            S3[印章中心]
        end
        subgraph Service_Mgmt [企业管理域]
            direction LR
            S4[统一账户与认证]
            S5[组织与权限(RBAC)]
            S6[集团管控]
        end
        subgraph Service_AI [AI赋能域]
            direction LR
            S7[智能合同生成与审查]
            S8[智能归档与检索]
            S9[智能用印分析]
        end
    end

    subgraph Layer_Platform [平台支撑层 - 中台能力]
        direction TB
        P1[开放平台 (API Gateway)]
        P2[计费与订单中心]
        P3[消息中心 (短信/邮件/Webhook)]
        P4[数据智能平台 (BI/报表)]
    end

    subgraph Layer_Infrastructure [基础设施层 - 技术底座]
        direction LR
        I1[微服务框架 (Go-Kratos)]
        I2[数据存储 (MySQL/TiDB, Redis, MinIO)]
        I3[消息队列 (Kafka/Pulsar)]
        I4[搜索引擎 (Elasticsearch)]
        I5[AI基础设施 (模型服务/向量数据库)]
        I6[安全与合规 (CA/TSA/区块链)]
    end

    Layer_Presentation --> Layer_Business
    Layer_Business --> Layer_Platform
    Layer_Platform --> Layer_Infrastructure
```

## 3. 产品版本规划 (Roadmap V3)

### V1.0 - “坚实核心”
- **目标**: 打造业界领先、安全合规的电子签名核心能力，覆盖主流签署场景。
- **关键特性**:
  - **账户与认证**: 完善的个人与企业多通道认证。
  - **合同签署**: 支持多源发起、灵活的签署流程、多重意愿认证。
  - **印章管理**: 标准的模板印章与上传印章、基础的授权与用印日志。
  - **基础管理**: 基础的合同归档、检索、下载。
  - **API**: 提供核心签署流程的API。

### V2.0 - “企业赋能”
- **目标**: 强化企业级管理能力，深度融入企业业务流程。
- **关键特性**:
  - **组织权限**: 完善的多层级组织架构与精细化RBAC权限体系。
  - **高级流程**: 引入审批流（用印审批、合同审批）、自动签署、批量签署。
  - **场景化方案**: 成熟的“一码多签”、“视频会议签”解决方案。
  - **集成能力**: 推出嵌入式SDK，完善Webhook机制。
  - **集团管控**: 发布集团企业解决方案。

### V3.0 - “智能驱动”
- **目标**: 全面融入AI能力，实现从“工具”到“智能平台”的跃迁。
- **关键特性**:
  - **AI合同生成**: 上线智能问答式合同生成、条款库推荐功能。
  - **AI合同审查**: 提供合同文本的智能风险识别与提示。
  - **AI归档检索**: 实现合同内容的自动标签、归类，并支持自然语言搜索合同。
  - **数据洞察**: 推出合同数据驾驶舱，提供多维度的数据分析与报表。
  - **生态互联**: 与更多业务系统（如CRM、SRM）进行深度集成，打通数据链路。# 路浩AI电子签 - 产品模块功能和链路（V3.0）

## 1. 账户与认证中心 (Account & AuthN/AuthZ Center)

**服务名: `account-service`**

该服务是整个平台的用户身份和权限管理中枢，采用微服务架构，独立部署。

### 1.1 个人用户体系

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `Register(phone, code)` | 1. 校验验证码是否正确。<br>2. 查询手机号是否已存在，若存在则执行登录逻辑，若不存在则创建新用户记录。<br>3. 初始化用户基础信息，状态为“未实名”。<br>4. 生成JWT Token返回给客户端。 | **-> 短信服务**: 发送/校验验证码。<br>**<-> 用户DB**: 读写`users`表。 |
| `KYC(userId, name, idCard, faceData)` | 1. 根据`userId`查找用户。<br>2. 调用第三方实名认证服务接口，传入姓名、身份证号、人脸数据。<br>3. 接收认证结果，若成功，则更新`users`表中的实名状态、姓名、身份证号等字段。<br>4. 为用户异步申请并绑定一个长期的个人数字证书。 | **-> 第三方认证服务**: 对接公安部/微信支付等实名认证接口。<br>**-> CA服务**: 异步申请个人数字证书。<br>**<-> 用户DB**: 更新`users`表。 |
| `UpdateMobile(userId, oldCode, newPhone, newCode)` | 1. 校验旧手机验证码或当前会话的人脸识别结果。<br>2. 校验新手机验证码。<br>3. 更新`users`表中的手机号字段。 | **-> 短信服务**: 校验验证码。<br>**<-> 用户DB**: 更新`users`表。 |

### 1.2 企业用户与RBAC体系

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `CreateEnterprise(userId, enterpriseInfo, authType)` | 1. 校验`userId`是否已实名。<br>2. 根据`authType`（认证类型）进入不同分支：<br>   - **法人授权**: 调用工商信息接口核验企业信息，生成授权链接，通过**消息中心**通知法人。<br>   - **对公打款**: 记录银行账户信息，生成待打款任务。<br>3. 创建企业记录，并将当前`userId`设为超管。 | **-> 第三方工商服务**: 核验企业信息。<br>**-> 消息中心**: 发送法人授权通知。<br>**<-> 企业DB**: 写入`enterprises`表、`enterprise_members`表。 |
| `AddEmployee(enterpriseId, employees)` | 1. 批量处理员工列表。<br>2. 对每个员工，检查手机号是否已注册平台。若未注册，创建个人用户记录。<br>3. 在`enterprise_members`表中插入员工记录，关联`enterpriseId`和`userId`，状态为“待激活”。<br>4. 通过**消息中心**发送激活邀请。 | **<-> 用户DB**: 查询/创建`users`表。<br>**<-> 企业DB**: 写入`enterprise_members`表。<br>**-> 消息中心**: 发送激活短信/邮件。 |
| `CheckPermission(userId, enterpriseId, permissionCode)` | 1. 根据`userId`和`enterpriseId`查询该员工所属的所有角色。<br>2. 查询这些角色关联的所有权限码。<br>3. 判断`permissionCode`是否存在于权限集合中，返回`true/false`。 | **<-> 企业DB**: 联表查询`enterprise_members`, `roles`, `role_permissions`。 |

## 2. 合同与签署中心 (Contract & Signing Center)

**服务名: `contract-service`**

负责合同全生命周期的核心业务逻辑。

### 2.1 合同拟定与发起

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `CreateFlowByFile(initiator, file, flowInfo)` | 1. 调用**文档中心**的`Upload(file)`接口，获取`fileId`。<br>2. 解析`flowInfo`中的签署方、控件、流程配置等信息。<br>3. 在`contracts`表中创建一条新合同记录，状态为“DRAFT”。<br>4. 将控件信息与签署方角色关联，存入`contract_components`表。<br>5. 若无审批，则将合同状态更新为“SIGNING”，并调用**消息中心**通知首个签署人。 | **-> 文档中心**: 上传并转换文件。<br>**<-> 合同DB**: 写入`contracts`, `contract_approvers`, `contract_components`表。<br>**-> 消息中心**: 发送签署通知。 |
| `GenerateContractByAI(userId, conversationHistory)` | 1. 将对话历史`conversationHistory`组装成一个结构化的Prompt。<br>2. 调用**AI赋能域**的`GenerateContract`接口。<br>3. 接收返回的合同文本和结构化数据。<br>4. 创建一份合同草稿，并将AI生成的文本填入。 | **-> AI赋能域**: 调用大模型生成服务。<br>**<-> 合同DB**: 创建合同草稿。 |

### 2.2 签署执行

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `ExecuteSign(userId, contractId, signAreaId, authData)` | 1. 权限校验：检查`userId`是否是当前合同的待签署人。<br>2. 意愿认证：调用**账户中心**的`VerifySignaturePassword`或第三方人脸识别服务，验证`authData`。<br>3. 获取待签署的合同PDF文件Hash。<br>4. 调用**安全与合规中心**的`ApplyDigitalSignature`接口，传入文件Hash、用户信息、签名/印章ID、时间戳等，获取含数字签名的PDF新版本。<br>5. 更新`contract_approvers`表中该签署方的状态为“SIGNED”。<br>6. 检查是否所有人都已签署，若是，则触发合同完成逻辑；否则，通知下一位签署人。 | **-> 账户中心**: 验证签署密码。<br>**-> 第三方认证服务**: 人脸识别。<br>**-> 安全与合规中心**: 申请数字签名和时间戳。<br>**<-> 合同DB**: 更新合同及签署方状态。<br>**-> 消息中心**: 通知下一签署人或所有方合同完成。 |

## 3. AI赋能域 (AI-Powered Domain)

**服务名: `ai-service`**

提供各类AI能力，作为业务服务的上游。

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `GenerateContract(prompt)` | 1. 对`prompt`进行预处理和安全过滤。<br>2. 调用内部或外部大模型（如GPT-4, ERNIE-4.0）的API。<br>3. 对LLM返回的结果进行后处理，提取结构化信息和合同文本，并进行格式化。<br>4. 返回处理后的结果。 | **-> 大模型服务**: 调用LLM接口。<br>**<-> 知识库/向量数据库**: 可能进行RAG检索增强。 |
| `ReviewContract(contractText)` | 1. 将合同文本分块。<br>2. 调用内置的风险规则库和分类模型，进行初步风险识别。<br>3. 对关键条款（如管辖、违约、赔偿）调用大模型进行深度语义分析。<br>4. 汇总所有风险点，并标记其在原文中的位置，返回给调用方。 | **-> 大模型服务**: 语义分析。<br>**<-> 风险规则库**: 规则匹配。 |
| `ExtractSealFromImage(image)` | 1. 对上传的印章图片进行预处理（灰度化、二值化）。<br>2. 使用图像分割模型（如U-Net）或传统图像处理算法（如边缘检测、霍夫变换）来定位印章区域。<br>3. 对识别出的区域进行背景去除和像素修复。<br>4. 返回透明背景的PNG格式印章图片。 | **-> 图像处理/AI模型**: 调用抠图算法。 |# 路浩AI电子签 - 产品用户和核心流程（V3.0）

## 1. 核心用户角色与场景分析

| 角色分类 | 角色名称 | 核心职责与典型场景 |
| :--- | :--- | :--- |
| **个人用户** | 个人签约方 | **职责**: 作为独立的法律主体，完成个人事务的合同签署。<br>**场景**: 签署租房合同、借条、劳动合同、收据、设计师服务协议等。 |
| **企业内部** | 超级管理员 | **职责**: 企业平台的最高权限所有者，负责企业认证、组织架构搭建、核心权限分配、购买服务、配置全局安全策略。<br>**场景**: 初始化公司电子签平台；为新设立的法务部配置角色和权限；处理最紧急的权限问题。 |
| | 系统/IT管理员 | **职责**: 由超管指定，负责日常的员工账号管理、部门调整、角色成员分配、API密钥管理等。<br>**场景**: 为新入职员工创建账号；将离职员工账号禁用并交接其工作；为业务系统集成提供API密钥。 |
| | 业务管理员<br>(如:合同/印章/模板管理员) | **职责**: 负责特定业务领域的管理。合同管理员管理公司所有合同；印章管理员管理印章的创建、授权和用印审批；模板管理员负责标准合同模板的制作与维护。<br>**场景**: 印章管理员审批业务部门的用印申请；模板管理员将法务部审核通过的最新版销售合同制作成模板。 |
| | 部门负责人 | **职责**: 管理本部门及下属部门的业务，通常拥有本部门的数据查看权限和业务审批权限。<br>**场景**: 销售总监查看本部门所有销售合同的签署进度；研发总监审批下属员工的采购申请。 |
| | 普通员工/业务员 | **职责**: 企业内业务的执行者，在被授予的权限内发起合同、使用印章、处理自己的待办事项。<br>**场景**: 销售人员使用公司模板发起一份销售合同；采购人员提交一份采购合同的用印申请。 |
| **外部关系方** | 企业签约方 | **职责**: 代表其所属企业完成合同的签署。<br>**场景**: 供应商公司的经办人，登录平台签署由我方发起的采购合同。 |
| | 审批人 | **职责**: 在特定流程节点上进行审核，决定流程是否继续。<br>**场景**: 合同金额超过10万元时，需要财务总监作为审批人进行审批，通过后方可正式发起。 |
| | 关注方/抄送人 | **职责**: 无需操作，但需要知晓合同进展和结果。<br>**场景**: 一份重要合同在签署时，需要将公司CEO添加为关注方，使其能收到合同完成的通知。 |

## 2. 核心业务流程图 (Mermaid)

### 2.1 企业入驻与初始化配置流程

```mermaid
graph TD
    A[企业代表访问官网/小程序] --> B[使用个人已实名账号注册/登录];
    B --> C[选择“创建企业”];
    C --> D{选择企业认证方式};
    D -- 法人授权(推荐) --> E[填写企业信息, 发送授权链接给法人];
    E --> F[法人扫码人脸识别];
    D -- 对公打款 --> G[填写对公账户, 等待平台打款并回填金额];
    F --> H{认证成功};
    G --> H;
    H --> I[账号成为“超级管理员”];
    I --> J[进入企业管理后台];
    J --> K[**Step 1: 组织架构搭建**<br>创建部门, 批量导入/邀请员工];
    K --> L[**Step 2: 角色与权限分配**<br>为部门/员工分配系统角色或自定义角色];
    L --> M[**Step 3: 印章创建与授权**<br>创建企业公章/合同章, 并授权给相应角色或员工];
    M --> N[**Step 4: 模板制作(可选)**<br>将常用合同制作成标准模板];
    N --> O[初始化完成, 企业可正式使用];
```

### 2.2 AI辅助合同拟定与发起流程

```mermaid
sequenceDiagram
    participant User as 业务员
    participant AI_Assistant as AI助手
    participant Platform as 路浩AI电子签平台
    participant Reviewer as 法务/上级

    User->>Platform: 登录系统, 选择“AI生成合同”
    Platform->>AI_Assistant: 启动合同生成会话
    AI_Assistant-->>User: 你好, 我是合同助手, 请问您想生成什么类型的合同？
    User->>AI_Assistant: 我需要一份软件采购合同
    AI_Assistant-->>User: 好的, 请告诉我采购方和供应方信息、采购标的、金额、交付日期...
    User->>AI_Assistant: (提供关键信息)
    AI_Assistant->>Platform: 根据用户输入和知识库, 生成合同初稿
    Platform-->>User: 展示合同初稿, 并高亮AI建议的风险点
    User->>Platform: 在线编辑修改部分条款
    User->>Platform: 选择“发起审批”
    Platform->>Reviewer: 发送审批通知 (待办中心/企微/钉钉)
    Reviewer->>Platform: 登录并查看合同, 提出修改意见或直接批准
    alt 审批通过
        Platform-->>User: 通知: 合同已审批通过, 可以发起
        User->>Platform: 填写签署方信息, 确认并发起合同
        Platform->>Signers: 向各签署方发送签署通知
    else 审批驳回
        Platform-->>User: 通知: 合同被驳回, 请根据意见修改
    end
```

### 2.3 多方顺序签署与自动签流程

```mermaid
sequenceDiagram
    participant Initiator as 发起方 (企业A员工)
    participant SignerB as 签署方B (个人)
    participant SignerC as 签署方C (企业C经办人)
    participant Platform as 路浩AI电子签平台
    participant AutoSign as 企业A自动签服务

    Initiator->>Platform: 发起合同, 设置签署顺序: B -> C -> A(自动签)
    Platform->>SignerB: 发送签署邀请
    SignerB->>Platform: 查看合同, 完成手写签名和人脸识别
    Platform-->>SignerB: 签署完成
    Platform->>SignerC: 发送签署邀请
    SignerC->>Platform: 查看合同, 选择企业印章完成盖章
    Platform-->>SignerC: 签署完成
    Platform->>AutoSign: 触发自动签署条件 (前序节点全部完成)
    AutoSign->>Platform: 使用预设印章, 自动完成企业A的盖章
    Platform->>Platform: 合同所有方签署完毕, 生成最终版PDF并存证
    Platform-->>Initiator: 通知: 合同已完成
    Platform-->>SignerB: 通知: 合同已完成
    Platform-->>SignerC: 通知: 合同已完成
```

### 2.4 集团主企业代子企业发起合同流程

```mermaid
graph TD
    A[集团合同管理员登录主企业账号] --> B[选择“发起合同”];
    B --> C{选择发起方企业};
    C -- 选择“主企业” --> D[按正常流程为“主企业”发起合同];
    C -- 选择“XX子公司” --> E[主企业代子企业发起];
    E --> F[**数据隔离**: 签署方联系人、可选印章、模板库<br>均切换为“XX子公司”的资产];
    F --> G[配置合同内容与签署流程];
    G --> H[发起合同];
    H --> I[合同法律主体为“XX子公司”];
    I --> J[合同数据归档于“XX子公司”名下];
    J --> K[集团管理员在集团视图下可统一查看和管理];# 路浩AI电子签 - 技术架构设计（V3.0）

## 1. 架构设计哲学与原则

- **云原生优先 (Cloud-Native First)**: 全面拥抱容器化、微服务、服务网格(Service Mesh)和声明式API，充分利用云平台的弹性、韧性和可观测性。
- **领域驱动设计 (DDD)**: 以业务领域为核心划分微服务，确保服务边界清晰、高内聚、低耦合，使技术架构与业务架构保持一致。
- **安全左移 (Shift-Left Security)**: 将安全设计贯穿于整个软件开发生命周期（SDLC），从编码、构建、测试到部署的每一个环节都内置安全考量。
- **数据驱动 (Data-Driven)**: 架构设计需支撑数据的采集、处理、分析和应用，使业务运营和产品迭代均由数据驱动。
- **拥抱开源与自主可控**: 积极采用业界领先的开源技术（如Kratos, TiDB, Kafka），同时在核心安全模块（如加密、签名）上，优先考虑国密标准，确保自主可控。

## 2. 整体技术架构

系统采用基于Go-Kratos的微服务架构，通过API Gateway对外提供统一服务，内部服务间通过gRPC进行高效通信。

```mermaid
graph TD
    subgraph User_Layer [用户与客户端]
        direction LR
        Client_Web[PC Web (React)]
        Client_Mobile[移动端 (Uni-app)]
        Client_API[API/SDK (Java/Go/Python)]
    end

    subgraph Gateway_Layer [API网关层]
        GW[API Gateway (Kratos Gateway)]
    end

    subgraph Service_Layer [微服务层 (Go-Kratos)]
        direction TB
        subgraph Biz_Services [业务服务]
            S_Account[account-service<br>账户与认证]
            S_Org[organization-service<br>组织与权限]
            S_Contract[contract-service<br>合同与签署]
            S_Template[template-service<br>模板中心]
            S_Seal[seal-service<br>印章中心]
        end
        subgraph AI_Services [AI服务]
            S_AI_Gen[ai-generation-service<br>合同生成与审查]
            S_AI_OCR[ai-ocr-service<br>智能抠图与识别]
        end
        subgraph Support_Services [支撑服务]
            S_Notify[notification-service<br>消息通知]
            S_Billing[billing-service<br>计费与订单]
        end
    end

    subgraph Data_Layer [数据与中间件层]
        direction LR
        DB_MySQL[**主业务库**<br>MySQL/TiDB Cluster]
        DB_Redis[**缓存**<br>Redis Cluster]
        DB_ES[**搜索引擎**<br>Elasticsearch Cluster]
        DB_Vector[**向量数据库**<br>Milvus/Qdrant]
        MQ_Kafka[**消息队列**<br>Kafka/Pulsar]
        Storage_MinIO[**对象存储**<br>MinIO Cluster]
    end

    subgraph Third_Party_Layer [第三方与安全基础设施]
        direction LR
        TP_CA[CA机构]
        TP_TSA[可信时间戳中心]
        TP_Blockchain[区块链存证节点]
        TP_SMS[短信服务]
        TP_LLM[大语言模型API]
    end

    User_Layer --> GW
    GW --> Biz_Services
    GW --> AI_Services
    Biz_Services -- gRPC --> Biz_Services
    Biz_Services -- gRPC --> AI_Services
    Biz_Services -- gRPC --> Support_Services
    Biz_Services -- Async --> MQ_Kafka
    Support_Services -- Consume --> MQ_Kafka
    AI_Services -- gRPC --> AI_Services
    
    Service_Layer -- 数据读写 --> Data_Layer
    S_Contract -- 安全调用 --> TP_CA & TP_TSA & TP_Blockchain
    S_Notify -- 外部调用 --> TP_SMS
    S_AI_Gen -- 外部调用 --> TP_LLM
```

## 3. 核心技术实现细节

### 3.1 安全加密核心

- **传输层安全**: 全链路强制TLS 1.3，API Gateway终结TLS，内部服务间通信启用mTLS。
- **数据存储加密**:
  - **敏感信息**: 用户身份证号、手机号、银行卡号等在数据库中**必须**使用`AES-256-GCM`或国密`SM4`算法加密存储，密钥由独立的KMS（密钥管理服务）管理。
  - **合同文件**: 存储在MinIO对象存储中的合同文件，采用服务端加密（SSE-S3），由MinIO管理加密密钥。
- **电子签名核心算法**:
  - **摘要算法**: 对合同原文PDF计算摘要，必须使用`SHA-256`或国密`SM3`。
  - **签名算法**: 调用CA机构服务时，使用`RSA-2048`或国密`SM2`非对称加密算法生成数字签名。
  - **时间戳**: 采用`RFC3161`标准协议与可信时间戳中心交互。

### 3.2 核心库表结构设计 (部分示例)

使用DDL进行定义，展示核心业务表的结构。

```sql
-- 合同主表
CREATE TABLE `contracts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '合同自增ID',
  `flow_id` varchar(64) NOT NULL COMMENT '合同流程ID，全局唯一',
  `title` varchar(255) NOT NULL COMMENT '合同标题',
  `enterprise_id` bigint(20) unsigned NOT NULL COMMENT '发起方企业ID',
  `initiator_id` bigint(20) unsigned NOT NULL COMMENT '发起人用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '合同状态: 1-草稿, 2-签署中, 3-已完成, 4-已撤销, 5-已过期, 6-已拒签',
  `template_id` varchar(64) DEFAULT NULL COMMENT '来源模板ID',
  `deadline` datetime DEFAULT NULL COMMENT '签署截止时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_flow_id` (`flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 合同签署方表
CREATE TABLE `contract_approvers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_id` varchar(64) NOT NULL COMMENT '合同流程ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '签署人用户ID',
  `enterprise_id` bigint(20) unsigned DEFAULT NULL COMMENT '签署方企业ID (若为企业签署)',
  `sign_order` int(11) NOT NULL DEFAULT '0' COMMENT '签署顺序，0为无序',
  `sign_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '签署状态: 0-待签署, 1-已签署, 2-已拒签, 3-已转交',
  `signed_at` datetime DEFAULT NULL COMMENT '签署时间',
  `sign_cert_sn` varchar(128) DEFAULT NULL COMMENT '签署时使用的数字证书序列号',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 企业印章表
CREATE TABLE `seals` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seal_id` varchar(64) NOT NULL COMMENT '印章唯一ID',
  `enterprise_id` bigint(20) unsigned NOT NULL COMMENT '所属企业ID',
  `name` varchar(100) NOT NULL COMMENT '印章名称',
  `type` varchar(20) NOT NULL COMMENT '印章类型: OFFICIAL, CONTRACT, FINANCE...',
  `source` varchar(20) NOT NULL COMMENT '来源: TEMPLATE, UPLOAD',
  `file_url` varchar(512) NOT NULL COMMENT '印章图片文件URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态: 1-已启用, 2-已停用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_seal_id` (`seal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3.3 MQ消息流转机制

采用发布-订阅模式，实现核心业务的异步化和解耦。

- **消息队列选型**: Kafka或Pulsar，以其高吞吐、高可用和持久化能力，支撑业务增长。
- **核心Topic**:
  - `contract_status_change`: 当合同状态变更时（如一方签署完成、合同全部完成），`contract-service`向此Topic发送消息。
  - `notification_request`: 当需要发送短信、邮件或站内信时，业务服务向此Topic发送消息。
  - `evidence_chain_data`: 当签署完成、文件上传等关键操作发生时，向此Topic发送需要存证的数据摘要。
- **消费者**:
  - `notification-service`: 订阅`contract_status_change`和`notification_request`，负责调用第三方服务发送通知。
  - `evidence-service` (未在架构图显示，但实际存在): 订阅`evidence_chain_data`，负责将数据上链存证。
  - `data-sync-service`: 订阅多个业务Topic，将数据同步到Elasticsearch和数据仓库。

### 3.4 合同分布式存储

- **存储方案**: 采用**MinIO**对象存储集群，进行私有化部署，确保数据物理安全。集群采用纠删码模式，实现高可用和数据冗余。
- **存储逻辑**:
  1.  **原文上传**: 用户上传的原始文件（Word/图片等）存入`raw-files`桶。
  2.  **PDF转换**: 系统统一转换为PDF后，存入`pdf-preview`桶，用于签署过程中的预览。
  3.  **版本化存储**: 每次签署操作后，生成的带有新数字签名的PDF文件，作为一个**新版本**存入`signed-contracts`桶，利用MinIO的版本控制功能保留所有签署过程中的文件版本，便于追溯。最终完成的合同是该对象的最新版本。
  4.  **证据报告**: 生成的证据链报告PDF，存入`evidence-reports`桶。
- **访问控制**: 所有桶均设置为私有。业务服务通过生成的临时授权URL（presigned URL）访问文件，避免AK/SK在网络中传输。

### 3.5 AI合同生成与抠图技术实现

- **AI合同生成**:
  - **技术栈**: LangChain / LlamaIndex + 私有化部署的大模型（如ChatGLM, Qwen） + Milvus向量数据库。
  - **流程**:
    1.  **知识库构建**: 将海量法律法规、标准合同范本、企业自有合同等进行切分、清洗，通过Embedding模型（如BGE）向量化后存入Milvus。
    2.  **意图识别**: `ai-generation-service`识别用户意图（如“生成一份租赁合同”）。
    3.  **RAG检索**: 根据用户意图和对话内容，在向量数据库中检索最相关的法律条款和合同片段。
    4.  **Prompt构建**: 将用户需求、检索到的知识、预设的Prompt模板组合成一个丰富的Prompt。
    5.  **LLM调用**: 调用大模型服务，生成合同文本。
    6.  **后处理**: 对生成内容进行校验、格式化，并返回给用户。
- **电子章智能抠图**:
  - **技术栈**: OpenCV, PaddleSeg/U-Net。
  - **流程**:
    1.  **图像预处理**: `ai-ocr-service`对上传的印章图片进行尺寸归一化、去噪、二值化。
    2.  **印章定位**: 使用基于深度学习的图像分割模型，精确定位印章的主体像素区域。
    3.  **背景去除**: 将分割出的印章区域外的所有像素设置为透明。
    4.  **边缘优化**: 使用形态学操作（如腐蚀、膨胀）平滑印章边缘，去除毛刺。
    5.  **颜色标准化**: 将印章颜色统一为标准的“中国红”。"分类","一级模块","二级模块","三级功能/具体任务","涉及岗位","产出物","工作量 (人天)","单价 (元/人天)","报价 (元)","备注"
"Part 1: 核心平台研发",,,,,,,,,"2,130,000"
,"平台基础","项目管理与设计","产品需求池(PRD)与原型(UI/UX)持续迭代、项目排期管理","产品经理,UI/UX设计师","PRD文档,高保真原型图,项目排期表","60","1500","90000","覆盖整个项目周期的持续性工作"
,,"技术基建","基于K8s的生产/测试环境搭建,CI/CD流水线(Jenkins/ArgoCD),监控告警(Prometheus),日志系统(EFK)","后端工程师,运维工程师(SRE)","环境部署脚本,CI/CD配置文件,监控大盘","40","2000","80000","项目启动初期的核心基建工作"
,"账户与认证中心","个人账户体系","多渠道注册/登录(手机/微信),统一实名认证(多证件),账户管理(更名/换绑/注销),个人签名/印章管理","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","40","1800","72000","涉及与第三方实名认证服务对接"
,,"企业账户体系","多通道企业认证(法人/打款/商户号),超管/法人体系,企业信息管理","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","45","1800","81000","企业认证流程复杂，需重点测试"
,"组织与权限(RBAC)","组织架构管理","无限层级部门管理,多方式员工管理(导入/邀请/同步),员工离职与交接","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","50","1800","90000",""
,,"角色权限体系","系统预设角色,自定义角色,操作权限+数据权限(企业/部门/个人)精细化配置","后端工程师,前端工程师,测试工程师","API接口,权限配置页面,测试用例","60","1800","108000","权限系统是B端核心，设计复杂"
,"印章中心","印章生命周期管理","多类型印章创建(模板/上传),印章授权,用印审批流,用印日志,启停用/删除","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","50","1800","90000",""
,"合同生命周期管理","合同拟定与发起","多源发起(模板/文件/草稿),在线协同编辑(多人/版本/评论),动态填写控件","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","70","1800","126000","在线协同编辑技术复杂度高"
,,"签署流程与执行","灵活签署流程(顺序/无序/混合),多重意愿认证配置,自动签署(本方),批量签署,拒签/撤销/转交","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","80","1800","144000","流程引擎状态机设计复杂"
,,"合同归档与管理","智能归档与分类,多维度智能检索,全链路操作日志视图,合同后处理(出证/解除)","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","60","1800","108000","需要与Elasticsearch深度集成"
,"高级功能与集成","集团企业解决方案","集团组织构建,集中管控(合同/印章/模板),资源共享","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","70","1800","126000",""
,,"场景化签约方案","一码多签,视频会议签插件开发,战略会议签方案","后端工程师,前端工程师,测试工程师","API接口,前端页面/插件,测试用例","60","1800","108000","会议签涉及与第三方会议软件集成"
,,"开放平台(API/SDK)","API Gateway配置,全功能API文档编写,多语言SDK开发,Webhook回调机制","后端工程师,技术文档工程师","API文档,SDK包,Webhook服务","50","1800","90000",""
"Part 2: AI能力研发与部署",,,,,,,,,"1,027,500"
,"AI模型层","大模型选型与评估","评估多种大模型(Deepseek, qwen, GLM等)在合同领域的综合效果、性能及成本","AI算法工程师,产品经理","模型评估报告","25","2500","62500",""
,,"知识库构建","法律法规/合同范本/行业数据清洗、切分、向量化(使用BGE等模型),构建向量数据库(Milvus)","AI算法工程师,数据工程师","向量数据库,数据处理脚本","40","2500","100000","高质量知识库是RAG效果的基础"
,,"模型部署与调优","大模型私有化部署,推理优化(vLLM/TGI),Prompt Engineering,模型微调(Fine-tuning)","AI算法工程师,运维工程师(SRE)","部署方案,优化后的模型服务,Prompt模板库","50","2500","125000",""
,"AI服务层","智能合同生成服务","提供智能问答生成合同、条款库推荐、风险审查与提示等能力的API服务","AI算法工程师,后端工程师,测试工程师","ai-generation-service,API接口,测试用例","80","2200","176000",""
,,"智能抠图与识别服务","提供电子章智能抠图、文档关键信息OCR识别等能力的API服务","AI算法工程师,后端工程师,测试工程师","ai-ocr-service,API接口,测试用例","60","2200","132000",""
"Part 3: 第三方服务与硬件成本 (年预估)",,,,,,,,,""
,"第三方服务费用",,"CA/时间戳服务","按签署份数或按年采购数字证书和时间戳服务","采购","","","100000","核心安全成本，必须采购"
,,,"短信/邮件服务","用于发送验证码、业务通知等","采购","","","50000","按量付费，预估值"
,,,"大模型API调用","若部分非核心场景使用公有云大模型API的费用","采购","","","100000","作为私有化模型的补充，按量付费"
,,,"工商信息查询","用于企业认证时的信息核验","采购","","","30000","按次调用，预估值"
,"硬件成本(本地部署)",,"AI服务器(训练+推理)","方案2: H200方案, 1台 (NVIDIA H200 * 8)","采购","","","2,926,000","参考截图方案，兼顾训练和推理"
,,,"应用服务器集群","用于部署微服务、数据库、中间件等，预估需要10台高性能服务器","采购","","","500000","按高可用标准配置"
,,,"网络设备","交换机、防火墙、负载均衡器等","采购","","","200000",""
"总计",,,,,,"","","6,993,500",明白了。我将为你构建一份完整的电子签平台业务体系与技术架构设计文档，融合腾讯电子签、e签宝、法大大、契约锁、上上签等主流平台的核心与辅助功能，并结合AI智能合同能力进行优化设计。内容将以Markdown格式输出，包括产品价值、功能列表、核心流程图、架构设计与技术栈等模块。

我会尽快完成初步草稿并通知你查阅。


# 路浩AI电子签业务体系与技术架构设计

## 产品定位与价值主张

**产品愿景：**“路浩AI电子签”旨在打造一个融合人工智能能力、支持全行业使用、具备政企级安全与扩展能力的电子签名SaaS平台。平台立志成为业内领先的电子合同解决方案，为用户提供**好用、智能、安全**的合同签署与管理服务。我们希望通过AI技术赋能电子签约，全方位提升合同生命周期各环节的效率与体验。

**战略定位：**平台采用纯云原生架构，仅提供云端SaaS服务（无需本地部署），专注于政企级市场同时兼顾个人用户，满足**政务机构、企事业单位、法律服务、知识产权、个人用户**等全行业的电子签约需求。我们强调以云服务的形式提供**开箱即用**的电子签解决方案，不需要用户自行搭建环境，从而降低使用门槛和运维成本。通过先进的AI能力与灵活的API接口，我们亦能深度融入各行各业的业务流程，成为数字化合同管理的基础设施。

\*\*主要服务对象：\*\*平台服务对象涵盖政府机关、各类型企业、律师事务所、专利代理机构以及各类有签约需求的个人用户。对于政府和大型企业，平台提供符合国家安全规范的严谨风控和权限管理，支持海量合同签署和监管审计；对于中小企业与法律/知识产权行业，平台提供丰富模板和流程定制，帮助提高合同起草审核效率，控制法律风险；对于个人和自由职业者，平台提供标准合同模板和便捷签署渠道（如手机H5/小程序），保障日常协议签署的合法有效性。

**差异化优势：**“路浩AI电子签”相较竞品具备以下独特价值：

* \*\*AI能力赋能：\*\*将大模型技术融入合同拟定、审查、管理全过程，实现智能问答生成合同、自动审核风险、OCR识别印章等功能，极大降低专业门槛和人力成本。这是传统电子签平台所不具备的新一代优势。
* **极致易用：**提供简洁直观的界面和**一键签署**体验，实现合同**秒发秒签**。支持PC、手机、微信小程序等多端无缝使用，随时随地签署合同，提高业务效率。复杂流程经优化对用户友好，“所见即签”，降低培训成本。
* \*\*价格清晰：\*\*采用年订阅制的版本定价，**定价简单明了**。个人版和企业各档位功能明确，无隐藏收费，不按每份合同另行计费，使用成本可预期。相比部分竞品按次或坐席收费的模式，用户更容易接受我们的透明定价策略。
* **纯SaaS免部署：**只提供云端服务，不提供本地私有化部署，从架构上**全面云原生**。这样确保所有用户始终使用平台最新功能，无需自行运维升级，并通过云端弹性保障性能和可靠性。这一点在传统需要本地安装部署的软件相比具有明显优势。

此外，平台充分利用腾讯等生态能力，如可靠的身份认证手段（如实名和人脸识别）、区块链存证等，确保电子合同签署的公信力和法律效力。总体而言，“路浩AI电子签”以“AI+电子签”的创新定位，为各类用户提供**安全可信**、**高效便捷**且**智能增值**的合同签署服务。

## 产品版本与定价结构

平台针对不同用户群体提供**个人版**和**企业版**多个档次，满足从个人用户到大型政企客户的需求。

* **个人版（¥399/年）：**面向个人用户、自由职业者以及合同数量较少的小微企业。个人版提供基础的电子签名功能，包括身份认证、合同模板应用、单方/双方法律合同签署、签署过程存证及合同归档下载等。个人版注重**经济实惠**和**使用简单**，订阅年度套餐即可不限次数签署常规合同（有合理的流量上限保障正常使用），非常适合个人租赁、借贷、合作协议等场景。

* **企业版标准（¥5999/年）：**适用于中小型企业的基础套餐。标准版支持最多一定数量的企业成员账号，提供企业实名认证、企业公章管理、员工账号及权限管理、**基础合同管理**（合同发起、签署、归档）等功能。包含**基础模板库**、简单审批流程和日志导出等。旨在以较低成本满足企业**电子化签约**的入门需求。

* **企业版专业（¥12,999/年）：**面向成长型企业或有较高签约量的组织。专业版在标准版基础上增加**高级功能**：如**批量签署**（同一签署人多份合同一键签署）、**签署流程自定义**（多角色顺序/并行签署配置）、**审批流**（合同/用印审批），**API对接**能力（可与企业现有系统集成）等。还提供更大的合同与存储额度、更丰富的合同模板，以及优先级技术支持。专业版帮助企业深度融入业务流程，提升签约效率，控制风险。

* **企业版旗舰（按需定价）：**针对大型企业、集团公司和政府机构的定制方案。旗舰版支持**不限用户数**的企业账户、支持私有网络连接、数据专属存储等**定制化部署**需求，可对接企业AD/LDAP单点登录，提供更严格的安全隔离和**专属技术支持**团队。功能上包含专业版所有特性，并可根据客户需求定制开发特殊功能模块。旗舰版价格依据合同签署规模、定制开发工作量等按需报价，充分满足**超大规模签约**和**个性化安全合规**要求。

\*注：\*所有版本均包含基础的电子签名法律效力保障服务（实名认证、数字签名证书、存证取证接口等）。企业版各档次的区别主要在于用户数量上限、功能深度和支持服务级别。清晰的版本划分和年费定价让客户可以根据自身需求和预算自由选择，避免隐藏费用，降低采购决策复杂度。

## 产品结构图与架构概览

平台整体产品结构可以分为三个主要层面：**个人端、企业端**和**运营管理端**，它们分别面向不同的用户角色，但共同依托于统一的电子签约云服务平台。下面以结构示意图描述各层次：

```mermaid
flowchart TB
    subgraph 客户端应用层
        A[个人端 \n（个人用户）]
        B[企业端 \n（企业/政企用户）]
        C[运营管理后台 \n（平台运营人员）]
    end
    subgraph 云端服务层
        D[电子签约SaaS平台\n（业务微服务与AI服务）]
    end
    subgraph 基础设施层
        E[数据库与存储]
        F[第三方集成]
    end
    A & B --> D --> E
    C --> D
    D --> F
```

如上图所示：“路浩AI电子签”主要由**前端客户端应用层**、**云端服务层**和**基础设施层**组成：

* \*\*个人端：\*\*提供面向个人用户的应用界面，包括PC网页端、移动H5/小程序等。个人用户通过个人端进行实名认证、创建或选择合同模板、发起签署以及查看存档文件等操作。
* \*\*企业端：\*\*提供面向企业用户的网页系统或集成接口，包括企业管理员和经办人员使用的功能。如企业资质认证、企业通讯录与成员管理、合同起草与审批、用印管理、合同签署及归档查询等均在企业端完成。企业端支持PC Web、移动App，以及嵌入到钉钉、企业微信等协同平台。
* \*\*运营管理后台：\*\*由平台方运维人员使用的管理控制台。用于审核企业认证申请、监控平台运行、管理合同模板库、配置AI模型和安全策略，以及处理用户服务工单等。运营后台确保平台合规稳定运营，为前端用户提供支持。

**云端服务层**是平台的核心业务层，承载所有电子签约相关的服务逻辑，包括用户与权限、合同流程、签名与证书、模板、印章、AI助手等微服务。前端各端的请求通过API网关进入云端服务层处理。**基础设施层**提供数据和第三方支持，包括关系型数据库、非结构化存储、缓存和搜索引擎，以及与**CA数字证书机构**、**可信时间戳服务**、**区块链存证节点**、**短信网关**、**AI大模型接口**等外部服务的集成。整体架构采用微服务+云原生模式，实现**各端统一接入、后端集中服务、底层弹性支撑**的设计，既保证了各类用户的差异化需求，又确保了平台的安全性和扩展性。

## 功能列表

平台功能覆盖电子合同生命周期的各个方面。下面按照**一级系统 - 二级模块 - 三级功能**的层次，列出完整的功能清单，并标识哪些为核心功能（常用或产品主打）与辅助功能（增强或特定场景）。功能列表整合了业内主流电子签产品的优秀设计，形成统一全面的方案。

### 个人端功能列表

个人端面向个人用户，提供个人身份签署合同的能力：

| **模块**      | **具体功能**                | **类型** |
| ----------- | ----------------------- | ------ |
| **账户与认证**   | 用户注册/登录（手机号验证码、一键登录等）   | 核心     |
|             | 个人实名认证（身份证实名认证、人脸识别核身）  | 核心     |
|             | 签署意愿验证（签署短信验证码、一键实人认证）  | 核心     |
| **合同拟制与签署** | 创建合同（从模板快速发起/上传本地文件）    | 核心     |
|             | 合同模板库浏览与选择（生活常用范本模板）    | 核心     |
|             | 模板填充与在线编辑（填入签署方、金额等信息）  | 核心     |
|             | AI智能合同起草（通过对话让AI生成合同草稿） | 辅助     |
|             | AI合同风险审查（识别缺失条款、异常条款高亮） | 辅助     |
|             | 添加签署方（输入对方手机号/邮箱邀请签署）   | 核心     |
|             | 设置签名方式（手写签名、电子印章盖章）     | 核心     |
|             | 发送签署链接（微信/短信/邮箱一键发送通知）  | 核心     |
| **签署过程**    | 在线签名/盖章（在手机或电脑上完成电子签名）  | 核心     |
|             | 实时签署状态跟踪（查看对方是否签署、拒签）   | 核心     |
|             | 签署意愿确认（签署前短信验证码二次确认）    | 辅助     |
|             | 拒签理由反馈（签署方可填写拒签原因）      | 辅助     |
|             | 撤销签署（发起人在对方未签前可撤回合同）    | 辅助     |
| **合同管理**    | 已签合同查看与下载（PDF加签名证书）     | 核心     |
|             | 合同归档保存（云端长期保存，防篡改存证）    | 核心     |
|             | 合同检索查询（按合同标题、签署方关键字搜索）  | 核心     |
|             | 存证取证服务（一键获取签署证明文件）      | 核心     |
|             | 合同删除/销毁（符合条件的合同可永久删除）   | 辅助     |
| **个人印章**    | 个人签名样式设置（支持手写签名图像上传）    | 辅助     |
|             | 个人电子印章生成（根据姓名生成标准章）     | 辅助     |
|             | 印章管理（启用/停用个人印章）         | 辅助     |
| **账户费用**    | 订阅及续费管理（查询个人版有效期，在线续费）  | 核心     |
|             | 签署配额查看（个人版可能设有月度签署份数上限） | 辅助     |
|             | 发票申请与下载                 | 辅助     |

*（说明：个人端侧重简洁易用，核心功能围绕快速签署常见合同，辅助功能提供个性化和保障措施。）*

### 企业端功能列表

企业端提供组织级的签约管理功能，包括企业管理员和企业员工两类用户使用的子模块：

| **模块**      | **具体功能**                    | **类型** |
| ----------- | --------------------------- | ------ |
| **企业认证与账户** | 企业实名认证（营业执照上传，法人身份验证）       | 核心     |
|             | 企业信息档案（企业基本资料、证照管理）         | 核心     |
|             | 企业账户审核（平台运营人员审核认证申请）        | 核心     |
|             | 企业账号设置（密码策略，登录安全设置）         | 辅助     |
| **组织与人员**   | 成员管理（添加/删除员工账户，分配部门）        | 核心     |
|             | 角色与权限（管理员、经办人、观察者等权限配置）     | 核心     |
|             | 部门架构管理（组织架构树维护）             | 辅助     |
|             | 成员实名认证（员工个人实名认证流程）          | 辅助     |
| **模板与合同拟制** | 企业模板库（企业自有合同模板上传、分类管理）      | 核心     |
|             | 官方模板库（平台提供的通用合同模板调用）        | 核心     |
|             | 模板编辑器（支持在线设计模板、填充控件）        | 辅助     |
|             | 智能模板推荐（根据合同类型自动推荐模板）        | 辅助     |
|             | 合同起草与发起（选择模板或上传文件创建合同）      | 核心     |
|             | 合同内容在线编辑协作（多人实时编辑草稿）        | 辅助     |
|             | 合同版本管理（草稿版本历史、差异对比）         | 辅助     |
|             | AI合同生成助手（业务问答生成合同初稿）        | 辅助     |
|             | AI风险审查助手（审核外部合同文本，标记风险）     | 辅助     |
| **签署流程管理**  | 签署方设置（定义合同各签署方及其签署顺序）       | 核心     |
|             | 多角色签署（支持甲方乙方丙方等多方签约）        | 核心     |
|             | 签署流程类型（顺序签署/并行签署配置）         | 核心     |
|             | 签署意愿认证设置（针对重要合同启用人脸/视频见证）   | 辅助     |
|             | 审批流设置（发起前内部审批，如法务审批合同内容）    | 核心     |
|             | 用印申请审批（签署前申请用章，由管理员审核）      | 核心     |
|             | 自动签署/静默签署（预设规则自动签章）         | 辅助     |
| **签署执行**    | 合同批量签署（同一签署人待签合同一键签署）       | 核心     |
|             | 外部签署链接（生成链接/二维码供外部方签署）      | 核心     |
|             | 通知提醒（签署请求短信/邮件通知，经办人催签）     | 核心     |
|             | 拒签处理（签署方拒签后流程终止通知各方）        | 核心     |
|             | 撤销合同（发起人撤销并通知所有相关方）         | 核心     |
|             | 转交签署（签署任务可转交给有权限的他人）        | 辅助     |
| **合同归档与管理** | 合同归档（签署完成合同自动归档入库）          | 核心     |
|             | 合同分类标签（自定义合同类别，标签管理）        | 核心     |
|             | 合同检索（全文检索合同内容及元数据）          | 核心     |
|             | 高级筛选（按类型、日期、金额等多条件筛选）       | 核心     |
|             | 合同附件管理（支持上传合同相关附件并归档）       | 辅助     |
|             | 合同下载与证书（下载含数字签名证书的PDF）      | 核心     |
|             | 存证出证（在线申请区块链存证报告、公证/仲裁）     | 辅助     |
|             | 合同台账报表（按需导出签约统计报表、Excel）    | 辅助     |
| **印章管理**    | 电子印章创建（企业公章、合同章、人名章等）       | 核心     |
|             | 印章资质证明（上传印章印模、关联数字证书）       | 核心     |
|             | 印章权限分配（控制哪些员工/部门可使用特定印章）    | 核心     |
|             | 用印申请与审批（员工发起用印申请，管理员审批）     | 核心     |
|             | 印章使用日志（记录每次用印操作、时间、人员）      | 核心     |
|             | 印章状态管理（启用/停用印章，防止滥用）        | 核心     |
|             | 印章生命周期（印章更换、作废销毁及记录）        | 辅助     |
| **企业设置**    | 企业印控策略（自定义不同合同类型的审批规则）      | 辅助     |
|             | 安全策略配置（密码策略、二步验证、登录IP限制）    | 辅助     |
|             | 日志审计（导出操作日志，满足合规审计要求）       | 辅助     |
|             | 多语言/多时区支持（跨国企业使用）           | 辅助     |
| **集成与开放**   | 开放API接口（提供RESTful API签署集成）  | 核心     |
|             | SDK工具包（提供Java/Python等语言SDK） | 辅助     |
|             | Webhook通知（签署事件回调通知企业系统）     | 辅助     |
|             | 第三方集成插件（钉钉、企业微信用印插件等）       | 辅助     |

*（说明：企业端功能丰富，核心功能满足企业日常在线签署和管理需求，辅助功能为大型企业深度集成和精细化管理提供支持。）*

### 运营管理后台功能列表

运营后台由平台方内部运营人员使用，用于对平台用户和系统进行管理维护：

| **模块**      | **具体功能**                      | **类型** |
| ----------- | ----------------------------- | ------ |
| **用户与权限管理** | 企业认证审核（审核企业提交的资质和身份信息）        | 核心     |
|             | 企业账户管理（查看企业客户列表、状态变更）         | 核心     |
|             | 个人用户管理（统计个人用户数、使用情况）          | 核心     |
|             | 黑名单和风控（可冻结违规账户、防范欺诈）          | 辅助     |
| **内容与模板管理** | 官方模板库管理（新增/更新平台提供的标准模板）       | 核心     |
|             | 模板审核（审核企业上传的公共合同模板）           | 辅助     |
|             | 行业方案配置（配置不同行业模板和AI知识库）        | 辅助     |
| **系统配置**    | 签名证书配置（对接多家CA，配置证书策略）         | 核心     |
|             | 时间戳服务配置（对接可信时间戳服务TSA）         | 核心     |
|             | 区块链存证配置（选择上链存证节点/服务）          | 核心     |
|             | SMS/Email配置（短信邮件网关账户设置）       | 辅助     |
|             | AI模型配置（接入LLM提供商API Key，向量库管理） | 辅助     |
| **运营分析**    | 合同签署数据统计（按日/月统计平台签约量）         | 辅助     |
|             | 系统使用率监控（各功能模块调用频度监控）          | 辅助     |
|             | 客户活跃度分析（付费转化率等商业指标）           | 辅助     |
| **运维管理**    | 日志查询（系统日志、操作日志检索）             | 核心     |
|             | 警报与通知（异常行为告警，系统故障通知）          | 核心     |
|             | 任务调度管理（定时任务配置，如定期存证）          | 辅助     |
|             | 版本发布管理（新功能灰度发布控制）             | 辅助     |
| **客服支持**    | 工单管理（查看和回复用户反馈的服务工单）          | 核心     |
|             | 帮助中心内容维护（更新常见问题解答）            | 辅助     |
|             | 消息公告发布（向用户推送系统公告/更新日志）        | 辅助     |

*（说明：运营后台核心功能在于审核把关、配置平台安全要素和保持系统平稳运行，辅助功能帮助提升运营效率和用户满意度。）*

上述功能体系囊括了电子签约平台所需的**核心业务功能**（身份认证、在线签署、证据保全等）以及**增强型功能**（AI辅助、深度集成、运营分析等）。所有功能共同构成完整的合同生命周期管理闭环，既满足普通用户快捷签署需求，也支持大型政企对合规、安全、效率的更高要求。

## 核心流程图

下面通过Mermaid流程图展示几项平台关键业务流程，帮助理解系统运作：

### 合同发起—审批—签署—归档流程

```mermaid
flowchart LR
    subgraph 发起方
      A[起草合同]
      B[提交内部审批]
    end
    subgraph 平台流程
      C(审批中)
      D(待签署)
      E(签署中)
      F(已归档)
    end
    subgraph 签署方
      G[收到签署通知]
      H[在线签署合同]
    end
    A --> B --> C
    C --> |审批通过| D
    C --> |审批拒绝/撤回| A
    D --> |发送签署链接| G
    G --> H --> E
    E --> |双方签署完成| F
```

\*\*说明：\*\*企业用户起草合同后，如果配置了审批流程，需要先提交内部审批（如法务或主管审批）。审批通过则合同进入待签署状态，并自动发送签署通知给外部签署方；审批不通过则退回起草人修改或作废。在待签署阶段，各签署方在线查看并签署合同（可支持顺序签署或同时签署）。所有签署方完成签名盖章后，合同状态变为“已完成”，平台自动归档合同文件并存证上链，整个流程结束。归档后的合同可随时调阅验证。

### 企业实名认证流程

```mermaid
flowchart TB
    X[企业提交认证申请<br/>（上传营业执照等材料）] --> Y[平台审核资质]
    Y --> |审核通过| Z[企业账号认证成功<br/>开通企业功能]
    Y --> |审核不通过| X
```

\*\*说明：\*\*新企业用户注册后，需要完成企业实名认证：提交工商营业执照、组织机构代码、法人身份证明等材料。平台运营人员在后台对提交的资质进行审核核验。若审核通过，则将该企业用户标记为认证企业，开通企业端完整功能（可创建员工账号、使用企业签章等）；若不通过，则退回申请要求补充或纠正材料，企业可重新提交认证申请。

### 企业印章管理流程

```mermaid
flowchart LR
    A[企业管理员申请新增印章] --> B[提供印章名称及用途]
    B --> C[上传印章图样或系统生成模板章]
    C --> D[绑定数字证书/公钥]
    D --> E[平台审核备案]
    E --> F[印章生成可用]
    F --> G[设置印章使用权限给指定人员]
```

\*\*说明：\*\*企业管理员可以在系统中新增电子印章：填写印章名称（如“合同专用章”）、用途说明，上传印章图样文件（或使用系统提供的模板生成带公司名称的电子公章）。同时需要将该印章关联企业的数字签名证书（由权威CA颁发，用于法律效力）。平台对新的印章申请进行审核备案，通过后生成可用的电子印章。管理员接着可以将此印章授权给特定员工使用，并可配置用印审批流程。以后员工在合同签署时选择该印章，若需要审批则按流程批准后盖章。平台对印章的全生命周期（创建、授权、使用、停用、删除）均留有操作记录以备审计。

*（其他如AI生成合同流程、合同证据存证流程等亦有相应机制，这里从略。）*

## 技术架构与技术栈

**总体架构：**“路浩AI电子签”后端采用**云原生微服务架构**，通过容器化部署和弹性伸缩保障高并发下的性能和可靠性。主要技术栈选择Java和Go结合：Java（Spring Boot框架）承担核心业务服务开发，Go（基于Go-Kratos框架）用于高性能组件和AI相关服务，实现优势互补。系统内部服务遵循领域驱动设计原则划分边界，高内聚低耦合，并通过服务注册发现和API Gateway对外统一暴露接口。整体架构注重**高可用、可扩展、安全合规**，关键技术组件如下：

* **服务框架：**后端核心使用 **Java Spring Boot** 微服务框架构建，各服务独立部署；部分高并发服务和基础服务采用 **Go + Kratos** 实现。前端采用React等主流技术构建 Web 界面，移动端通过H5/小程序适配。服务间通信主要基于HTTP RESTful API和gRPC（内部高效调用），并辅以**消息队列**实现异步解耦。
* **数据库存储：**采用混合数据库方案：**PostgreSQL** 作为主事务型数据库，存储核心业务数据（用户、企业、合同元数据、印章等），利用其JSONB支持兼顾部分非结构化内容；**MongoDB** 用于存储合同正文、模板等文档数据以及审计日志（方便扩展大文档存储）；**Redis** 作为缓存数据库，缓存会话、验证码、临时数据，提升读写性能；**Elasticsearch** 构建全文检索引擎，实现对合同内容和日志的多条件快速搜索；**分布式文件存储**（如MinIO或对象存储服务）用于保存合同PDF文件、附件原件，支持大文件的高可靠存储和访问。另外引入**向量数据库**（如 Milvus/Qdrant）保存AI语义索引，用于合同知识库的语义检索（RAG）。
* \*\*核心中间件：\*\***RabbitMQ** 或 **Kafka** 作为消息队列，用于异步任务和事件通知（如签署完成通知、OCR任务下发）。**XXL-Job** 等分布式任务调度框架用于定时任务管理（如定期清理草稿、证据保全轮询）。API Gateway采用Kratos Gateway或Spring Cloud Gateway，实现统一鉴权、流量控制和路由。服务注册配置使用Nacos或Consul等，配置中心统一管理。通过服务网格（可选 Istio）进一步增强服务间通讯的可观测性和安全。
* **安全加密与合规：**平台在安全上采用业界最佳实践：传输层开启全链路 **TLS 1.3** 加密，内部服务通信也启用双向TLS认证。存储层面，用户敏感数据（如身份证号、手机号）在数据库中使用**AES-256-GCM**或国密**SM4**算法加密保存，密钥统一由独立的KMS管理；合同PDF文件在对象存储中启用服务端加密。电子签章采用**非对称加密**数字签名技术，摘要算法使用 **SHA-256** 或国密 **SM3** 计算文件哈希；签名算法调用权威CA服务，使用 **RSA-2048** 或国密 **SM2** 算法生成数字签名。每次签署还会获取**可信时间戳**（符合RFC3161标准）来确认证据的时间。此外，平台对接司法链/联盟链，将合同签署关键哈希值上链存证，借助区块链和时间戳确保电子数据不可篡改、全流程可追溯。平台安全模块遵循国家电子签名法及密码管理规定，符合等保三级等安全要求（如数据加密存储、访问控制和审计）确保政企用户放心使用。
* **AI能力接入：**平台内置**智能合同助手**服务，采用 **RAG（检索增强生成）** 技术架构集成大语言模型。具体做法是在平台内构建领域合同知识库（包括法律条款库、历史合同片段等）并使用向量检索引擎提供语义查询，AI服务将用户提问或合同文本与相关知识检索结果相结合，通过调用主流大语言模型（可对接OpenAI GPT系列或国内模型如讯飞星火、百度文心等）进行答案生成或合同改写。AI助手可多模型协同，根据任务智能选择或并行调用不同模型（如调用专业法律模型审查合同风险，调用通用模型生成语言表达），以平衡准确性和创造性。平台采用策略路由确保每项AI任务使用最适合的模型，并支持模型持续训练优化。AI能力主要体现在：智能问答生成合同、合同条款修改建议、风险条款检测、一键提取合同要点、OCR识别合同盖章及证件等功能上，实现**AI对业务的深度赋能**。
* **架构高可用设计：**所有服务均无状态部署，利用Kubernetes容器编排实现弹性伸缩和故障自愈。关键数据库和缓存集群搭建主从或多副本，保证高可用和数据冗余备份。引入熔断器和限流措施保障当某些外部服务（如CA、短信、AI接口）异常时不影响核心签署功能。重要操作（签名、生效）采用事务机制和消息确认，确保流程的**原子性和可靠性**。同时，通过完善的日志和监控告警体系，及时发现并处理异常，保证平台7x24稳定运行。

技术架构如下示意：

```mermaid
flowchart TB
    %% 分层展示架构关键组件
    subgraph 客户端层
        direction LR
        Web[Web前端 (React)]
        Mobile[移动端 (H5/小程序)]
        APIClient[开放API/SDK]
    end
    subgraph 网关层
        APIGW[API网关]
    end
    subgraph 微服务层
        direction TB
        AuthSvc[账户认证服务]
        OrgSvc[组织权限服务]
        ContractSvc[合同签署服务]
        TemplateSvc[模板管理服务]
        SealSvc[印章管理服务]
        ApprovalSvc[审批流程服务]
        AISvc[AI智能服务]
        NotifSvc[通知推送服务]
        BillingSvc[计费订单服务]
    end
    subgraph 基础设施层
        DB[(PostgreSQL 主库)]
        DocDB[(MongoDB 文档库)]
        Cache[(Redis 缓存)]
        Search[(Elastic 搜索)]
        MQ[消息队列 (RabbitMQ)]
        Scheduler[调度 (XXL-Job)]
        Storage[(对象存储/区块链存证)]
        CA[CA签名服务]
        TSA[可信时间戳服务]
        LLM[大模型AI服务]
    end
    Web & Mobile & APIClient --> APIGW --> 微服务层
    微服务层 --> DB & DocDB & Cache & Search
    微服务层 --> MQ & Scheduler
    AISvc --> LLM
    ContractSvc & SealSvc --> CA & TSA
    微服务层 --> Storage
```

上图展示了平台的核心服务模块和基础设施之间的关系。客户端通过API网关访问各种微服务；微服务层实现具体业务逻辑，并调用底层数据库、缓存、搜索引擎存取数据；同时使用消息队列和任务调度器处理异步任务；安全相关服务（数字证书CA、可信时间戳TSA）在签署时由合同/印章服务对接调用；AI服务对接外部大模型API，实现智能功能。整套架构具备**清晰的分层**和**良好的扩展性**：可以方便地水平扩展服务实例，应对高并发；也可以根据业务需求增减模块（例如扩展更多AI能力或集成新存证手段），从而保证平台的生命力和竞争力。

## 差异化亮点总结

综合以上，“路浩AI电子签”在功能和技术上相比现有竞品有以下独特亮点：

* \*\*AI驱动合同生成与审查：\*\*率先将大语言模型应用于电子合同领域。用户可以通过与AI对话自动生成合同初稿，AI还能实时审查合同文本中潜在风险条款并给出修改建议。这大幅降低了非专业人员拟稿的门槛，并为法律人员提供智能辅助，加快合同起草和审核流程。

* **OCR印章识别与纸质合同数字化：**平台内置OCR能力，可以精准识别合同扫描件中的印章、签名等要素。用户上传纸质合同照片后，系统自动提取其中的盖章信息和关键文本，将其转化为可管理的电子合同数据。这种能力方便了**历史纸质合同上链管理**，也能用于验真比对防止篡改。

* **一键合同签发与批量签署：**通过优化交互设计，实现合同从发起到签署的极简流程。发起人填完合同内容后，一键即可发送给所有签署方，无需繁琐操作。针对同一签署人有多份待签合同的情形，支持**批量签署**：签署人只需一次身份验证和操作，即可集中签署多份合同，提高效率。这在销售、HR场景下非常实用。

* **智能合同模板推荐：**平台内建丰富的合同模板库，并结合AI分析用户业务场景，自动推荐最适合的合同模板供选择。例如当用户输入合同标题或选择合同类型时，系统智能匹配行业场景下常用的模板，省去用户查找时间。这一**智能推荐**机制保证用户“拿来即用”，提升签约体验。

* **清晰透明的定价模式：**不同于部分竞品按签署次数或用户数复杂计费，本平台采用年费订阅按版本定价，让客户明白每年投入成本。各版本涵盖功能和配额公开透明，无隐藏收费项目。尤其个人版价廉物美，企业版套餐明确，帮助客户**轻松对比选择**。这种定价简单直接的策略在市场上更具亲和力。

* **完全云原生、无缝升级：**我们坚持云端SaaS模式，不提供本地部署，从架构上杜绝了版本分裂和升级难题。所有用户始终使用统一版本，第一时间享有新功能新特性。云原生架构确保弹性伸缩和故障快速恢复，无需客户投入IT运维资源。相较某些需要本地安装维护的电子签系统，本平台在**可用性和运维成本**方面有巨大优势。

* \*\*政企级安全合规能力：\*\*平台按照金融级标准打造安全体系，全面支持国密算法和合规需求。通过了等保三级、ISO27001等权威认证，采用区块链存证+时间戳技术确保合同不可篡改。同时支持严格的权限控制和操作留痕，可满足政企客户对安全审计的要求。相较一般民用电子签产品，我们的安全性和合规性达到政企级水准，真正做到让客户放心。

综上，“路浩AI电子签”集成了当前电子签约领域各家之长，并在AI智能化方面引领创新。它既提供全面完善的**合同签署管理功能**，又具备差异化的**智能优势**和**高安全、高可靠**保证，能够为各行业用户带来实实在在的价值提升。在数字化浪潮下，我们有信心凭借这些独特亮点，帮助客户实现合同管理的提质增效，成为新一代电子签约市场的标杆产品。

产品板块,终端平台,一级功能模块,二级功能点,工作量(人天),报价(元)
个人版,小程序,账户与认证,微信一键授权注册/登录,4,6000
个人版,小程序,账户与认证,手机号+验证码登录,2,3000
个人版,小程序,账户与认证,统一实名认证(人脸识别/多证件),5,7500
个人版,小程序,账户与认证,个人账户管理(改手机/邮箱/密码/更名),4,6000
个人版,小程序,账户与认证,设置签署验证方式(指纹/密码优先),2,3000
个人版,小程序,账户与认证,与港澳台居民签署开关,3,4500
个人版,小程序,签名管理,手写签名创建与管理,3,4500
个人版,小程序,签名管理,AI智能抠图上传签名,4,6000
个人版,小程序,签名管理,系统字体签名生成,3,4500
个人版,小程序,合同与签署,从微信文件发起合同,3,4500
个人版,小程序,合同与签署,使用官方模板发起合同,2,3000
个人版,小程序,合同与签署,合同签署(填写控件+签名),5,7500
个人版,小程序,合同与签署,合同管理(列表/查看/撤销/拒签),4,6000
个人版,小程序,合同与签署,合同下载与申请出证(证据链报告),3,4500
个人版,小程序,合同与签署,合同分享（可分享给微信好友或群组预览等）,3,4500
个人版,小程序,合同与签署,合同提醒设置（提醒签署时间、履约时间等）,3,4500
个人版,小程序,合同与签署,合同条款智能提醒（对关键条款进行醒目提示）,4,6000
个人版,小程序,合同与签署,小借条-结清/解除流程,4,6000
个人版,小程序,合同与签署,合同存证与法律服务入口,1,1500
个人版,小程序,辅助功能,常见问题解答（FAQ）模块,5,7500
个人版,小程序,辅助功能,在线客服实时咨询,5,7500
个人版,H5,移动端适配,手机号+验证码登录,2,3000
个人版,H5,移动端适配,实名认证(复用小程序逻辑),2,3000
个人版,H5,移动端适配,合同签署(复用小程序逻辑),3,4500
个人版,H5,移动端适配,合同管理列表(复用小程序逻辑),2,3000
个人版,H5,移动端适配,合同分享（复用小程序逻辑）,3,4500
个人版,H5,移动端适配,合同提醒设置（复用小程序逻辑）,3,4500
个人版,小计,,93,139500
企业版,PC Web,账户与认证,多通道企业认证(含微信支付商户号),10,15000
企业版,PC Web,账户与认证,企业信息变更-子流程细化,8,12000
企业版,PC Web,账户与认证,超级管理员与法人管理,7,10500
企业版,PC Web,账户与认证,解绑企业微信企业,4,6000
企业版,PC Web,集团管理,集团组织构建与管理,10,15000
企业版,PC Web,集团管理,子公司账户关联与管理,8,12000
企业版,PC Web,集团管理,集团资源与权限管控,8,12000
企业版,PC Web,组织与权限,组织架构管理(部门/员工),12,18000
企业版,PC Web,组织与权限,员工离职交接,10,15000
企业版,PC Web,组织与权限,角色与权限体系(预设/自定义/数据权限),15,22500
企业版,PC Web,组织与权限,部门间数据隔离与共享设置,10,15000
企业版,PC Web,组织与权限,员工临时权限授予与管理,8,12000
企业版,PC Web,组织与权限,审批流引擎(图形化配置+场景绑定),25,37500
企业版,PC Web,合同模板,模板制作(控件拖拽/流程配置),12,18000
企业版,PC Web,合同模板,企业模板库管理,7,10500
企业版,PC Web,合同模板,模板分类与标签管理,6,9000
企业版,PC Web,合同模板,模板版本管理与回滚,8,12000
企业版,PC Web,合同模板,模板体验流程(沙盒),8,12000
企业版,PC Web,合同模板,模板草稿箱,4,6000
企业版,PC Web,合同拟定,AI合同生成/审查/文本辅助,30,45000
企业版,PC Web,合同拟定,在线协同编辑/评论/版本追溯,12,18000
企业版,PC Web,合同拟定,合同条款智能推荐（基于过往合同及行业标准）,10,15000
企业版,PC Web,签署流程,发起合同(基础流程配置),6,9000
企业版,PC Web,签署流程,配置灵活签署顺序(串/并行),3,4500
企业版,PC Web,签署流程,配置抄送与关注方,2,3000
企业版,PC Web,签署流程,配置自动签署(本方),4,6000
企业版,PC Web,签署流程,签署提醒（短信、邮件、站内信等多渠道）,6,9000
企业版,PC Web,签署流程,批量签署发起,8,12000
企业版,PC Web,签署流程,签署进度实时跟踪可视化（甘特图等形式）,10,15000
企业版,PC Web,签署流程,动态签署方配置,8,12000
企业版,PC Web,签署流程,签署前审批,6,9000
企业版,PC Web,签署流程,本企业签署人与发起人分离,4,6000
企业版,PC Web,签署流程,他方自动签署授权,15,22500
企业版,PC Web,签署流程,允许签署方转他人处理,5,7500
企业版,PC Web,场景化签署,一码多签方案,12,18000
企业版,PC Web,场景化签署,视频会议签方案,15,22500
企业版,PC Web,场景化签署,战略会议签方案,15,22500
企业版,PC Web,场景化签署,招投标文件签署方案,15,22500
企业版,PC Web,场景化签署,供应链协同签署方案,15,22500
企业版,PC Web,归档与管理,合同类型自定义与管理,6,9000
企业版,PC Web,归档与管理,多维度智能检索/自然语言搜索,15,22500
企业版,PC Web,归档与管理,合同全链路视图/履约管理,15,22500
企业版,PC Web,归档与管理,合同到期提醒与续约流程引导,8,12000
企业版,PC Web,归档与管理,合同数据备份与恢复,10,15000
企业版,PC Web,归档与管理,批量清理失效文件,4,6000
企业版,PC Web,合同后处理,合同验签,8,12000
企业版,PC Web,合同后处理,合同对比,10,15000
企业版,PC Web,合同后处理,合同作废/解除流程,9,13500
企业版,PC Web,合同后处理,合同争议解决指引（提供相关法律条款及案例参考）,8,12000
企业版,PC Web,合同后处理,申请公证处核验报告,8,12000
企业版,PC Web,印章管理,企业印章类型细分,9,13500
企业版,PC Web,印章管理,印章授权与用印日志审计,8,12000
企业版,PC Web,印章管理,印章使用频率统计与分析,6,9000
企业版,PC Web,印章管理,模板用印免审批授权,5,7500
企业版,PC Web,印章管理,骑缝章配置,5,7500
企业版,PC Web,印章管理,印章挂失与解挂功能,5,7500
企业版,PC Web,企业设置,拓展服务开关化管理,5,7500
企业版,PC Web,计费与开放平台,费用中心(套餐/订单/发票),15,22500
企业版,PC Web,计费与开放平台,开放平台(API/SDK/Webhook)管理,20,30000
企业版,PC Web,安全与合规,国密算法(SM2/3/4)支持,12,18000
企业版,PC Web,安全与合规,区块链存证对接,15,22500
企业版,PC Web,小计,,611,916500
企业版,小程序,移动端核心,切换企业/查看个人信息,3,4500
企业版,小程序,移动端核心,统一待办中心(待签/待审),5,7500
企业版,小程序,移动端核心,待办事项分类筛选,4,6000
企业版,小程序,移动端核心,发起合同(模板/文件),4,6000
企业版,小程序,移动端核心,合同签署与管理,5,7500
企业版,小程序,移动端核心,用印/发起/作废等审批,5,7500
企业版,小程序,移动端核心,合同分享（适配小程序端）,3,4500
企业版,小程序,移动端核心,合同提醒设置（适配小程序端）,3,4500
企业版,H5,移动端核心,H5端适配(复用小程序功能),8,12000
企业版,H5,移动端核心,合同分享（适配H5端）,3,4500
企业版,H5,移动端核心,合同提醒设置（适配H5端）,3,4500
企业版,小计,,46,69000
业务运营后台,PC Web,用户管理,个人/企业用户查询与管理,8,12000
业务运营后台,PC Web,用户管理,特殊申诉审核(更名/认证失败),6,9000
业务运营后台,PC Web,用户管理,用户活跃度分析与统计,8,12000
业务运营后台,PC Web,用户管理,用户流失预警功能,10,15000
业务运营后台,PC Web,用户管理,用户行为日志审计,7,10500
业务运营后台,PC Web,合同与内容管理,官方模板市场管理,10,15000
业务运营后台,PC Web,合同与内容管理,合同数据统计报表生成（多维度）,10,15000
业务运营后台,PC Web,AI能力管理,AI配置与模型管理,8,12000
业务运营后台,PC Web,AI能力管理,AI知识库管理(用于RAG),15,22500
业务运营后台,PC Web,AI能力管理,AI任务审计(调用日志/分析),7,10500
业务运营后台,PC Web,AI能力管理,AI模型优化建议反馈,8,12000
业务运营后台,PC Web,财务管理,订单查询与管理,7,10500
业务运营后台,PC Web,财务管理,开票审核与处理,8,12000
业务运营后台,PC Web,财务管理,财务数据分析与可视化（营收、成本等）,10,15000
业务运营后台,PC Web,财务管理,套餐包管理,6,9000
业务运营后台,PC Web,财务管理,优惠券/代金券系统,8,12000
业务运营后台,PC Web,系统管理,平台看板与服务监控,12,18000
业务运营后台,PC Web,系统管理,系统日志管理与查询,8,12000
业务运营后台,PC Web,系统管理,系统性能监控与优化建议,10,15000
业务运营后台,PC Web,系统管理,平台公告与消息推送管理,6,9000
业务运营后台,PC Web,系统管理,后台操作员权限管理,8,12000
业务运营后台,PC Web,风控与安全,风险事件监控与告警,12,18000
业务运营后台,PC Web,风控与安全,敏感词库管理,5,7500
业务运营后台,小计,,203,304500
,,,,,
,,总计,,953,1429500 