# 集团组织管理常见问题

> 导航路径: 常见问题 > 企业微信端常见问题 > 集团组织管理常见问题
> 来源: https://qian.tencent.com/document/85697/
> 抓取时间: 2025-06-15 16:34:16

---

### 为什么会提示“集团主企业审批人暂无法进行审批”？ 

如果加入了集团组织的子企业使用的是**企业微信审批流引擎** ，在该企业的**企业设置 > 扩展服务 > 审批流程配置**中审批流示意模块会提示：集团主企业审批人暂无法进行审批。

这是因为子企业在电子签中使用的是**企业微信审批流引擎** ，通过**企业微信审批流引擎** 发起的审批请求只能通知到**本企业微信** 中的企业成员，无法通知另外一个企业微信企业。（主企业的审批应用中无法接收到子企业发起的审批通知）

您可以将子企业的审批流引擎调整为**腾讯电子签内置审批流** 来解决该问题，如何调整审批流引擎请参见 [审批流配置](https://qian.tencent.com/document/77772)。

### 我在主企业中签署一份子企业的合同时，发起的用印申请由谁来审批？

根据主企业使用的审批流引擎情况，该场景的审批逻辑有所不同： 

**主企业使用的审批流引擎**| **审批人**  
---|---  
企业微信审批流引擎-默认模式| 主企业中具有子企业印章管理权限的人。  
企业微信审批流引擎-自定义模式| 请参见 [如何为不同子企业的用印申请配置不同的审批流程？](https://qian.tencent.com/document/79407)﻿  
腾讯电子签内置审批流引擎| 主企业中具有子企业印章管理权限的人、子企业印章管理岗人员。  
  
如何为主企业中的员工分配**子企业印章管理权限** ，请参见****[ 集团角色管理](https://qian.tencent.com/document/86710)。

### 我在子企业中签署本企业的合同时，发起的用印申请由谁来审批？

根据子企业使用的审批流引擎情况，该场景的审批逻辑有所不同：

**子企业使用的审批流引擎**| **审批人**  
---|---  
企业微信审批流引擎| 子企业印章管理岗人员。  
腾讯电子签内置审批流引擎| 主企业中具有子企业印章管理权限的人、子企业印章管理岗人员。  
  
如何为主企业中的员工分配**子企业印章管理权限** ，请参见 [集团角色管理](https://qian.tencent.com/document/86710)。