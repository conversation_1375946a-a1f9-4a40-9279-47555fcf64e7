"""
腾讯千帆文档爬虫 - 主启动脚本
"""

import argparse
import sys
import os
from crawler import TencentQianCrawler

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='腾讯千帆文档爬虫 - 抓取并转换文档为Markdown格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 使用默认设置运行
  python main.py -p 50             # 限制抓取50个页面
  python main.py -w 5              # 使用5个并发线程
  python main.py -p 100 -w 3       # 抓取100个页面，使用3个线程
  python main.py --reset           # 重置进度，从头开始
  python main.py --report          # 只生成抓取报告
  python main.py --check           # 检查URL完成情况
        """
    )
    
    parser.add_argument(
        '-p', '--pages',
        type=int,
        default=None,
        help='最大页面数量 (默认: 无限制)'
    )
    
    parser.add_argument(
        '-w', '--workers',
        type=int,
        default=3,
        help='并发线程数 (默认: 3)'
    )
    
    parser.add_argument(
        '--reset',
        action='store_true',
        help='重置进度，从头开始爬取'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='只生成抓取报告，不进行抓取'
    )
    
    parser.add_argument(
        '--check',
        action='store_true',
        help='检查URL完成情况'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='./download',
        help='输出目录 (默认: ./download)'
    )
    
    args = parser.parse_args()
    
    try:
        # 创建爬虫实例
        crawler = TencentQianCrawler()
        
        # 处理特殊命令
        if args.report:
            generate_report(crawler)
            return
            
        if args.check:
            check_completion(crawler)
            return
        
        # 显示配置信息
        print("腾讯千帆文档爬虫 - 升级版")
        print("="*50)
        print(f"输出目录: {args.output_dir}")
        print(f"最大页面数: {args.pages or '无限制'}")
        print(f"并发线程数: {args.workers}")
        print(f"重置进度: {'是' if args.reset else '否'}")
        
        # 显示当前状态
        url_stats = crawler.url_manager.get_statistics()
        print(f"当前状态:")
        print(f"  发现URL: {url_stats['total_urls']}")
        print(f"  已完成: {url_stats['completed_urls']}")
        print(f"  失败: {url_stats['failed_urls']}")
        print(f"  待处理: {url_stats['pending_urls']}")
        print(f"  完成率: {url_stats['completion_rate']:.2f}%")
        print("="*50)
        
        # 重置进度（如果需要）
        if args.reset:
            crawler.url_manager.reset()
            crawler.url_manager.add_url(crawler.start_url)
            print("已重置进度，重新添加起始URL")
        
        # 确认开始
        if not args.reset and url_stats['total_urls'] > 0:
            print("检测到已有抓取进度，将继续之前的抓取...")
        
        try:
            confirm = input("是否开始爬取? (y/N): ")
            if confirm.lower() not in ['y', 'yes', '是']:
                print("取消操作")
                return
        except KeyboardInterrupt:
            print("\n取消操作")
            return
        
        # 开始爬取
        crawler.run(
            max_pages=args.pages,
            max_workers=args.workers
        )
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def generate_report(crawler):
    """生成抓取报告"""
    print("生成抓取报告...")
    report = crawler.url_manager.export_report()
    
    # 显示统计信息
    stats = crawler.url_manager.get_statistics()
    print("\n" + "="*50)
    print("抓取报告")
    print("="*50)
    print(f"总URL数量: {stats['total_urls']}")
    print(f"已完成数量: {stats['completed_urls']}")
    print(f"失败数量: {stats['failed_urls']}")
    print(f"待处理数量: {stats['pending_urls']}")
    print(f"完成率: {stats['completion_rate']:.2f}%")
    
    if stats['pending_urls'] > 0:
        print(f"\n还有 {stats['pending_urls']} 个URL待处理")
        print("可以运行 python main.py 继续抓取")
    else:
        print("\n✅ 所有URL都已处理完成！")


def check_completion(crawler):
    """检查完成情况"""
    print("检查URL完成情况...")
    
    stats = crawler.url_manager.get_statistics()
    all_urls = crawler.url_manager.all_urls
    completed_urls = crawler.url_manager.completed_urls
    failed_urls = crawler.url_manager.failed_urls
    pending_urls = crawler.url_manager.get_pending_urls()
    
    print("\n" + "="*50)
    print("URL完成情况检查")
    print("="*50)
    print(f"总发现URL: {len(all_urls)}")
    print(f"已完成URL: {len(completed_urls)}")
    print(f"失败URL: {len(failed_urls)}")
    print(f"待处理URL: {len(pending_urls)}")
    print(f"完成率: {stats['completion_rate']:.2f}%")
    
    # 显示待处理的URL
    if pending_urls:
        print(f"\n待处理的URL ({len(pending_urls)}个):")
        for i, url in enumerate(pending_urls[:10], 1):
            print(f"  {i}. {url}")
        if len(pending_urls) > 10:
            print(f"  ... 还有 {len(pending_urls) - 10} 个")
    
    # 显示失败的URL
    if failed_urls:
        print(f"\n失败的URL ({len(failed_urls)}个):")
        for i, url in enumerate(list(failed_urls)[:5], 1):
            metadata = crawler.url_manager.url_metadata.get(url, {})
            error = metadata.get('error', '未知错误')
            print(f"  {i}. {url} (错误: {error})")
        if len(failed_urls) > 5:
            print(f"  ... 还有 {len(failed_urls) - 5} 个")
    
    # 检查一致性
    print(f"\n一致性检查:")
    total_check = len(completed_urls) + len(failed_urls) + len(pending_urls)
    if total_check == len(all_urls):
        print("✅ URL状态一致性检查通过")
    else:
        print(f"❌ URL状态不一致: 总数={len(all_urls)}, 分类总数={total_check}")


def check_dependencies():
    """检查依赖包"""
    # 包名和实际导入名的映射
    package_imports = {
        'requests': 'requests',
        'beautifulsoup4': 'bs4', 
        'html2text': 'html2text',
        'lxml': 'lxml',
        'jieba': 'jieba',
        'tqdm': 'tqdm'
    }
    
    missing_packages = []
    
    for package_name, import_name in package_imports.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查依赖
    check_dependencies()
    
    # 运行主程序
    main()