#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Selenium的腾讯千帆爬虫 - 支持JavaScript渲染和自动展开菜单
"""

import os
import re
import time
import json
import random
import logging
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

from bs4 import BeautifulSoup
import requests

from config import *
from utils import *
from url_manager import URLManager


class SeleniumTencentQianCrawler:
    """基于Selenium的腾讯千帆爬虫类"""
    
    def __init__(self, headless=True, use_selenium=True):
        self.base_url = BASE_URL
        self.start_url = START_URL
        self.output_dir = OUTPUT_DIR
        self.headless = headless
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        
        # URL管理器
        self.url_manager = URLManager(self.output_dir, self.base_url)
        
        # 会话管理
        self.session = requests.Session()
        self.setup_session()
        
        # Selenium驱动
        self.driver = None
        if self.use_selenium:
            self.setup_selenium()
        
        # 内容处理
        import html2text
        self.html2text = html2text.HTML2Text()
        self.setup_html2text()
        
        # 设置日志
        self.setup_logging()
        
        # 统计信息
        self.stats = {
            'total_found': 0,
            'total_crawled': 0,
            'total_failed': 0,
            'total_saved': 0
        }
        
        # 初始化起始URL
        self.url_manager.add_url(self.start_url)
        
    def setup_session(self):
        """设置会话"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def setup_selenium(self):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            self.logger.warning("Selenium不可用，将使用普通HTTP请求")
            self.use_selenium = False
            return
            
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 优化选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript-harmony-shipping')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            
            # 设置用户代理
            chrome_options.add_argument(f'--user-agent={random.choice(REQUEST_CONFIG["user_agents"])}')
            
            # 设置窗口大小
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 创建驱动
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            self.logger.info("Selenium WebDriver初始化成功")
            
        except Exception as e:
            self.logger.error(f"Selenium初始化失败: {str(e)}")
            self.use_selenium = False
            
    def setup_html2text(self):
        """设置HTML转Markdown"""
        self.html2text.ignore_links = False
        self.html2text.ignore_images = True
        self.html2text.ignore_emphasis = False
        self.html2text.body_width = 0
        self.html2text.unicode_snob = True
        
    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.output_dir, 'selenium_crawler.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('selenium_crawler')
        
    def expand_all_menus(self, max_attempts=3):
        """自动展开所有折叠的菜单"""
        if not self.use_selenium or not self.driver:
            return False
            
        expanded_count = 0
        
        for attempt in range(max_attempts):
            try:
                # 查找所有折叠的菜单项
                collapsed_items = self.driver.find_elements(
                    By.CSS_SELECTOR, 
                    '.menu__list-item--collapsed .menu__link--sublist-caret'
                )
                
                if not collapsed_items:
                    self.logger.info(f"第{attempt + 1}轮：没有找到更多折叠菜单")
                    break
                    
                self.logger.info(f"第{attempt + 1}轮：找到 {len(collapsed_items)} 个折叠菜单")
                
                # 点击展开每个折叠项
                for item in collapsed_items:
                    try:
                        # 滚动到元素可见
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", item)
                        time.sleep(0.5)
                        
                        # 点击展开
                        item.click()
                        expanded_count += 1
                        time.sleep(0.5)  # 等待展开动画
                        
                    except Exception as e:
                        self.logger.debug(f"点击菜单项失败: {str(e)}")
                        continue
                        
                # 等待页面更新
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"展开菜单时发生错误: {str(e)}")
                break
                
        self.logger.info(f"总共展开了 {expanded_count} 个菜单项")
        return expanded_count > 0
        
    def extract_all_sidebar_links_selenium(self, url):
        """使用Selenium提取侧边栏所有链接"""
        if not self.use_selenium or not self.driver:
            return []
            
        try:
            self.logger.info(f"使用Selenium访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待侧边栏加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".theme-doc-sidebar-container, nav[aria-label*='侧边栏']"))
                )
            except TimeoutException:
                self.logger.warning("侧边栏加载超时")
            
            # 自动展开所有折叠菜单
            self.expand_all_menus()
            
            # 获取页面源码
            html = self.driver.page_source
            
            # 解析链接
            soup = BeautifulSoup(html, 'lxml')
            links = set()
            
            # 查找侧边栏
            sidebar_selectors = [
                '.theme-doc-sidebar-container',
                'nav[aria-label*="侧边栏"]',
                'nav[aria-label*="sidebar"]',
                '.sidebar',
                '.menu'
            ]
            
            sidebar = None
            for selector in sidebar_selectors:
                sidebar = soup.select_one(selector)
                if sidebar:
                    break
                    
            if sidebar:
                # 提取所有链接
                for link in sidebar.find_all('a', href=True):
                    href = link['href']
                    if href and not href.startswith('#'):
                        full_url = urljoin(url, href)
                        if is_valid_url(full_url, self.base_url):
                            links.add(full_url)
                            
            # 额外查找文档链接模式
            doc_pattern = re.compile(r'/document/\d+')
            for link in soup.find_all('a', href=True):
                href = link['href']
                if href and doc_pattern.search(href):
                    full_url = urljoin(url, href)
                    if is_valid_url(full_url, self.base_url):
                        links.add(full_url)
                        
            self.logger.info(f"从 {url} 提取到 {len(links)} 个链接")
            return list(links)
            
        except Exception as e:
            self.logger.error(f"Selenium提取链接失败: {url} - {str(e)}")
            return []
            
    def discover_all_links_selenium(self):
        """使用Selenium发现所有链接"""
        self.logger.info("开始使用Selenium发现所有文档链接...")
        
        # 从起始页面开始
        sidebar_links = self.extract_all_sidebar_links_selenium(self.start_url)
        self.logger.info(f"从起始页面发现 {len(sidebar_links)} 个侧边栏链接")
        
        # 添加到URL管理器
        added_count = self.url_manager.add_urls_batch(sidebar_links, self.start_url)
        self.logger.info(f"新增 {added_count} 个URL到待抓取列表")
        
        # 尝试从其他页面发现更多链接
        sample_urls = list(self.url_manager.all_urls)[:3]  # 取前3个URL作为样本
        
        for url in sample_urls:
            if url == self.start_url:
                continue
                
            self.logger.info(f"从样本页面发现链接: {url}")
            links = self.extract_all_sidebar_links_selenium(url)
            if links:
                new_added = self.url_manager.add_urls_batch(links, url)
                if new_added > 0:
                    self.logger.info(f"从 {url} 新增 {new_added} 个链接")
                    
            # 避免请求过快
            time.sleep(random.uniform(2, 4))
            
        # 保存发现的所有URL
        self.url_manager.save_urls()
        
        final_stats = self.url_manager.get_statistics()
        self.logger.info(f"Selenium链接发现完成，总共发现 {final_stats['total_urls']} 个URL")
        
    def fetch_page_content(self, url):
        """获取页面内容（优先使用Selenium）"""
        if self.use_selenium and self.driver:
            return self.fetch_page_selenium(url)
        else:
            return self.fetch_page_requests(url)
            
    def fetch_page_selenium(self, url):
        """使用Selenium获取页面内容"""
        try:
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待主要内容加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".theme-doc-markdown, .markdown, article"))
                )
            except TimeoutException:
                self.logger.warning(f"主要内容加载超时: {url}")
            
            html = self.driver.page_source
            self.logger.info(f"Selenium成功获取页面: {url}")
            return True, html
            
        except Exception as e:
            self.logger.error(f"Selenium获取页面失败: {url} - {str(e)}")
            return False, str(e)
            
    def fetch_page_requests(self, url, retries=None):
        """使用requests获取页面内容"""
        if retries is None:
            retries = REQUEST_CONFIG['retry_times']
            
        for attempt in range(retries + 1):
            try:
                headers = {
                    'User-Agent': random.choice(REQUEST_CONFIG['user_agents']),
                    'Accept-Charset': 'utf-8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
                
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=REQUEST_CONFIG['timeout']
                )
                
                response.encoding = 'utf-8'
                
                if response.status_code == 200:
                    self.logger.info(f"Requests成功获取页面: {url}")
                    content = response.text
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='ignore')
                    return True, content
                elif response.status_code == 404:
                    self.logger.warning(f"页面不存在: {url}")
                    return False, "404 Not Found"
                else:
                    self.logger.warning(f"HTTP错误 {response.status_code}: {url}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{retries + 1}): {url} - {str(e)}")
                
                if attempt < retries:
                    wait_time = random.uniform(2, 5) * (attempt + 1)
                    time.sleep(wait_time)
                    
        return False, "Max retries exceeded"
        
    def extract_content(self, html, url):
        """提取页面内容"""
        soup = BeautifulSoup(html, 'lxml')
        
        # 提取标题
        title = self.extract_title(soup)
        
        # 提取主要内容
        main_content = self.extract_main_content(soup)
        
        # 转换为Markdown
        markdown_content = self.convert_to_markdown(main_content)
        
        return {
            'url': url,
            'title': title,
            'content': markdown_content,
            'links': []
        }
        
    def extract_title(self, soup):
        """提取页面标题"""
        title_selectors = [
            'h1',
            '.theme-doc-markdown h1',
            'title',
            '.docTitle_node_modules',
            '[data-testid="doc-title"]'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                if title and title != "文档中心":
                    return title
                    
        return "未知标题"
        
    def extract_main_content(self, soup):
        """提取主要内容"""
        content_selectors = [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR',
            'main .container'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                self.clean_content(content_elem)
                return str(content_elem)
                
        body = soup.find('body')
        if body:
            self.clean_content(body)
            return str(body)
            
        return str(soup)
        
    def clean_content(self, content_element):
        """清理内容元素"""
        unwanted_selectors = [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb',
            '.pagination', '.feedback', '.advertisement',
            '[class*="ad-"]', '[id*="ad-"]'
        ]
        
        for selector in unwanted_selectors:
            for elem in content_element.select(selector):
                elem.decompose()
                
    def convert_to_markdown(self, html_content):
        """转换HTML为Markdown"""
        try:
            markdown = self.html2text.handle(html_content)
            markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
            return markdown.strip()
        except Exception as e:
            self.logger.error(f"HTML转Markdown失败: {str(e)}")
            return html_content
            
    def save_content(self, content_info):
        """保存内容到文件"""
        try:
            filename = generate_filename(content_info['title'], content_info['url'])
            filepath = os.path.join(self.output_dir, filename)
            
            file_content = f"# {content_info['title']}\n\n"
            file_content += f"> 来源: {content_info['url']}\n"
            file_content += f"> 抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            file_content += "---\n\n"
            file_content += content_info['content']
            
            if isinstance(file_content, bytes):
                file_content = file_content.decode('utf-8', errors='ignore')
            
            with open(filepath, 'w', encoding='utf-8-sig', newline='\n') as f:
                f.write(file_content)
                
            safe_filename = os.path.basename(filepath)
            if isinstance(safe_filename, bytes):
                safe_filename = safe_filename.decode('utf-8', errors='replace')
            
            self.logger.info(f"保存文件: {safe_filename}")
            self.stats['total_saved'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            return False
            
    def crawl_single_page(self, url):
        """爬取单个页面"""
        try:
            random_sleep(
                REQUEST_CONFIG['delay_min'],
                REQUEST_CONFIG['delay_max']
            )
            
            success, html_content = self.fetch_page_content(url)
            
            if not success:
                self.url_manager.mark_failed(url, html_content)
                self.stats['total_failed'] += 1
                return False
                
            content_info = self.extract_content(html_content, url)
            save_success = self.save_content(content_info)
            
            if save_success:
                self.url_manager.mark_completed(url)
                self.stats['total_crawled'] += 1
            else:
                self.url_manager.mark_failed(url, "保存文件失败")
                
            return save_success
            
        except Exception as e:
            self.logger.error(f"爬取页面失败: {url} - {str(e)}")
            self.url_manager.mark_failed(url, str(e))
            self.stats['total_failed'] += 1
            return False
            
    def run(self, max_pages=None, max_workers=None, discover_links=True):
        """运行Selenium爬虫"""
        if max_workers is None:
            max_workers = 1 if self.use_selenium else PROGRESS_CONFIG['max_concurrent']
            
        self.logger.info(f"开始Selenium爬取，最大页面数: {max_pages or '无限制'}, 最大线程数: {max_workers}")
        
        try:
            # 第一步：发现所有链接
            if discover_links:
                if self.use_selenium:
                    self.discover_all_links_selenium()
                else:
                    self.logger.warning("Selenium不可用，使用普通方式发现链接")
                    # 这里可以调用普通的链接发现方法
            
            # 第二步：抓取所有页面
            pbar = tqdm(desc="抓取进度", unit="页", dynamic_ncols=True)
            
            try:
                if self.use_selenium:
                    # Selenium模式：单线程处理
                    while not self.url_manager.is_all_completed():
                        if max_pages and self.stats['total_crawled'] >= max_pages:
                            self.logger.info(f"已达到最大页面数: {max_pages}")
                            break
                            
                        pending_urls = self.url_manager.get_pending_urls()
                        if not pending_urls:
                            break
                            
                        url = pending_urls[0]
                        result = self.crawl_single_page(url)
                        pbar.update(1)
                        
                        url_stats = self.url_manager.get_statistics()
                        pbar.set_postfix({
                            '成功': self.stats['total_crawled'],
                            '失败': self.stats['total_failed'],
                            '待处理': url_stats['pending_urls']
                        })
                        
                        if self.stats['total_crawled'] % PROGRESS_CONFIG['save_interval'] == 0:
                            self.url_manager.save_urls()
                else:
                    # 普通模式：多线程处理
                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        while not self.url_manager.is_all_completed():
                            if max_pages and self.stats['total_crawled'] >= max_pages:
                                break
                                
                            pending_urls = self.url_manager.get_pending_urls()
                            current_batch = pending_urls[:max_workers]
                            
                            if not current_batch:
                                break
                                
                            futures = [
                                executor.submit(self.crawl_single_page, url)
                                for url in current_batch
                            ]
                            
                            for future in as_completed(futures):
                                result = future.result()
                                pbar.update(1)
                                
                                url_stats = self.url_manager.get_statistics()
                                pbar.set_postfix({
                                    '成功': self.stats['total_crawled'],
                                    '失败': self.stats['total_failed'],
                                    '待处理': url_stats['pending_urls']
                                })
                                
                            if self.stats['total_crawled'] % PROGRESS_CONFIG['save_interval'] == 0:
                                self.url_manager.save_urls()
                                
            except KeyboardInterrupt:
                self.logger.info("用户中断爬取")
            except Exception as e:
                self.logger.error(f"爬取过程中发生错误: {str(e)}")
            finally:
                pbar.close()
                self.url_manager.save_urls()
                self.print_summary()
                
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("Selenium WebDriver已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebDriver失败: {str(e)}")
                
    def print_summary(self):
        """打印爬取摘要"""
        url_stats = self.url_manager.get_statistics()
        
        print("\n" + "="*50)
        print("Selenium爬取摘要")
        print("="*50)
        print(f"发现页面数: {url_stats['total_urls']}")
        print(f"成功爬取: {url_stats['completed_urls']}")
        print(f"爬取失败: {url_stats['failed_urls']}")
        print(f"保存文件: {self.stats['total_saved']}")
        print(f"待处理页面: {url_stats['pending_urls']}")
        print(f"完成率: {url_stats['completion_rate']:.2f}%")
        print("="*50)
        
        self.url_manager.export_report()
        
        if self.url_manager.is_all_completed():
            print("所有URL都已处理完成!")
        else:
            print("还有未完成的URL，可以重新运行继续抓取")


if __name__ == "__main__":
    # 检查Selenium是否可用
    if not SELENIUM_AVAILABLE:
        print("Selenium不可用，请安装: pip install selenium")
        print("同时需要下载ChromeDriver")
        exit(1)
    
    crawler = SeleniumTencentQianCrawler(headless=True, use_selenium=True)
    
    try:
        crawler.run(
            max_pages=None,
            max_workers=1,  # Selenium建议单线程
            discover_links=True
        )
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        crawler.cleanup() 