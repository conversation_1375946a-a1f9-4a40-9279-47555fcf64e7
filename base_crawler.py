#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础爬虫类 - 包含所有通用功能
消除代码重复，提供统一接口
"""

import os
import re
import time
import json
import random
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from abc import ABC, abstractmethod

from config import *
from utils import *
from url_manager import URLManager


class BaseCrawler(ABC):
    """基础爬虫类，包含所有通用功能"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.start_url = START_URL
        self.output_dir = OUTPUT_DIR
        self.page_output_dir = PAGE_OUTPUT_DIR
        
        # URL管理器
        self.url_manager = URLManager(self.output_dir, self.base_url)
        
        # 会话管理
        self.session = requests.Session()
        self.setup_session()
        
        # 内容处理
        import html2text
        self.html2text = html2text.HTML2Text()
        self.setup_html2text()
        
        # 设置日志
        self.setup_logging()
        
        # 统计信息
        self.stats = {
            'total_found': 0,
            'total_crawled': 0,
            'total_failed': 0,
            'total_saved': 0
        }
        
        # 初始化起始URL
        self.url_manager.add_url(self.start_url)
    
    def setup_session(self):
        """设置HTTP会话"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def setup_html2text(self):
        """设置HTML转Markdown"""
        self.html2text.ignore_links = False
        self.html2text.ignore_images = True
        self.html2text.ignore_emphasis = False
        self.html2text.body_width = 0
        self.html2text.unicode_snob = True
    
    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.output_dir, f'{self.__class__.__name__.lower()}.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(REQUEST_CONFIG['user_agents'])
    
    def fetch_page_requests(self, url, retries=None):
        """使用requests获取页面内容，支持最多3次重定向"""
        if retries is None:
            retries = REQUEST_CONFIG['retry_times']
            
        for attempt in range(retries + 1):
            try:
                headers = {
                    'User-Agent': self.get_random_user_agent(),
                    'Accept-Charset': 'utf-8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
                
                # 手动处理重定向，最多允许3次
                current_url = url
                redirect_count = 0
                max_redirects = 3
                redirect_chain = [url]
                
                while redirect_count <= max_redirects:
                    response = self.session.get(
                        current_url,
                        headers=headers,
                        timeout=REQUEST_CONFIG['timeout'],
                        allow_redirects=False  # 手动处理重定向
                    )
                    
                    # 检查是否是重定向状态码
                    if response.status_code in [301, 302, 303, 307, 308]:
                        redirect_url = response.headers.get('Location')
                        if redirect_url:
                            # 处理相对URL
                            if not redirect_url.startswith('http'):
                                redirect_url = urljoin(current_url, redirect_url)
                            
                            redirect_count += 1
                            redirect_chain.append(redirect_url)
                            
                            if redirect_count > max_redirects:
                                self.logger.warning(f"重定向次数超过限制({max_redirects})，放弃抓取: {' -> '.join(redirect_chain)}")
                                return False, f"Too many redirects (>{max_redirects})"
                            
                            # 检查域名安全性
                            redirect_domain = urlparse(redirect_url).netloc
                            if not (redirect_domain.endswith('.tencent.com') or redirect_domain == 'qian.tencent.com'):
                                self.logger.warning(f"检测到可疑重定向域名，放弃抓取: {current_url} -> {redirect_url}")
                                return False, "Suspicious redirect domain"
                            
                            self.logger.info(f"重定向 {redirect_count}/{max_redirects}: {current_url} -> {redirect_url}")
                            current_url = redirect_url
                            continue
                    
                    # 非重定向状态码，处理响应
                    response.encoding = 'utf-8'
                    
                    if response.status_code == 200:
                        if redirect_count > 0:
                            self.logger.info(f"经过{redirect_count}次重定向成功获取页面: {url} -> {current_url}")
                        else:
                            self.logger.info(f"成功获取页面: {url}")
                        
                        content = response.text
                        if isinstance(content, bytes):
                            content = content.decode('utf-8', errors='ignore')
                        return True, content
                    elif response.status_code == 404:
                        self.logger.warning(f"页面不存在: {current_url}")
                        return False, "404 Not Found"
                    else:
                        self.logger.warning(f"HTTP错误 {response.status_code}: {current_url}")
                        return False, f"HTTP {response.status_code}"
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{retries + 1}): {url} - {str(e)}")
                
                if attempt < retries:
                    wait_time = random.uniform(2, 5) * (attempt + 1)
                    time.sleep(wait_time)
                    
        return False, "Max retries exceeded"
    
    def extract_title(self, soup):
        """提取页面标题 - 通用方法"""
        title_selectors = [
            'h1',
            '.theme-doc-markdown h1',
            'title',
            '.docTitle_node_modules',
            '[data-testid="doc-title"]',
            '.markdown h1'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                if title and title != "文档中心":
                    return title
                    
        return "未知标题"
    
    def extract_breadcrumb_path(self, soup):
        """提取面包屑导航路径"""
        breadcrumb_selectors = [
            '.breadcrumb',
            '.breadcrumbs',
            '.nav-breadcrumb',
            '.theme-doc-breadcrumbs',
            '[aria-label="breadcrumb"]',
            '.breadcrumb-container',
            '.page-breadcrumb'
        ]
        
        for selector in breadcrumb_selectors:
            breadcrumb_elem = soup.select_one(selector)
            if breadcrumb_elem:
                # 提取所有链接文本
                links = breadcrumb_elem.find_all(['a', 'span', 'li'])
                path_parts = []
                seen_parts = set()  # 用于去重
                
                for link in links:
                    text = link.get_text().strip()
                    # 过滤掉"文档中心"、空文本和重复项
                    if text and text != "文档中心" and text != ">" and text != "/" and text not in seen_parts:
                        path_parts.append(text)
                        seen_parts.add(text)
                
                if len(path_parts) > 1:  # 至少要有2个层级
                    return path_parts
        
        # 如果没有找到面包屑，尝试从URL路径推断
        return self.extract_path_from_url(soup)
    
    def extract_path_from_url(self, soup):
        """从URL路径推断层级结构"""
        # 这是一个备用方法，如果没有面包屑导航时使用
        title = self.extract_title(soup)
        if title and title != "未知标题":
            return [title]
        return []
    
    def generate_filename_from_breadcrumb(self, breadcrumb_path):
        """根据面包屑路径生成文件名"""
        if not breadcrumb_path:
            return "未知页面"
        
        # 连接路径，跳过"文档中心"
        filtered_path = [part for part in breadcrumb_path if part != "文档中心"]
        
        if not filtered_path:
            return "未知页面"
        
        # 使用下划线连接
        filename = "_".join(filtered_path)
        
        # 清理文件名中的非法字符
        forbidden_chars = FILE_CONFIG['forbidden_chars']
        for char in forbidden_chars:
            filename = filename.replace(char, FILE_CONFIG['replacement_char'])
        
        # 限制文件名长度
        max_length = FILE_CONFIG['max_filename_length']
        if len(filename) > max_length:
            filename = filename[:max_length]
        
        return filename
    
    def extract_main_content(self, soup):
        """提取主要内容 - 通用方法"""
        content_selectors = [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR',
            'main .container',
            '.tea-editable'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                self.clean_content(content_elem)
                return str(content_elem)
                
        # 如果没找到特定容器，返回body内容
        body = soup.find('body')
        if body:
            self.clean_content(body)
            return str(body)
            
        return str(soup)
    
    def clean_content(self, content_element):
        """清理内容元素 - 通用方法"""
        unwanted_selectors = [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb',
            '.pagination', '.feedback', '.advertisement',
            '[class*="ad-"]', '[id*="ad-"]',
            '.theme-doc-sidebar-container',
            '.tocCollapsible_ETCw',
            '.pagination-nav',
            '.feedback-panel'
        ]
        
        for selector in unwanted_selectors:
            for elem in content_element.select(selector):
                elem.decompose()
    
    def convert_to_markdown(self, html_content):
        """转换HTML为Markdown - 通用方法"""
        try:
            markdown = self.html2text.handle(html_content)
            # 清理多余的空行
            markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
            return markdown.strip()
        except Exception as e:
            self.logger.error(f"HTML转Markdown失败: {str(e)}")
            return html_content
    
    def save_content(self, content_info):
        """保存内容到文件 - 使用面包屑路径命名"""
        try:
            # 根据面包屑路径生成文件名
            breadcrumb_path = content_info.get('breadcrumb_path', [])
            if breadcrumb_path:
                filename = self.generate_filename_from_breadcrumb(breadcrumb_path)
            else:
                # 备用方案：使用标题
                filename = content_info['title']
                # 清理文件名
                forbidden_chars = FILE_CONFIG['forbidden_chars']
                for char in forbidden_chars:
                    filename = filename.replace(char, FILE_CONFIG['replacement_char'])
            
            # 确保文件名是有效的UTF-8字符串
            if isinstance(filename, bytes):
                filename = filename.decode('utf-8', errors='ignore')
            
            # 添加扩展名
            filename += FILE_CONFIG['markdown_extension']
            
            # 确保页面输出目录存在
            if not os.path.exists(self.page_output_dir):
                os.makedirs(self.page_output_dir)
            
            # 确保文件名唯一
            filepath = os.path.join(self.page_output_dir, filename)
            filepath = ensure_unique_filename(filepath)
            
            # 准备文件内容
            file_content = f"# {content_info['title']}\n\n"
            
            # 添加面包屑信息
            if breadcrumb_path:
                breadcrumb_str = " > ".join(breadcrumb_path)
                file_content += f"> 导航路径: {breadcrumb_str}\n"
            
            file_content += f"> 来源: {content_info['url']}\n"
            file_content += f"> 抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            file_content += "---\n\n"
            file_content += content_info['content']
            
            # 确保内容是字符串格式
            if isinstance(file_content, bytes):
                file_content = file_content.decode('utf-8', errors='ignore')
            
            # 保存文件（使用UTF-8编码）
            with open(filepath, 'w', encoding='utf-8', newline='\n') as f:
                f.write(file_content)
                
            safe_filename = os.path.basename(filepath)
            if isinstance(safe_filename, bytes):
                safe_filename = safe_filename.decode('utf-8', errors='replace')
            
            self.logger.info(f"保存文件: {safe_filename}")
            self.stats['total_saved'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            return False
    
    def extract_content(self, html, url):
        """提取页面内容 - 通用方法"""
        soup = BeautifulSoup(html, 'lxml')
        
        # 提取标题
        title = self.extract_title(soup)
        
        # 提取面包屑路径
        breadcrumb_path = self.extract_breadcrumb_path(soup)
        
        # 提取主要内容
        main_content = self.extract_main_content(soup)
        
        # 转换为Markdown
        markdown_content = self.convert_to_markdown(main_content)
        
        return {
            'url': url,
            'title': title,
            'content': markdown_content,
            'breadcrumb_path': breadcrumb_path,
            'links': []
        }
    
    def crawl_single_page(self, url):
        """爬取单个页面 - 通用方法"""
        try:
            # 随机等待
            random_sleep(
                REQUEST_CONFIG['delay_min'],
                REQUEST_CONFIG['delay_max']
            )
            
            # 获取页面内容
            success, html_content = self.fetch_page(url)
            
            if not success:
                self.url_manager.mark_failed(url, html_content)
                self.stats['total_failed'] += 1
                return False
                
            # 提取内容
            content_info = self.extract_content(html_content, url)
            
            # 保存内容
            save_success = self.save_content(content_info)
            
            # 更新状态
            if save_success:
                self.url_manager.mark_completed(url)
                self.stats['total_crawled'] += 1
            else:
                self.url_manager.mark_failed(url, "保存文件失败")
                
            return save_success
            
        except Exception as e:
            self.logger.error(f"爬取页面失败: {url} - {str(e)}")
            self.url_manager.mark_failed(url, str(e))
            self.stats['total_failed'] += 1
            return False
    
    def print_summary(self):
        """打印爬取摘要 - 通用方法"""
        url_stats = self.url_manager.get_statistics()
        
        print("\n" + "="*50)
        print(f"{self.__class__.__name__} 爬取摘要")
        print("="*50)
        print(f"发现页面数: {url_stats['total_urls']}")
        print(f"成功爬取: {url_stats['completed_urls']}")
        print(f"爬取失败: {url_stats['failed_urls']}")
        print(f"保存文件: {self.stats['total_saved']}")
        print(f"待处理页面: {url_stats['pending_urls']}")
        print(f"完成率: {url_stats['completion_rate']:.2f}%")
        print("="*50)
        
        # 生成详细报告
        self.url_manager.export_report()
        
        if self.url_manager.is_all_completed():
            print("所有URL都已处理完成!")
        else:
            print("还有未完成的URL，可以重新运行继续抓取")
    
    # 抽象方法，子类必须实现
    @abstractmethod
    def discover_links(self):
        """发现链接 - 子类实现"""
        pass
    
    @abstractmethod
    def fetch_page(self, url):
        """获取页面 - 子类实现"""
        pass
    
    @abstractmethod
    def run(self, max_pages=None, max_workers=None, discover_links=True):
        """运行爬虫 - 子类实现"""
        pass