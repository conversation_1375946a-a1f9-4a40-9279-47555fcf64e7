# 路浩AI电子签 - 系统模块与链路设计（V4.2）

## 1. 系统总体架构与分层

路浩AI电子签平台采用先进的云原生微服务架构，确保系统的高可用、高并发和可扩展性。整体架构遵循领域驱动设计（DDD）思想，分为四个主要层次：

```mermaid
graph TD
    subgraph Layer_Frontend [前端应用层 (Frontend Applications)]
        direction LR
        A1[PC Web 管理端 (React + ShadCN)]
        A2[H5 移动端]
        A3[微信小程序]
        A4[嵌入式组件 (Embed SDK)]
    end

    subgraph Layer_Gateway [API网关层 (API Gateway)]
        B1[统一API网关 (Spring Cloud Gateway / Go-Kratos Gateway)]
    end

    subgraph Layer_Service [后台服务层 (Backend Microservices)]
        direction TB
        subgraph Domain_Core [核心业务域]
            C1[**合同服务 (contract-service)**<br>生命周期管理、模板、草稿]
            C2[**签署服务 (signing-service)**<br>签署流程执行、意愿认证、批量签署]
            C3[**印章服务 (seal-service)**<br>印章创建、管理、授权、用印日志]
        end

        subgraph Domain_Mgmt [企业管理域]
            C4[**账户服务 (account-service)**<br>个人/企业用户、实名认证]
            C5[**组织权限服务 (org-permission-service)**<br>组织架构、RBAC、集团管控]
            C6[**审批服务 (approval-service)**<br>独立的审批流引擎、用印/发文审批]
        end

        subgraph Domain_Support [业务支撑域]
            C7[**计费中心 (billing-service)**<br>套餐管理、订单、支付回调]
            C8[**消息中心 (message-service)**<br>短信、邮件、微信通知、Webhook]
            C9[**履约提醒服务 (fulfillment-service)**<br>履约节点管理、到期提醒]
        end

        subgraph Domain_AI [AI赋能域]
            C10[**AI服务 (ai-service)**<br>合同生成、审查、智能检索、OCR]
        end
    end

    subgraph Layer_Infra [基础设施层 (Infrastructure)]
        direction LR
        D1[**数据库**<br>MySQL, MongoDB, Elasticsearch]
        D2[**中间件**<br>Redis, RabbitMQ]
        D3[**存储**<br>MinIO / S3]
        D4[**可观测性**<br>Prometheus, ELK]
        D5[**安全设施**<br>CA/TSA, HSM, KMS]
    end

    Layer_Frontend --> B1
    B1 --> Layer_Service
    Layer_Service --> Layer_Infra
```

## 2. 核心模块说明

基于V3版本的设计，V4.2对服务进行了进一步的拆分与扩展，以支持更复杂的功能和更高的内聚性。

| 模块/服务名 | 核心职责 | V4.2 新增/强化功能 |
| :--- | :--- | :--- |
| **合同服务 (contract-service)** | 管理合同的元数据、草稿、版本、模板。 | 强化模板管理功能，增加官方模板市场；增加合同对比、履约计划关联等。 |
| **签署服务 (signing-service)** | 负责驱动整个签署流程，处理签署意愿认证。 | 从原合同服务中拆分，独立处理复杂的签署顺序（串行/并行）、自动签署、批量签署、骑缝章逻辑。 |
| **印章服务 (seal-service)** | 管理个人签名与企业印章，包括其生命周期和授权。 | 增加AI辅助的印章OCR识别与优化；强化用印日志的审计能力。 |
| **账户服务 (account-service)** | 统一管理个人与企业的基础信息和实名认证状态。 | 扩展对政务用户的支持；对接更多第三方认证源。 |
| **组织权限服务 (org-permission-service)** | 负责企业的组织架构、员工管理、RBAC角色权限体系。 | 新增集团管控模型，支持跨企业授权；增加"本人及下属"等更精细的数据权限。 |
| **审批服务 (approval-service)** | **[新增]** 独立的、通用的审批流引擎。 | 提供图形化的审批流配置界面，支持多级、会签、或签等复杂流程，服务于用印申请、合同发起审批等场景。 |
| **计费中心 (billing-service)** | **[新增]** 管理产品套餐、用户订单、支付与发票。 | 对接微信、支付宝、对公转账，处理支付回调，管理套餐余量，提供发票申请与下载功能。 |
| **消息中心 (message-service)** | 统一处理所有需要触达用户的通知。 | 强化Webhook机制，支持消息重试和签名验证，确保对客户业务系统通知的可靠性。 |
| **履约提醒服务 (fulfillment-service)** | **[新增]** 负责合同的后管理阶段。 | 支持用户设置关键履约节点（如付款、交付日），并通过消息中心在到期前发送提醒。 |
| **AI服务 (ai-service)** | 封装所有AI能力，为其他业务服务提供接口。 | 强化RAG能力，对接企业私有知识库；优化合同审查和生成模型；提供自然语言搜索合同的能力。 |

## 3. 核心系统链路图说明

以下Mermaid图描述了两个最核心的业务流程："AI合同生成与多方签署"和"需要审批的用印流程"，展示了各微服务之间的调用关系。

### 3.1 链路一：用户通过AI生成合同并发起多方签署

```mermaid
sequenceDiagram
    participant FE as 前端应用
    participant Gateway as API网关
    participant ContractSvc as 合同服务
    participant AISvc as AI服务
    participant SigningSvc as 签署服务
    participant MsgSvc as 消息中心

    FE->>Gateway: 1. 发起AI生成合同请求 (含对话历史)
    Gateway->>ContractSvc: 2. 转发请求
    ContractSvc->>AISvc: 3. 调用AI生成合同(GenerateContract)
    AISvc-->>ContractSvc: 4. 返回合同文本初稿
    ContractSvc-->>FE: 5. 返回合同草稿ID和内容
    
    Note right of FE: 用户在线编辑、确认合同内容<br/>并添加签署方、配置签署流程
    
    FE->>Gateway: 6. 确认发起签署(含签署方、流程信息)
    Gateway->>SigningSvc: 7. 创建签署任务(CreateSigningTask)
    SigningSvc->>ContractSvc: 8. 更新合同状态为"签署中"
    SigningSvc->>MsgSvc: 9. 请求发送签署通知 (To 签署人A)
    MsgSvc-->>签署人A: 10. 发送短信/微信通知
    
    Note right of FE: 签署人A完成签署...<br/>签署人B收到通知并完成签署...

    SigningSvc->>MsgSvc: 11. 请求发送合同完成通知 (To 所有参与方)
    MsgSvc-->>所有参与方: 12. 发送短信/微信通知
    SigningSvc->>ContractSvc: 13. 更新合同状态为"已完成"
    SigningSvc->>MsgSvc: 14. [Webhook] 推送合同完成事件
```

### 3.2 链路二：业务员申请使用"合同专用章"签署合同

```mermaid
sequenceDiagram
    participant FE as 前端应用
    participant Gateway as API网关
    participant SigningSvc as 签署服务
    participant ApprovalSvc as 审批服务
    participant SealSvc as 印章服务
    participant MsgSvc as 消息中心
    participant 印章管理员 as 印章管理员(审批人)

    FE->>Gateway: 1. 业务员在签署页选择"合同章"并发起
    Gateway->>SigningSvc: 2. 请求签署(ExecuteSign)
    SigningSvc->>SealSvc: 3. 检查用印权限(CheckSealPermission)
    SealSvc-->>SigningSvc: 4. 返回"无权限"，但告知需审批
    
    SigningSvc->>ApprovalSvc: 5. 创建用印审批申请(CreateApprovalRequest)
    ApprovalSvc->>MsgSvc: 6. 请求发送审批通知
    MsgSvc-->>印章管理员: 7. 发送待审批任务通知
    
    Note right of 印章管理员: 印章管理员在PC或手机上<br/>查看合同并点击"同意"
    
    印章管理员->>Gateway: 8. 提交审批意见(Approve)
    Gateway->>ApprovalSvc: 9. 转发审批意见
    ApprovalSvc->>SigningSvc: 10. 回调通知：用印申请已通过(ApprovalPassed)
    
    Note left of SigningSvc: 收到回调后，系统自动完成盖章
    
    SigningSvc->>SealSvc: 11. 记录用印日志(LogSealUsage)
    SigningSvc->>ContractSvc: 12. 更新合同签署状态
    SigningSvc-->>FE: 13. 返回签署成功
``` 