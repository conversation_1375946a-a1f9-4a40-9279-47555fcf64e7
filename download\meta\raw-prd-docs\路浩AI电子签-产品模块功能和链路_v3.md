# 路浩AI电子签 - 产品模块功能和链路（V3.0）

## 1. 账户与认证中心 (Account & AuthN/AuthZ Center)

**服务名: `account-service`**

该服务是整个平台的用户身份和权限管理中枢，采用微服务架构，独立部署。

### 1.1 个人用户体系

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `Register(phone, code)` | 1. 校验验证码是否正确。<br>2. 查询手机号是否已存在，若存在则执行登录逻辑，若不存在则创建新用户记录。<br>3. 初始化用户基础信息，状态为“未实名”。<br>4. 生成JWT Token返回给客户端。 | **-> 短信服务**: 发送/校验验证码。<br>**<-> 用户DB**: 读写`users`表。 |
| `KYC(userId, name, idCard, faceData)` | 1. 根据`userId`查找用户。<br>2. 调用第三方实名认证服务接口，传入姓名、身份证号、人脸数据。<br>3. 接收认证结果，若成功，则更新`users`表中的实名状态、姓名、身份证号等字段。<br>4. 为用户异步申请并绑定一个长期的个人数字证书。 | **-> 第三方认证服务**: 对接公安部/微信支付等实名认证接口。<br>**-> CA服务**: 异步申请个人数字证书。<br>**<-> 用户DB**: 更新`users`表。 |
| `UpdateMobile(userId, oldCode, newPhone, newCode)` | 1. 校验旧手机验证码或当前会话的人脸识别结果。<br>2. 校验新手机验证码。<br>3. 更新`users`表中的手机号字段。 | **-> 短信服务**: 校验验证码。<br>**<-> 用户DB**: 更新`users`表。 |

### 1.2 企业用户与RBAC体系

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `CreateEnterprise(userId, enterpriseInfo, authType)` | 1. 校验`userId`是否已实名。<br>2. 根据`authType`（认证类型）进入不同分支：<br>   - **法人授权**: 调用工商信息接口核验企业信息，生成授权链接，通过**消息中心**通知法人。<br>   - **对公打款**: 记录银行账户信息，生成待打款任务。<br>3. 创建企业记录，并将当前`userId`设为超管。 | **-> 第三方工商服务**: 核验企业信息。<br>**-> 消息中心**: 发送法人授权通知。<br>**<-> 企业DB**: 写入`enterprises`表、`enterprise_members`表。 |
| `AddEmployee(enterpriseId, employees)` | 1. 批量处理员工列表。<br>2. 对每个员工，检查手机号是否已注册平台。若未注册，创建个人用户记录。<br>3. 在`enterprise_members`表中插入员工记录，关联`enterpriseId`和`userId`，状态为“待激活”。<br>4. 通过**消息中心**发送激活邀请。 | **<-> 用户DB**: 查询/创建`users`表。<br>**<-> 企业DB**: 写入`enterprise_members`表。<br>**-> 消息中心**: 发送激活短信/邮件。 |
| `CheckPermission(userId, enterpriseId, permissionCode)` | 1. 根据`userId`和`enterpriseId`查询该员工所属的所有角色。<br>2. 查询这些角色关联的所有权限码。<br>3. 判断`permissionCode`是否存在于权限集合中，返回`true/false`。 | **<-> 企业DB**: 联表查询`enterprise_members`, `roles`, `role_permissions`。 |

## 2. 合同与签署中心 (Contract & Signing Center)

**服务名: `contract-service`**

负责合同全生命周期的核心业务逻辑。

### 2.1 合同拟定与发起

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `CreateFlowByFile(initiator, file, flowInfo)` | 1. 调用**文档中心**的`Upload(file)`接口，获取`fileId`。<br>2. 解析`flowInfo`中的签署方、控件、流程配置等信息。<br>3. 在`contracts`表中创建一条新合同记录，状态为“DRAFT”。<br>4. 将控件信息与签署方角色关联，存入`contract_components`表。<br>5. 若无审批，则将合同状态更新为“SIGNING”，并调用**消息中心**通知首个签署人。 | **-> 文档中心**: 上传并转换文件。<br>**<-> 合同DB**: 写入`contracts`, `contract_approvers`, `contract_components`表。<br>**-> 消息中心**: 发送签署通知。 |
| `GenerateContractByAI(userId, conversationHistory)` | 1. 将对话历史`conversationHistory`组装成一个结构化的Prompt。<br>2. 调用**AI赋能域**的`GenerateContract`接口。<br>3. 接收返回的合同文本和结构化数据。<br>4. 创建一份合同草稿，并将AI生成的文本填入。 | **-> AI赋能域**: 调用大模型生成服务。<br>**<-> 合同DB**: 创建合同草稿。 |

### 2.2 签署执行

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `ExecuteSign(userId, contractId, signAreaId, authData)` | 1. 权限校验：检查`userId`是否是当前合同的待签署人。<br>2. 意愿认证：调用**账户中心**的`VerifySignaturePassword`或第三方人脸识别服务，验证`authData`。<br>3. 获取待签署的合同PDF文件Hash。<br>4. 调用**安全与合规中心**的`ApplyDigitalSignature`接口，传入文件Hash、用户信息、签名/印章ID、时间戳等，获取含数字签名的PDF新版本。<br>5. 更新`contract_approvers`表中该签署方的状态为“SIGNED”。<br>6. 检查是否所有人都已签署，若是，则触发合同完成逻辑；否则，通知下一位签署人。 | **-> 账户中心**: 验证签署密码。<br>**-> 第三方认证服务**: 人脸识别。<br>**-> 安全与合规中心**: 申请数字签名和时间戳。<br>**<-> 合同DB**: 更新合同及签署方状态。<br>**-> 消息中心**: 通知下一签署人或所有方合同完成。 |

## 3. AI赋能域 (AI-Powered Domain)

**服务名: `ai-service`**

提供各类AI能力，作为业务服务的上游。

| API 接口 (gRPC/HTTP) | 核心处理逻辑 | 关联模块与数据流 |
| :--- | :--- | :--- |
| `GenerateContract(prompt)` | 1. 对`prompt`进行预处理和安全过滤。<br>2. 调用内部或外部大模型（如GPT-4, ERNIE-4.0）的API。<br>3. 对LLM返回的结果进行后处理，提取结构化信息和合同文本，并进行格式化。<br>4. 返回处理后的结果。 | **-> 大模型服务**: 调用LLM接口。<br>**<-> 知识库/向量数据库**: 可能进行RAG检索增强。 |
| `ReviewContract(contractText)` | 1. 将合同文本分块。<br>2. 调用内置的风险规则库和分类模型，进行初步风险识别。<br>3. 对关键条款（如管辖、违约、赔偿）调用大模型进行深度语义分析。<br>4. 汇总所有风险点，并标记其在原文中的位置，返回给调用方。 | **-> 大模型服务**: 语义分析。<br>**<-> 风险规则库**: 规则匹配。 |
| `ExtractSealFromImage(image)` | 1. 对上传的印章图片进行预处理（灰度化、二值化）。<br>2. 使用图像分割模型（如U-Net）或传统图像处理算法（如边缘检测、霍夫变换）来定位印章区域。<br>3. 对识别出的区域进行背景去除和像素修复。<br>4. 返回透明背景的PNG格式印章图片。 | **-> 图像处理/AI模型**: 调用抠图算法。 |