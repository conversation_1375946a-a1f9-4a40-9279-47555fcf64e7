<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路浩AI电子签 · 交互式产品报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Neutrals with Indigo Accent -->
    <!-- Application Structure Plan: A tab-based single-page application is chosen to manage the dense information of the PDD. This structure avoids an overwhelmingly long scroll and allows users to selectively dive into specific areas of interest. The sections are logically ordered: starting with the high-level 'Why' (Overview, Market), moving to the 'Who' (Users, Scenarios), then the 'What' (Features, Flows), and finally the 'How' (Business Model, Security). This user flow facilitates a progressive understanding of the product, from strategic vision to tactical implementation, making it ideal for business stakeholders. -->
    <!-- Visualization & Content Choices: 
        1. Market Growth (Report Info) -> Goal: Show scale & opportunity -> Viz: Bar Chart (Chart.js) -> Interaction: Hover tooltips -> Justification: Clearly visualizes quantitative growth trends.
        2. Core Values (Report Info) -> Goal: Highlight key differentiators -> Viz: Styled HTML/CSS Cards -> Interaction: Hover effects for engagement -> Justification: More engaging and visually distinct than a simple list.
        3. User Personas (Report Info) -> Goal: Empathize with target users -> Viz: Tabbed content cards -> Interaction: Click to switch between personas -> Justification: Organizes detailed persona info neatly, preventing clutter.
        4. Feature Architecture (Report Info) -> Goal: Explain product structure -> Viz: Interactive diagram using HTML/CSS divs -> Interaction: Click to expand/collapse pillars -> Justification: Recreates a diagram's utility without using disallowed SVG/Mermaid, making a complex structure digestible.
        5. Core Flows (Report Info) -> Goal: Illustrate user journeys -> Viz: Sequential visual diagrams using HTML/CSS Flexbox -> Interaction: Static visual flow -> Justification: Simplifies complex Mermaid charts into clear, step-by-step process visuals.
        6. Pricing Tiers (Report Info) -> Goal: Detail commercial model -> Viz: Interactive pricing table with cards -> Interaction: Clear visual separation -> Justification: A standard and effective pattern for comparing product versions.
        7. Security Measures (Report Info) -> Goal: Build trust -> Viz: Grid of icon cards -> Interaction: Static, clear icons -> Justification: Quickly communicates a sense of comprehensive security.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc;
        }
        .tab-active {
            border-bottom: 2px solid #4f46e5;
            color: #4f46e5;
            font-weight: 500;
        }
        .tab-inactive {
            color: #6b7280;
        }
        .section {
            display: none;
        }
        .section.active {
            display: block;
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 40vh;
            max-height: 350px;
            width: 100%;
            max-width: 800px;
        }
        .flow-step {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            background-color: #eef2ff;
            border: 1px solid #c7d2fe;
            color: #4338ca;
            min-height: 80px;
            transition: all 0.3s ease;
        }
        .flow-step:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        .flow-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 1.5rem;
            padding: 0 1rem;
        }
        .persona-tab-active {
            background-color: #4f46e5;
            color: #ffffff;
        }
        .persona-tab-inactive {
            background-color: #eef2ff;
            color: #4338ca;
        }
        .feature-pillar {
            cursor: pointer;
        }
        .feature-details {
            display: none;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out;
            max-height: 0;
        }
        .feature-details.expanded {
            display: block;
            max-height: 500px;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div class="container mx-auto p-4 md:p-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-slate-900">路浩AI电子签</h1>
            <p class="text-lg text-slate-600 mt-2">智能合同平台 · 交互式产品报告 V5.0</p>
        </header>

        <nav class="sticky top-0 bg-slate-50/80 backdrop-blur-lg z-10 mb-8 border-b border-slate-200">
            <div class="flex flex-wrap justify-center space-x-2 md:space-x-8">
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-active" onclick="showSection('overview')">总览与机遇</button>
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-inactive" onclick="showSection('users')">用户与场景</button>
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-inactive" onclick="showSection('features')">产品功能</button>
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-inactive" onclick="showSection('flows')">核心流程</button>
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-inactive" onclick="showSection('business')">商业模式</button>
                <button class="tab-button py-4 px-2 md:px-4 text-sm md:text-base tab-inactive" onclick="showSection('security')">安全与合规</button>
            </div>
        </nav>

        <main>
            <section id="overview" class="section active animate-fade-in">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第一章 总览：迎接智能合同新时代</h2>
                    <p class="text-slate-600 mb-6">本章将阐述项目的战略远景、市场定位及核心价值主张，为理解“路浩AI电子签”的“为什么”提供宏观视角。我们旨在通过技术创新，重塑合同管理的未来。</p>
                    
                    <div class="grid md:grid-cols-2 gap-8 mb-8">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">产品愿景与战略定位</h3>
                            <p class="text-slate-600 mb-4">我们的愿景是成为中国领先的、以AI技术为核心驱动的智能合同全生命周期管理平台。我们不仅仅是一个签名工具，而是一个融合了**“合规签署 + 智能管理 + 业务赋能”**三位一体的企业级SaaS解决方案。</p>
                            <ul class="list-disc list-inside space-y-2 text-slate-600">
                                <li><strong class="text-slate-700">基石：</strong>金融级安全合规的电子签名。</li>
                                <li><strong class="text-slate-700">核心：</strong>AI能力深度渗透合同全生命周期。</li>
                                <li><strong class="text-slate-700">延展：</strong>开放集成，打破企业数据孤岛。</li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">市场机遇：千亿级蓝海</h3>
                             <p class="text-slate-600 mb-4">中国电子签名市场正经历爆炸性增长，预计到2030年规模将达<strong class="text-indigo-600">926.58亿元</strong>。这一趋势为我们提供了巨大的发展机遇，尤其是在AI赋能和垂直行业深度服务方面存在显著的结构性机会。</p>
                             <div class="chart-container">
                                 <canvas id="marketChart"></canvas>
                             </div>
                        </div>
                    </div>
                    
                    <h3 class="text-xl font-semibold mb-4 text-center">四大核心价值主张</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-indigo-50 p-6 rounded-lg text-center border border-indigo-200 hover:shadow-lg hover:-translate-y-1 transition-all">
                            <div class="text-4xl mb-2">🚀</div>
                            <h4 class="font-bold text-lg text-indigo-800">极致效率</h4>
                            <p class="text-sm text-indigo-700">将数周的合同周期缩短至几分钟，通过AI与自动化加速商业流转。</p>
                        </div>
                        <div class="bg-green-50 p-6 rounded-lg text-center border border-green-200 hover:shadow-lg hover:-translate-y-1 transition-all">
                            <div class="text-4xl mb-2">🛡️</div>
                            <h4 class="font-bold text-lg text-green-800">绝对安全</h4>
                            <p class="text-sm text-green-700">提供符合国密标准的全链路保障，确保每一份合同的法律效力。</p>
                        </div>
                        <div class="bg-amber-50 p-6 rounded-lg text-center border border-amber-200 hover:shadow-lg hover:-translate-y-1 transition-all">
                             <div class="text-4xl mb-2">💡</div>
                            <h4 class="font-bold text-lg text-amber-800">深度智能</h4>
                            <p class="text-sm text-amber-700">用AI唤醒沉睡的合同数据，提供风险预警与商业洞察。</p>
                        </div>
                        <div class="bg-sky-50 p-6 rounded-lg text-center border border-sky-200 hover:shadow-lg hover:-translate-y-1 transition-all">
                             <div class="text-4xl mb-2">🔗</div>
                            <h4 class="font-bold text-lg text-sky-800">无缝集成</h4>
                            <p class="text-sm text-sky-700">通过开放API/SDK融入企业业务系统，打破信息孤岛。</p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="users" class="section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第二章 目标用户与核心场景</h2>
                    <p class="text-slate-600 mb-6">理解我们为谁服务是产品设计的基石。本章将深入剖析我们的核心用户群体、他们的痛点，以及我们的产品如何精准地满足他们在不同业务场景下的需求。</p>
                    
                    <h3 class="text-xl font-semibold mb-4">目标用户画像</h3>
                    <div id="persona-tabs" class="flex flex-wrap justify-center gap-2 mb-6">
                        <button class="persona-tab-button px-4 py-2 text-sm rounded-full transition persona-tab-active" onclick="showPersona('persona1')">法务负责人</button>
                        <button class="persona-tab-button px-4 py-2 text-sm rounded-full transition persona-tab-inactive" onclick="showPersona('persona2')">销售负责人</button>
                        <button class="persona-tab-button px-4 py-2 text-sm rounded-full transition persona-tab-inactive" onclick="showPersona('persona3')">人事负责人</button>
                    </div>

                    <div id="persona-content">
                        <div id="persona1" class="persona-details p-6 bg-slate-50 rounded-lg border">
                             <h4 class="font-bold text-lg mb-2">画像一：“风控守门人”王律师</h4>
                             <p class="text-sm text-slate-500 mb-4">35岁，某中型科技公司法务总监。</p>
                             <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-semibold text-red-600">核心痛点 (Pains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>人工审查效率低，风险易遗漏</li>
                                        <li>合同版本管理混乱，定稿难追溯</li>
                                        <li>归档检索困难，如“大海捞针”</li>
                                        <li>线下用印流程不透明，存在风险</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-green-600">期望收益 (Gains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>AI审查助手，将审查时间缩短70%</li>
                                        <li>标准化的合同模板“生产线”</li>
                                        <li>可追溯的在线协同平台</li>
                                        <li>严格、透明、可审计的线上用印</li>
                                    </ul>
                                </div>
                             </div>
                        </div>
                        <div id="persona2" class="persona-details p-6 bg-slate-50 rounded-lg border" style="display: none;">
                             <h4 class="font-bold text-lg mb-2">画像二：“业绩冲刺者”李经理</h4>
                             <p class="text-sm text-slate-500 mb-4">28岁，某SaaS公司销售团队负责人。</p>
                             <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-semibold text-red-600">核心痛点 (Pains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>签约周期长，影响回款和业绩</li>
                                        <li>异地签署不便，快递耗时且有风险</li>
                                        <li>合同寄出后流程不透明，只能被动等待</li>
                                        <li>移动办公受限，出差无法处理合同</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-green-600">期望收益 (Gains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>“秒签”体验，谈判后即时锁定订单</li>
                                        <li>随时随地移动签署，不受地域限制</li>
                                        <li>实时追踪合同状态，掌握主动权</li>
                                        <li>视频会议中“边谈边签”，加速成交</li>
                                    </ul>
                                </div>
                             </div>
                        </div>
                        <div id="persona3" class="persona-details p-6 bg-slate-50 rounded-lg border" style="display: none;">
                             <h4 class="font-bold text-lg mb-2">画像三：“效率管家”张女士</h4>
                             <p class="text-sm text-slate-500 mb-4">40岁，某连锁零售企业HR总监。</p>
                             <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-semibold text-red-600">核心痛点 (Pains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>招聘季批量签署劳动合同，效率极低</li>
                                        <li>纸质员工档案管理成本高，查阅不便</li>
                                        <li>易出现信息填写不完整等合规风险</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-green-600">期望收益 (Gains):</h5>
                                    <ul class="list-disc list-inside text-sm text-slate-600 space-y-1 mt-2">
                                        <li>一键批量向新员工发起合同</li>
                                        <li>自动生成和归集员工电子档案</li>
                                        <li>通过模板和流程确保标准化与合规</li>
                                        <li>用“一码多签”应对现场招聘等场景</li>
                                    </ul>
                                </div>
                             </div>
                        </div>
                    </div>

                    <h3 class="text-xl font-semibold mt-8 mb-4">核心使用场景</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>🧑‍💼 人力资源:</strong> 员工入职、在职、离职全流程文件签署。</div>
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>📈 销售客户:</strong> 销售合同、渠道协议、服务协议快速签署。</div>
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>📦 采购供应:</strong> 供应商准入、采购订单、招投标管理。</div>
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>⚖️ 法务合规:</strong> 知识产权、公司治理、AI风险控制。</div>
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>💰 财务管理:</strong> 投融资协议、借贷担保、审计支持。</div>
                        <div class="bg-slate-50 p-4 rounded-lg border"><strong>🏛️ 政务服务:</strong> 电子公文、便民服务、政企合作协议。</div>
                    </div>
                </div>
            </section>
            
            <section id="features" class="section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第三章 产品功能体系详解</h2>
                    <p class="text-slate-600 mb-6">为了实现我们的愿景，产品构建在五大逻辑支柱之上。这个交互式架构图展示了各功能模块如何协同工作，为用户提供一个完整、智能且安全的解决方案。点击每个支柱可以展开查看其核心功能。</p>
                    
                    <div id="feature-architecture" class="space-y-4">
                        <!-- Pillar 1 -->
                        <div class="border rounded-lg">
                            <div class="feature-pillar bg-indigo-100 p-4 rounded-t-lg flex justify-between items-center" onclick="toggleFeature('feature1')">
                                <h3 class="text-lg font-semibold text-indigo-800">第一支柱：身份与权限中心</h3>
                                <span class="arrow text-indigo-600 transform transition-transform">▼</span>
                            </div>
                            <div id="feature1" class="feature-details p-4 bg-white rounded-b-lg border-t">
                                <p class="text-sm text-slate-600 mb-2">确保每一个参与方的身份真实可信，并严格遵循权限操作，是平台安全合规的基石。</p>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                    <li><strong>统一账户体系:</strong> 个人与企业的多通道、权威实名认证 (KYC/KYB)。</li>
                                    <li><strong>组织与权限管理:</strong> 可视化组织架构、基于角色的访问控制(RBAC)、精细化数据权限隔离。</li>
                                    <li><strong>集团解决方案:</strong> 支持集团化统一管控与资源共享。</li>
                                </ul>
                            </div>
                        </div>
                        <!-- Pillar 2 -->
                        <div class="border rounded-lg">
                             <div class="feature-pillar bg-green-100 p-4 rounded-t-lg flex justify-between items-center" onclick="toggleFeature('feature2')">
                                <h3 class="text-lg font-semibold text-green-800">第二支柱：合同全生命周期管理</h3>
                                <span class="arrow text-green-600 transform transition-transform">▼</span>
                            </div>
                            <div id="feature2" class="feature-details p-4 bg-white rounded-b-lg border-t">
                                <p class="text-sm text-slate-600 mb-2">覆盖合同从“出生”到“归宿”的全过程，并在每个环节注入智能，提升效率、控制风险。</p>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                    <li><strong>拟定与创建:</strong> 支持模板、本地文件、AI问答等多种发起方式。</li>
                                    <li><strong>协同与审批:</strong> 多人在线实时编辑、版本追溯、AI风险审查、灵活的审批流引擎。</li>
                                    <li><strong>签署执行:</strong> 多重意愿认证、批量签署、跨终端无缝体验。</li>
                                    <li><strong>归档与后管理:</strong> 智能归档与分类、AI履约提醒、全链路证据链报告。</li>
                                </ul>
                            </div>
                        </div>
                        <!-- Pillar 3 -->
                        <div class="border rounded-lg">
                             <div class="feature-pillar bg-amber-100 p-4 rounded-t-lg flex justify-between items-center" onclick="toggleFeature('feature3')">
                                <h3 class="text-lg font-semibold text-amber-800">第三支柱：核心资产管理</h3>
                                 <span class="arrow text-amber-600 transform transition-transform">▼</span>
                            </div>
                            <div id="feature3" class="feature-details p-4 bg-white rounded-b-lg border-t">
                                <p class="text-sm text-slate-600 mb-2">对企业在签署活动中沉淀的核心数字资产——“印章”和“模板”——进行安全、高效、标准化的管理。</p>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                    <li><strong>印章管理中心:</strong> 支持AI抠图、合规模板生成，提供严格的用印授权、审批与审计日志。</li>
                                    <li><strong>模板管理中心:</strong> 建立企业与官方模板库，支持版本控制与权限管理，从源头确保合同范本的合规统一。</li>
                                </ul>
                            </div>
                        </div>
                        <!-- Pillar 4 -->
                        <div class="border rounded-lg">
                            <div class="feature-pillar bg-sky-100 p-4 rounded-t-lg flex justify-between items-center" onclick="toggleFeature('feature4')">
                                <h3 class="text-lg font-semibold text-sky-800">第四支柱：AI智能引擎</h3>
                                <span class="arrow text-sky-600 transform transition-transform">▼</span>
                            </div>
                            <div id="feature4" class="feature-details p-4 bg-white rounded-b-lg border-t">
                                <p class="text-sm text-slate-600 mb-2">产品的核心竞争力，将AI深度融入业务血脉，实现从“效率工具”到“智能平台”的跃迁。</p>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                     <li><strong>AI辅助生成与审查:</strong> 智能问答生成合同、自动识别风险、语义对比版本。</li>
                                     <li><strong>AI智能处理与分析:</strong> 智能抠图、自动归档打标签、自然语言搜索合同、履约节点识别。</li>
                                </ul>
                            </div>
                        </div>
                        <!-- Pillar 5 -->
                        <div class="border rounded-lg">
                            <div class="feature-pillar bg-rose-100 p-4 rounded-t-lg flex justify-between items-center" onclick="toggleFeature('feature5')">
                                <h3 class="text-lg font-semibold text-rose-800">第五支柱：开放与集成平台</h3>
                                <span class="arrow text-rose-600 transform transition-transform">▼</span>
                            </div>
                            <div id="feature5" class="feature-details p-4 bg-white rounded-b-lg border-t">
                                <p class="text-sm text-slate-600 mb-2">打破信息孤岛，将平台的原子化能力作为“积木”，无缝嵌入客户自有业务系统，实现端到端自动化。</p>
                                <ul class="list-disc list-inside text-sm space-y-1">
                                    <li><strong>API/SDK集成能力:</strong> 提供覆盖全业务流程的RESTful API和多语言SDK。</li>
                                    <li><strong>嵌入式组件:</strong> 提供“零跳转”的无缝签署体验。</li>
                                    <li><strong>场景化解决方案:</strong> 包括“一码多签”、“视频会议签”、“战略会议签”等。</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="flows" class="section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第四章 核心业务流程详解</h2>
                    <p class="text-slate-600 mb-6">本章通过可视化的流程图，展示平台最关键的业务流程，让您直观感受各功能模块如何协同工作，为用户提供完整的服务闭环。</p>

                    <div class="space-y-12">
                        <div>
                            <h3 class="text-xl font-semibold mb-4 border-l-4 border-indigo-500 pl-4">流程一：企业入驻与初始化</h3>
                            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4">
                                <div class="flow-step">1. 注册/登录<br/>(个人已实名)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">2. 创建企业<br/>(法人授权/对公打款)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">3. 搭建组织架构<br/>(建部门/导员工)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">4. 创建印章<br/>(授权给角色/部门)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">5. 正式使用</div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-4 border-l-4 border-indigo-500 pl-4">流程二：AI辅助标准合同签署</h3>
                             <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4">
                                <div class="flow-step">1. 发起合同<br/>(上传文件/AI审查)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">2. 内部审批<br/>(主管/法务批准)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">3. 配置签署<br/>(添加签署方/控件)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">4. 多方签署<br/>(人脸识别/签名)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">5. 自动归档<br/>(生成证据链)</div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-4 border-l-4 border-indigo-500 pl-4">流程三：批量合同发起 (HR场景)</h3>
                             <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4">
                                <div class="flow-step">1. 选定模板<br/>(如《劳动合同》)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">2. 导入数据<br/>(上传员工信息Excel)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">3. 一键发起<br/>(向多人发送邀请)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">4. 员工签署<br/>(各自在线完成)</div>
                                <div class="flow-arrow">→</div>
                                <div class="flow-step">5. 自动盖章归档</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="business" class="section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第五章 商业模式与版本策略</h2>
                    <p class="text-slate-600 mb-6">我们采用成熟的SaaS订阅模式，结合按需付费的增值服务，为不同规模和需求的用户提供清晰、价值驱动的版本选择。</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Personal Edition -->
                        <div class="border border-slate-200 rounded-lg p-6 flex flex-col">
                            <h3 class="text-xl font-bold text-center">个人版</h3>
                            <p class="text-center text-slate-500 text-sm mb-4">便捷普惠</p>
                            <div class="text-center my-4">
                                <span class="text-4xl font-bold">¥399</span>
                                <span class="text-slate-500">/年</span>
                            </div>
                            <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>个人实名认证</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>100份合同/年</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>1GB云存储</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>手写签名管理</li>
                            </ul>
                             <button class="mt-6 w-full bg-slate-200 text-slate-700 py-2 rounded-lg font-semibold">当前方案</button>
                        </div>

                        <!-- Standard Edition -->
                        <div class="border-2 border-indigo-500 rounded-lg p-6 flex flex-col relative">
                            <div class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-indigo-500 text-white text-xs font-bold px-3 py-1 rounded-full">推荐</div>
                            <h3 class="text-xl font-bold text-center text-indigo-600">企业标准版</h3>
                            <p class="text-center text-slate-500 text-sm mb-4">数字化起点</p>
                            <div class="text-center my-4">
                                <span class="text-4xl font-bold">¥5,999</span>
                                <span class="text-slate-500">/年起</span>
                            </div>
                             <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>包含个人版所有功能</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span><strong>50个</strong>员工账户</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>组织架构 & 权限管理</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>企业印章 & 模板管理</li>
                                 <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>基础审批流</li>
                            </ul>
                            <button class="mt-6 w-full bg-indigo-500 text-white py-2 rounded-lg font-semibold hover:bg-indigo-600 transition">选择方案</button>
                        </div>
                        
                        <!-- Professional Edition -->
                        <div class="border border-slate-200 rounded-lg p-6 flex flex-col">
                            <h3 class="text-xl font-bold text-center">企业专业版</h3>
                            <p class="text-center text-slate-500 text-sm mb-4">专业提效与集成</p>
                            <div class="text-center my-4">
                                <span class="text-4xl font-bold">¥12,999</span>
                                <span class="text-slate-500">/年起</span>
                            </div>
                             <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>包含标准版所有功能</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span><strong>200个</strong>员工账户</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span><strong>基础AI能力</strong> (审查/提取)</li>
                                 <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span><strong>API/SDK</strong> 集成</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>高级审批流 & 用印审计</li>
                            </ul>
                            <button class="mt-6 w-full bg-slate-800 text-white py-2 rounded-lg font-semibold hover:bg-slate-900 transition">选择方案</button>
                        </div>

                        <!-- Flagship Edition -->
                        <div class="border border-slate-200 rounded-lg p-6 flex flex-col">
                            <h3 class="text-xl font-bold text-center">企业旗舰版</h3>
                            <p class="text-center text-slate-500 text-sm mb-4">战略级解决方案</p>
                            <div class="text-center my-4">
                               <p class="text-2xl font-bold">按需定制</p>
                            </div>
                             <ul class="space-y-2 text-sm text-slate-600 flex-grow">
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>包含专业版所有功能</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span><strong>高级AI套件</strong> (生成/履约)</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>集团管控 & 数据洞察</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>深度定制 & 私有化部署</li>
                                <li class="flex items-center"><span class="text-green-500 mr-2">✔️</span>专属客户成功团队</li>
                            </ul>
                           <button class="mt-6 w-full bg-slate-800 text-white py-2 rounded-lg font-semibold hover:bg-slate-900 transition">联系我们</button>
                        </div>
                    </div>
                </div>
            </section>

            <section id="security" class="section">
                 <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-2xl md:text-3xl font-bold mb-4 text-indigo-600">第七章 安全与合规</h2>
                    <p class="text-slate-600 mb-6">安全与合规是电子签名平台的生命线。我们严格遵循中国法律法规，并采用业界领先的技术与管理措施，为您的每一份合同保驾护航。</p>
                     
                    <h3 class="text-xl font-semibold mb-4 text-center">合规性基石</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 text-center">
                        <div class="bg-slate-50 p-4 rounded-lg border">
                            <h4 class="font-semibold">《电子签名法》</h4>
                            <p class="text-sm text-slate-500">确保“可靠电子签名”与手写盖章同等法律效力。</p>
                        </div>
                        <div class="bg-slate-50 p-4 rounded-lg border">
                            <h4 class="font-semibold">《民法典》</h4>
                            <p class="text-sm text-slate-500">从根本大法层面认可电子合同的书面形式效力。</p>
                        </div>
                         <div class="bg-slate-50 p-4 rounded-lg border">
                            <h4 class="font-semibold">《数据安全法》 & 《个保法》</h4>
                            <p class="text-sm text-slate-500">严格遵循数据分类分级与个人信息保护原则。</p>
                        </div>
                    </div>
                     
                    <h3 class="text-xl font-semibold mb-4 text-center">核心技术安全保障</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">👤</div>
                            <div>
                                <h4 class="font-semibold">身份认证安全</h4>
                                <p class="text-sm text-slate-500">对接权威数据源，采用人脸识别、活体检测，确保签署主体真实性。</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                             <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">🔒</div>
                            <div>
                                <h4 class="font-semibold">数据传输安全</h4>
                                <p class="text-sm text-slate-500">全链路采用TLS 1.3+协议加密，防止数据被窃听或篡改。</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">🗄️</div>
                            <div>
                                <h4 class="font-semibold">数据存储安全</h4>
                                <p class="text-sm text-slate-500">合同文件及敏感数据采用AES-256高强度算法加密存储。</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                             <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">✍️</div>
                            <div>
                                <h4 class="font-semibold">防篡改与完整性</h4>
                                <p class="text-sm text-slate-500">通过数字签名、哈希校验和权威时间戳，确保内容自签署后未被改动。</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">☁️</div>
                            <div>
                                <h4 class="font-semibold">可用性与灾备</h4>
                                <p class="text-sm text-slate-500">多副本、多可用区分布式存储与异地灾备，保障服务高可用。</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">🇨🇳</div>
                            <div>
                                <h4 class="font-semibold">国密算法支持</h4>
                                <p class="text-sm text-slate-500">全面支持SM2/SM3/SM4国密套件，满足信创合规要求。</p>
                            </div>
                        </div>
                    </div>
                 </div>
            </section>
        </main>
    </div>

    <script>
        const sections = document.querySelectorAll('.section');
        const tabButtons = document.querySelectorAll('.tab-button');
        const personaButtons = document.querySelectorAll('.persona-tab-button');
        const personaDetails = document.querySelectorAll('.persona-details');
        const featurePillars = document.querySelectorAll('.feature-pillar');

        function showSection(id) {
            sections.forEach(section => {
                if (section.id === id) {
                    section.classList.add('active');
                } else {
                    section.classList.remove('active');
                }
            });

            tabButtons.forEach(button => {
                if (button.getAttribute('onclick').includes(id)) {
                    button.classList.add('tab-active');
                    button.classList.remove('tab-inactive');
                } else {
                    button.classList.remove('tab-active');
                    button.classList.add('tab-inactive');
                }
            });
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function showPersona(id) {
            personaDetails.forEach(detail => {
                detail.style.display = detail.id === id ? 'block' : 'none';
            });
            personaButtons.forEach(button => {
                if (button.getAttribute('onclick').includes(id)) {
                    button.classList.add('persona-tab-active');
                    button.classList.remove('persona-tab-inactive');
                } else {
                    button.classList.remove('persona-tab-active');
                    button.classList.add('persona-tab-inactive');
                }
            });
        }
        
        function toggleFeature(id) {
            const details = document.getElementById(id);
            const arrow = details.previousElementSibling.querySelector('.arrow');
            const isExpanded = details.classList.contains('expanded');
            
            featurePillars.forEach(p => {
                const d = p.nextElementSibling;
                if(d.id !== id && d.classList.contains('expanded')) {
                    d.classList.remove('expanded');
                     p.querySelector('.arrow').style.transform = 'rotate(0deg)';
                }
            });

            if (isExpanded) {
                details.classList.remove('expanded');
                arrow.style.transform = 'rotate(0deg)';
            } else {
                details.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            const ctx = document.getElementById('marketChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['2023年', '2030年 (预计)'],
                    datasets: [{
                        label: '市场规模 (亿元)',
                        data: [297.32, 926.58],
                        backgroundColor: [
                            'rgba(129, 140, 248, 0.5)',
                            'rgba(79, 70, 229, 0.5)'
                        ],
                        borderColor: [
                            'rgb(129, 140, 248)',
                            'rgb(79, 70, 229)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '亿元 (人民币)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: '中国电子签名市场规模增长趋势',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('zh-CN').format(context.parsed.y) + ' 亿元';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
