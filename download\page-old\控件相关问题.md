# 控件相关问题

> 导航路径: 控件相关问题
> 来源: https://qian.tencent.com/document/78897/
> 抓取时间: 2025-06-15 15:54:03

---

### 在调用 CreateFlowByFiles（用 PDF 文件创建签署流程）时，经办人内容控件中 ComponentName 是否为必传值？ 

ComponentId 和 ComponentName 选择传入一项即可，此处建议传入 ComponentName。 

### 在调用 CreateFlowByFiles（用 PDF 文件创建签署流程）时，如何获取经办人内容控件中 ComponentName 值？

在 [腾讯电子签控制台](https://ess.tencent.cn/template-mgr) 新建或编辑模板时，进入**添加签署区** 步骤，在合同文档上单击选中您需要获取 ComponentName 值的控件，右下角将会出现该控件的属性面板。面板中的**控件名称** 即 ComponentName 的值。

﻿

﻿

﻿  

### 在调用 CreateFlowByFiles（用 PDF 文件创建签署流程）接口时，签署者信息入参 SignComponents，能否支持传入填写控件和签署控件两种？

此处只能传入签署控件，如果需要为签署者添加填写控件，请使用模板发起流程。

### 控件如何进行关键字定位？

Component 入参时，GenerateMode 参数选择填入 KEYWORD，并使用 ComponentId 指定关键字。

### 关键字定位如何调整控件位置？

使用关键字定位时，控件区域的左上角和关键字区域的左上角为重叠关系，可以通过 Component 的入参 OffsetX 以及 OffsetY 来完成控件在横纵坐标上的偏移。

### 控件如何进行表单域定位？

Component 入参时，GenerateMode 参数选择填入 FIELD，并使用 ComponentName 指定关键字。