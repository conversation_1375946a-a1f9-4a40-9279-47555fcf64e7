#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合链接发现器 - 多策略发现所有文档链接
专门针对JavaScript动态内容优化
"""

import re
import time
import json
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from smart_menu_expander import SmartMenuExpander
from utils import is_valid_url


class ComprehensiveLinkDiscoverer:
    """综合链接发现器"""
    
    def __init__(self, crawler):
        self.crawler = crawler
        self.driver = getattr(crawler, 'driver', None)
        self.session = crawler.session
        self.logger = crawler.logger
        self.base_url = crawler.base_url
        self.start_url = crawler.start_url
        self.discovered_links = set()
    
    def discover_all_links(self):
        """发现所有链接的综合策略"""
        
        self.logger.info("开始综合链接发现策略")
        
        # 策略1：Selenium完整展开（如果可用）
        if self.driver:
            selenium_links = self.discover_from_selenium()
            self.discovered_links.update(selenium_links)
            self.logger.info(f"Selenium策略发现 {len(selenium_links)} 个链接")
        
        # 策略2：API接口探测
        api_links = self.discover_from_api()
        new_api_links = api_links - self.discovered_links
        self.discovered_links.update(api_links)
        self.logger.info(f"API策略新增 {len(new_api_links)} 个链接")
        
        # 策略3：站点地图解析
        sitemap_links = self.discover_from_sitemap()
        new_sitemap_links = sitemap_links - self.discovered_links
        self.discovered_links.update(sitemap_links)
        self.logger.info(f"站点地图策略新增 {len(new_sitemap_links)} 个链接")
        
        # 策略4：多样本页面递归
        recursive_links = self.discover_from_samples()
        new_recursive_links = recursive_links - self.discovered_links
        self.discovered_links.update(recursive_links)
        self.logger.info(f"样本页面策略新增 {len(new_recursive_links)} 个链接")
        
        # 策略5：模式匹配生成
        pattern_links = self.discover_from_patterns()
        new_pattern_links = pattern_links - self.discovered_links
        self.discovered_links.update(pattern_links)
        self.logger.info(f"模式匹配策略新增 {len(new_pattern_links)} 个链接")
        
        self.logger.info(f"综合策略总共发现 {len(self.discovered_links)} 个链接")
        return list(self.discovered_links)
    
    def discover_from_selenium(self):
        """使用Selenium发现链接"""
        if not self.driver:
            return set()
        
        self.logger.info("策略1：使用Selenium发现链接")
        
        try:
            # 访问主页面
            self.driver.get(self.start_url)
            self.wait_for_page_load()
            
            # 智能展开所有菜单
            expander = SmartMenuExpander(self.driver, self.logger)
            expanded_count = expander.expand_all_menus(max_rounds=15)
            
            # 提取所有链接
            links = self.extract_all_links_from_page()
            
            self.logger.info(f"主页面展开 {expanded_count} 个菜单，发现 {len(links)} 个链接")
            
            # 从多个样本页面发现更多链接
            sample_urls = list(links)[:10] if links else []
            for url in sample_urls:
                try:
                    self.logger.info(f"处理样本页面: {url}")
                    
                    self.driver.get(url)
                    self.wait_for_page_load()
                    
                    # 展开菜单
                    expander = SmartMenuExpander(self.driver, self.logger)
                    expander.expand_all_menus(max_rounds=8)
                    
                    # 提取链接
                    sample_links = self.extract_all_links_from_page()
                    links.update(sample_links)
                    
                    self.logger.info(f"样本页面 {url} 新增 {len(sample_links - links)} 个链接")
                    
                except Exception as e:
                    self.logger.error(f"样本页面 {url} 处理失败: {e}")
            
            return links
            
        except Exception as e:
            self.logger.error(f"Selenium链接发现失败: {e}")
            return set()
    
    def extract_all_links_from_page(self):
        """从当前页面提取所有有效链接"""
        links = set()
        
        try:
            # 等待页面稳定
            time.sleep(3)
            
            # Docusaurus特定的链接选择器
            docusaurus_selectors = [
                # 侧边栏链接
                '.theme-doc-sidebar-container a[href*="/document/"]',
                '.sidebar a[href*="/document/"]',
                '.menu a[href*="/document/"]',
                
                # 导航链接
                'nav a[href*="/document/"]',
                '.navbar a[href*="/document/"]',
                
                # 内容区域链接
                '.theme-doc-markdown a[href*="/document/"]',
                'article a[href*="/document/"]',
                '.markdown a[href*="/document/"]',
                
                # 分页链接
                '.pagination-nav a[href*="/document/"]',
                
                # 面包屑链接
                '.breadcrumbs a[href*="/document/"]'
            ]
            
            # 通用链接选择器
            generic_selectors = [
                'a[href*="/document/"]',
                'a[href^="/document/"]'
            ]
            
            # 首先尝试Docusaurus特定选择器
            for selector in docusaurus_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and self.is_valid_document_link(href):
                            links.add(href)
                            
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
            
            # 如果特定选择器没找到足够链接，使用通用选择器
            if len(links) < 20:
                for selector in generic_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        
                        for element in elements:
                            href = element.get_attribute('href')
                            if href and self.is_valid_document_link(href):
                                links.add(href)
                                
                    except Exception as e:
                        self.logger.debug(f"通用选择器 {selector} 失败: {e}")
            
            # 使用JavaScript提取更多链接
            js_links = self.extract_links_with_javascript()
            links.update(js_links)
            
        except Exception as e:
            self.logger.error(f"提取页面链接失败: {e}")
        
        return links
    
    def extract_links_with_javascript(self):
        """使用JavaScript提取链接"""
        try:
            js_code = """
            var links = new Set();
            
            // 查找所有包含href的元素
            var elements = document.querySelectorAll('a[href]');
            elements.forEach(function(elem) {
                var href = elem.href;
                if (href && href.includes('/document/')) {
                    links.add(href);
                }
            });
            
            // 查找可能的数据属性
            var dataElements = document.querySelectorAll('[data-href], [data-url], [data-link]');
            dataElements.forEach(function(elem) {
                var href = elem.getAttribute('data-href') || 
                          elem.getAttribute('data-url') || 
                          elem.getAttribute('data-link');
                if (href && href.includes('/document/')) {
                    if (!href.startsWith('http')) {
                        href = window.location.origin + href;
                    }
                    links.add(href);
                }
            });
            
            // 查找可能隐藏在JavaScript变量中的链接
            var scripts = document.querySelectorAll('script');
            scripts.forEach(function(script) {
                var content = script.textContent || script.innerText;
                if (content) {
                    var matches = content.match(/['"]/document/\\d+['"]|['"]/document/[^'"]+['"]/g);
                    if (matches) {
                        matches.forEach(function(match) {
                            var url = match.replace(/['"]/g, '');
                            if (url.startsWith('/document/')) {
                                links.add(window.location.origin + url);
                            }
                        });
                    }
                }
            });
            
            return Array.from(links);
            """
            
            js_links = self.driver.execute_script(js_code)
            valid_links = set()
            
            for link in js_links or []:
                if self.is_valid_document_link(link):
                    valid_links.add(link)
            
            return valid_links
            
        except Exception as e:
            self.logger.debug(f"JavaScript提取链接失败: {e}")
            return set()
    
    def discover_from_api(self):
        """通过API接口发现链接"""
        self.logger.info("策略2：API接口探测")
        
        api_links = set()
        
        # 常见的API端点
        api_endpoints = [
            '/api/docs',
            '/api/documents',
            '/api/navigation',
            '/api/menu',
            '/api/sitemap',
            '/api/list',
            '/document/api/list',
            '/document/api/tree',
            '/document/api/navigation',
            '/_next/static/chunks/pages',
            '/static/js'
        ]
        
        for endpoint in api_endpoints:
            try:
                api_url = urljoin(self.base_url, endpoint)
                response = self.session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            links = self.extract_links_from_json(data)
                            api_links.update(links)
                            self.logger.info(f"API端点 {endpoint} 发现 {len(links)} 个链接")
                        except:
                            pass
                    elif 'text/javascript' in content_type or 'application/javascript' in content_type:
                        # 从JavaScript文件中提取链接
                        links = self.extract_links_from_js_content(response.text)
                        api_links.update(links)
                        if links:
                            self.logger.info(f"JS文件 {endpoint} 发现 {len(links)} 个链接")
                    
            except Exception as e:
                self.logger.debug(f"API端点 {endpoint} 测试失败: {e}")
        
        return api_links
    
    def extract_links_from_json(self, data):
        """从JSON数据中提取链接"""
        links = set()
        
        def recursive_extract(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in ['url', 'href', 'link', 'path', 'route'] and isinstance(value, str):
                        if '/document/' in value:
                            full_url = urljoin(self.base_url, value)
                            if self.is_valid_document_link(full_url):
                                links.add(full_url)
                    else:
                        recursive_extract(value)
            elif isinstance(obj, list):
                for item in obj:
                    recursive_extract(item)
        
        recursive_extract(data)
        return links
    
    def extract_links_from_js_content(self, js_content):
        """从JavaScript内容中提取链接"""
        links = set()
        
        # 匹配JavaScript中的文档链接
        patterns = [
            r'["\'](/document/\d+)["\']',
            r'["\'](/document/[^"\']+)["\']',
            r'href\s*:\s*["\'](/document/[^"\']+)["\']',
            r'path\s*:\s*["\'](/document/[^"\']+)["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, js_content)
            for match in matches:
                full_url = urljoin(self.base_url, match)
                if self.is_valid_document_link(full_url):
                    links.add(full_url)
        
        return links
    
    def discover_from_sitemap(self):
        """通过站点地图发现链接"""
        self.logger.info("策略3：站点地图解析")
        
        sitemap_links = set()
        
        sitemap_urls = [
            '/sitemap.xml',
            '/sitemap_index.xml',
            '/sitemap-0.xml',
            '/sitemap-1.xml',
            '/robots.txt'
        ]
        
        for sitemap_url in sitemap_urls:
            try:
                full_url = urljoin(self.base_url, sitemap_url)
                response = self.session.get(full_url, timeout=10)
                
                if response.status_code == 200:
                    if sitemap_url.endswith('.xml'):
                        links = self.parse_sitemap_xml(response.text)
                    else:
                        links = self.parse_robots_txt(response.text)
                    
                    sitemap_links.update(links)
                    if links:
                        self.logger.info(f"站点地图 {sitemap_url} 发现 {len(links)} 个链接")
                    
            except Exception as e:
                self.logger.debug(f"站点地图 {sitemap_url} 失败: {e}")
        
        return sitemap_links
    
    def parse_sitemap_xml(self, xml_content):
        """解析XML站点地图"""
        links = set()
        
        try:
            soup = BeautifulSoup(xml_content, 'xml')
            
            # 查找所有URL元素
            for url_elem in soup.find_all('url'):
                loc_elem = url_elem.find('loc')
                if loc_elem and loc_elem.text:
                    url = loc_elem.text.strip()
                    if self.is_valid_document_link(url):
                        links.add(url)
            
            # 查找sitemap索引
            for sitemap_elem in soup.find_all('sitemap'):
                loc_elem = sitemap_elem.find('loc')
                if loc_elem and loc_elem.text:
                    sitemap_url = loc_elem.text.strip()
                    # 递归获取子站点地图
                    try:
                        response = self.session.get(sitemap_url, timeout=10)
                        if response.status_code == 200:
                            sub_links = self.parse_sitemap_xml(response.text)
                            links.update(sub_links)
                    except:
                        pass
        
        except Exception as e:
            self.logger.debug(f"解析XML站点地图失败: {e}")
        
        return links
    
    def parse_robots_txt(self, robots_content):
        """解析robots.txt文件"""
        links = set()
        
        try:
            for line in robots_content.split('\n'):
                line = line.strip()
                if line.startswith('Sitemap:'):
                    sitemap_url = line.split(':', 1)[1].strip()
                    try:
                        response = self.session.get(sitemap_url, timeout=10)
                        if response.status_code == 200:
                            sitemap_links = self.parse_sitemap_xml(response.text)
                            links.update(sitemap_links)
                    except:
                        pass
        
        except Exception as e:
            self.logger.debug(f"解析robots.txt失败: {e}")
        
        return links
    
    def discover_from_samples(self):
        """从样本页面递归发现链接"""
        self.logger.info("策略4：样本页面递归发现")
        
        # 使用已发现的链接作为样本
        sample_urls = list(self.discovered_links)[:15] if self.discovered_links else [self.start_url]
        
        sample_links = set()
        
        for url in sample_urls:
            try:
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'lxml')
                    
                    # 提取所有文档链接
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        full_url = urljoin(url, href)
                        if self.is_valid_document_link(full_url):
                            sample_links.add(full_url)
                
            except Exception as e:
                self.logger.debug(f"样本页面 {url} 处理失败: {e}")
        
        return sample_links
    
    def discover_from_patterns(self):
        """基于模式匹配生成可能的链接"""
        self.logger.info("策略5：模式匹配生成")
        
        pattern_links = set()
        
        # 分析已发现链接的模式
        if self.discovered_links:
            # 提取数字ID模式
            ids = set()
            for link in self.discovered_links:
                match = re.search(r'/document/(\d+)', link)
                if match:
                    ids.add(int(match.group(1)))
            
            if ids:
                min_id = min(ids)
                max_id = max(ids)
                
                # 生成可能的ID范围
                for doc_id in range(min_id - 100, max_id + 1000):
                    if doc_id > 0:  # 确保ID为正数
                        test_url = f"{self.base_url}/{doc_id}"
                        pattern_links.add(test_url)
                
                # 限制数量避免过多无效请求
                pattern_links = set(list(pattern_links)[:200])
        
        # 验证生成的链接（快速HEAD请求）
        valid_pattern_links = set()
        for url in pattern_links:
            try:
                response = self.session.head(url, timeout=5)
                if response.status_code == 200:
                    valid_pattern_links.add(url)
                    if len(valid_pattern_links) >= 50:  # 限制数量
                        break
            except:
                continue
        
        self.logger.info(f"模式匹配验证了 {len(valid_pattern_links)} 个有效链接")
        return valid_pattern_links
    
    def is_valid_document_link(self, url):
        """验证是否为有效的文档链接"""
        return is_valid_url(url, self.base_url)
    
    def wait_for_page_load(self, timeout=30):
        """等待页面完全加载"""
        if not self.driver:
            return
        
        try:
            # 等待基础DOM
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待文档就绪
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 等待侧边栏
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".theme-doc-sidebar-container, .sidebar, .menu, nav"))
                )
            except TimeoutException:
                self.logger.warning("侧边栏加载超时")
            
            # 额外等待时间
            time.sleep(3)
            
        except TimeoutException:
            self.logger.warning("页面加载超时")