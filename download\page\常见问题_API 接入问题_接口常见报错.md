# 接口常见报错

> 导航路径: 常见问题 > API 接入问题 > 接口常见报错
> 来源: https://qian.tencent.com/document/116843
> 抓取时间: 2025-06-15 16:41:37

---

### 同一份合同中的同一签署人不可同时以企业及个人身份签署

同一个自然人，不能同时作为个人和企业（经办人）两个签署方去签署。

**解决方式：**

方式一：合同只保留企业签署方（去掉个人签署方），并指定**印章** 和**签字** 两个签署控件；

方式二：如果除了此自然人外，合同还有其他签署方，可联系客户经理给开白处理。

**注意：**

如果合同涉及到本方企业自动签署，其经办人取的合同发起人（即自建应用是 `Operator.UserId`，第三方应用是 `Agent.ProxyOperator.OpenId`），请确保此员工和个人签署方不是一个人。

### 其他公司自动签署功能暂未开放，请联系客户经理申请【他方自动签署权限】后使用

假设合同由 A 企业发起（即调接口使用的 API 密钥来自于 A 企业），签署方为另外一个企业（B 企业），如果需要 B 企业自动签署，请参考：

[自建应用合作方自动签署](https://qian.tencent.com/developers/company/autosign_guide#%E5%9B%9B-%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2 "https://qian.tencent.com/developers/company/autosign_guide#%E5%9B%9B-%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2")

[第三方应用合作方自动签署](https://qian.tencent.com/developers/partner/autosign_guide#%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2 "https://qian.tencent.com/developers/partner/autosign_guide#%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2")

### 本企业自动签印章无权限使用

1. 请检查传入的印章是否存在并启用

2. 请检查经办人是否有此印章权限。需要注意的是对于自动签署而言，经办人默认为发起人且不能更改。具体请参考以下：

自建应用：发起人为 `Operator.UserId`。 

﻿

﻿

第三方应用：发起人为 `Agent.ProxyOperator.OpenId`。 

﻿

﻿

### 合作方企业自动签印章未授权

**自建应用：**

假设合同由 A 企业发起（即调接口使用的 API 密钥来自于 A 企业），签署方为另外一个企业（B 企业），如果需要 B 企业自动签署，则被称为**合作方企业自动签** ，需要 B 企业授权操作。

**情况1** ：

若采用 B 企业主动给 A 企业授权，请参考 [让合作方主动给我授权](https://qian.tencent.com/developers/company/autosign_guide/#2-%E8%AE%A9%E5%90%88%E4%BD%9C%E6%96%B9%E4%B8%BB%E5%8A%A8%E7%BB%99%E6%88%91%E6%8E%88%E6%9D%83%E6%94%AF%E6%8C%81%E6%A8%A1%E6%9D%BF%E5%92%8C%E6%96%87%E4%BB%B6%E5%8F%91%E8%B5%B7 "https://qian.tencent.com/developers/company/autosign_guide/#2-%E8%AE%A9%E5%90%88%E4%BD%9C%E6%96%B9%E4%B8%BB%E5%8A%A8%E7%BB%99%E6%88%91%E6%8E%88%E6%9D%83%E6%94%AF%E6%8C%81%E6%A8%A1%E6%9D%BF%E5%92%8C%E6%96%87%E4%BB%B6%E5%8F%91%E8%B5%B7")。 **情况2** ：

若采用 A 企业邀请 B 企业给固定模板授权，请参考 [邀请合作方给我授权](https://qian.tencent.com/developers/company/autosign_guide/#1-%E9%82%80%E8%AF%B7%E5%90%88%E4%BD%9C%E6%96%B9%E7%BB%99%E6%88%91%E6%8E%88%E6%9D%83%E4%BB%85%E6%94%AF%E6%8C%81%E5%9B%BA%E5%AE%9A%E6%A8%A1%E6%9D%BF "https://qian.tencent.com/developers/company/autosign_guide/#1-%E9%82%80%E8%AF%B7%E5%90%88%E4%BD%9C%E6%96%B9%E7%BB%99%E6%88%91%E6%8E%88%E6%9D%83%E4%BB%85%E6%94%AF%E6%8C%81%E5%9B%BA%E5%AE%9A%E6%A8%A1%E6%9D%BF")。

**第三方应用：**

假设合同由 A 企业发起（`Agent.ProxyOrganizationOpenId` 为 A 子客的 OpenId），签署方为另外一个子客（B 企业），如果需要 B 企业自动签署，则被称为**合作方企业自动签** ，需要 B 企业授权操作，请参考 [合作方企业自动签署](https://qian.tencent.com/developers/partner/autosign_guide#%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2 "https://qian.tencent.com/developers/partner/autosign_guide#%E5%90%88%E4%BD%9C%E6%96%B9%E4%BB%96%E6%96%B9%E4%BC%81%E4%B8%9A%E8%87%AA%E5%8A%A8%E7%AD%BE%E7%BD%B2")。

**注意：**

比如 B 企业给 A 企业只授权了「公章」，但发起合同时传入了「财务专用章」的印章 ID，那也会报此错误。

在自建应用情况2中，如果被授权的模板发生了变更，授权会失效，需要重新授权。

### 在此合同中不存在该签署人，请确认传入的签署人信息是否正确

请检查是否发起合同时指定了签署人身份证号，但获取 H5签署链接时没有指定。如果签署人之前已经在电子签实名过，且绑定的手机号并非获取签署链接时传入的手机号，就会报此错误。

例如：比如发起合同时指定了张三，手机号133，身份证号 abc。由于张三之前在电子签实名过，且绑定的手机号为188，则发起的这个合同就关联到了188手机号。再用133手机号去获取签署链接就会报错。

**解决方式：**

方式1：引导用户直接去小程序签署

方式2：获取签署链接时也传入身份证号

方式3：发起合同时不传身份证号，上例中张三会在签署过程中将绑定的手机号更换为133（需要注意的是换绑过程133手机号需要收验证码，请确保133为张三当前在用的手机号）

### 未在合同中找到您提供的签署人信息。请核对姓名、手机号等提供的信息是否正确

请检查是否发起合同时指定了签署人身份证号，但获取合同组签署链接时没有指定。如果签署人之前已经在电子签实名过，且绑定的手机号并非获取签署链接时传入的手机号，就会报此错误。

例如：比如发起合同时指定了张三，手机号133，身份证号 abc。由于张三之前在电子签实名过，且绑定的手机号为188，则发起的这个合同就关联到了188手机号。再用133手机号去获取签署链接就会报错。

**解决方式：**

方式1：引导用户直接去小程序签署。

方式2：获取签署链接时也传入身份证号。

### 参数错误，指定的操作人不存在/不属于当前企业

**操作人** 是指接口中的 `Operator.UserId` 参数，请先确认 UserId 是否取的是企业员工的用户 ID：

﻿

﻿

﻿  

**另外一个常见原因是，这里的用户 ID 传入了正式/测试环境的企业员工，而接口请求到了测试/正式环境。**

联调环境地址：

环境| 地址| 说明  
---|---|---  
文件服务的 EndPoint| file.test.ess.tencent.cn| UploadFiles 接口使用  
通用接口请求 EndPoint| ess.test.ess.tencent.cn| 除 UploadFiles 外其他接口使用  
  
线上环境地址：

环境| 地址| 说明  
---|---|---  
文件服务的 EndPoint| file.ess.tencent.cn| UploadFiles 接口使用  
通用接口请求 EndPoint| ess.tencentcloudapi.com| 除 UploadFiles 外其他接口使用  
  
### 在这个 PDF 中没有搜索到[xxx]，请确认这个字符串是否存在

用关键字定位控件时报此错误，可以借助 [Adobe Acrobat Reader](https://get.adobe.com/cn/reader/ "https://get.adobe.com/cn/reader/") 等 PDF 阅读器查找是否有对应关键字。另外一个常见问题是在传参时指定了 `KeywordPage`，即限定了搜索页码，请确认是否是此原因造成。

### 控件的 X（Y） 坐标超出了允许范围

请检查控件是否超过了 PDF 的最大宽度（595）或者最大高度（842），尤其需要注意，是否将 PDF 旋转了（宽842，高595）。

### PDF 文件损坏，请上传正确的 PDF 文件

出现这种情况是指传入的 PDF 文件结构不规范，电子签无法处理此类文件。可以用 PDF 阅读器（例如 Adobe Acrobat Reader、福昕等）检查文件签名是否完整。比如以下文件已损坏：

﻿

﻿

﻿  

### 您输入的内容含不允许的敏感词汇

如果是签署方姓名、企业名称，可以选择使用模板发起合同，使用签署方信息控件； 

﻿

﻿

如果是其他的填写控件，可以联系电子签侧处理后再操作。

### 参数错误，签署人填写控件不支持[SIGN_SEAL]类型

请检查是否传参时将签署控件（`SignComponents`）字段写成了填写控件（`Components`）。

### 用户[xxx]未开启或已关闭自动签服务

在发起带有**个人自动签** 功能的合同时，如果报此错误，并且已确认该签署人已经开通了个人自动签。请检查是否签署人开通自动签时的手机号，和发起合同时传入的手机号不一致。

### 发起方签署审核/节点签署审核不能同时使用

请检查代表签署审批的参数 **NeedSignReview** 是指定到哪个维度的：

如果指定的是合同维度的 `NeedSignReview` 参数，在调审批接口时**无需** 传 `RecipientId`。

如果指定的是签署方维度的 `ApproverNeedSignReview`，在调审批接口时**必须传**` RecipientId`。

### 您当前正在使用【自建应用集成】功能，升级至【专业版】及以上版本后即可使用

如果是通过集团**主代子** 的方式调用接口报此错误，请联系电子签客户经理开白处理。

### 签署人 RecipientId 不属于当前模版

在调用 [CreatePrepareFlow](https://qian.tencent.com/developers/companyApis/embedPages/CreatePrepareFlow "https://qian.tencent.com/developers/companyApis/embedPages/CreatePrepareFlow") 接口时报此错误，请确认是否模板发生了调整，模板的任意变化都可能导致 RecipientId 变更。

### 您上传的文件不是 PDF 格式，导致无法发起合同

通过文件发起合同时，仅支持 PDF 格式的文件。需要注意的是，如果通过**创建文件转换任务** 接口对源文件格式进行了转换，还需要调**查询转换任务状态** 接口去获取转换后的 `ResourceId`（PDF 格式），用于合同发起。

### 缺少登录用户 ID，请检查登录账号并重试

自建应用集成中，所有接口的 `Operator` 字段都是必传，请检查是否少传了此参数。如果没有用电子签提供的 SDK（不建议），请确保传入的所有字段为大写字母开头。