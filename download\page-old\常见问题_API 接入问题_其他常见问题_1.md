# 其他常见问题

> 导航路径: 常见问题 > API 接入问题 > 其他常见问题
> 来源: https://qian.tencent.com/document/116845
> 抓取时间: 2025-06-15 16:34:16

---

### 为什么收不到签署短信？

如果签署人收不到短信，可以从以下几个方面进行排查：

1. 规则限制：自建应用的短信规则较为复杂，除了在**企业设置** -**拓展服务** 中有全局控制的开关“**短信通知签署方** ”外，短信是否默认发送还受到多方面因素的影响，具体规则见下图： 

﻿

﻿

**注意：**

﻿[发起解除协议](https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateReleaseFlow "https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateReleaseFlow") 时会给签署方发送短信且不能配置。

2. 签署顺序：对于设置了签署顺序的合同，请确保当前是否已经轮到此签署人签署；

3. 手机配置：短信被用户手机中的系统/软件拦截屏蔽，请检查屏蔽列表；

4. 短信限频：电子签给同一个手机号所发短信会有频率限制：1条/30秒、10条/小时、20条/天。

### 支持自动签署吗？

自动签署是指在某些场景下，企业签署方无需手动进入签署页面进行签署操作，由腾讯电子签负责自动给合同进行盖章动作。详细接入步骤请参见：

[自建应用自动签署](https://qian.tencent.com/developers/company/autosign_guide "https://qian.tencent.com/developers/company/autosign_guide")

[第三方应用自动签署](https://qian.tencent.com/developers/partner/autosign_guide "https://qian.tencent.com/developers/partner/autosign_guide")

### 合同只能由发起时指定的签署人签署吗？

**签署方为个人**

如果指定了合同由 A 签署，则只有 A 可以签署这份合同；

通过**动态签署方** 的能力实现发起合同时不指定具体的签署人。具体请参考：

﻿[自建应用动态签署方](https://qian.tencent.com/developers/company/dynamic_signer "https://qian.tencent.com/developers/company/dynamic_signer")﻿

[第三方应用动态签署方](https://qian.tencent.com/developers/partner/dynamic_signer "https://qian.tencent.com/developers/partner/dynamic_signer")

**签署方为企业员工**

如果指定了合同由 A 签署，则只有 A 可以签署这份合同，或者由 A 操作将这份合同**转交** 给其他同事；

通过**动态签署方** 的能力实现发起合同时不指定具体的签署人。具体请参考：

[自建应用动态签署方](https://qian.tencent.com/developers/company/dynamic_signer "https://qian.tencent.com/developers/company/dynamic_signer")

[第三方应用动态签署方](https://qian.tencent.com/developers/partner/dynamic_signer "https://qian.tencent.com/developers/partner/dynamic_signer")

对于自建应用，还可以通过「企业或签」的能力实现发起合同时指定多个企业员工可或签。

### “模板”、“文件”、“嵌入页面”，这些发起方式我该如何选择？

**模板发起** 如果合同应用场景比较单一，推荐用此方案，只需要在电子签后台预先配置好模板，后续发起时传入指定的签署人信息就可以了。

**文件发起** 较模板发起更为灵活，无需在电子签后台配置模板，合同发起所需的参数均由接口传入，控件定位支持通过关键字或者坐标。另外还支持由签署方在签署时通过拖拽的方式指定签章这种（模板发起不支持的）方式。

**嵌入页面发起** 如果业务中所需签署的合同文件不可预期，无法即时的去配置模板，或者很难通过文件发起的参数指定控件的位置，推荐采用此种方式。通过嵌入电子签提供的发起页面，由合同发起人每发起一份合同，都在嵌入页面上重新指定合同文件、签署控件、签署人等内容。

### 文件发起合同时，如何定位控件的位置？

通过文件发起合同会涉及到控件（签署控件/填写控件）在 PDF 文件中的定位，电子签支持 [三种定位方式](https://qian.tencent.com/developers/company/createFlowByFiles#%E4%B8%89%E7%A7%8D%E5%AE%9A%E4%BD%8D%E6%96%B9%E5%BC%8F%E8%AF%B4%E6%98%8E "https://qian.tencent.com/developers/company/createFlowByFiles#%E4%B8%89%E7%A7%8D%E5%AE%9A%E4%BD%8D%E6%96%B9%E5%BC%8F%E8%AF%B4%E6%98%8E")。其中**关键字定位** 和**坐标定位** 较为常用，在使用时需要注意以下几点：

对于关键字定位，推荐用 `RelativeLocation` 参数结合 OffsetX 和 OffsetY 来做相对位置的微调，以防 Offset 给的不合理导致控件出了文件边界。

﻿

﻿

代码示例（上图最后一个章）：

    {
    
      "GenerateMode": "KEYWORD",
    
      "ComponentId": "关键字",
    
      "FileIndex": 0,
    
      "ComponentType": "SIGN_SEAL",
    
      "ComponentWidth": 119,
    
      "ComponentHeight": 119,
    
      "RelativeLocation": "Right",
    
      "OffsetX": 22,
    
      "OffsetY": 11
    
    }

对于坐标定位，可以使用 [PDF 坐标计算助手](https://qian.tencent.com/developers/tools/template-editor/) 辅助您对控件进行定位，具体可参考使用介绍。

对于印章和签名，`ComponentWidth` 和 `ComponentHeight` 建议采用以下推荐值，保持和官方模板配置一致：

**SIGN_SEAL** ：宽119高119

**SIGN_SIGNATURE** ：宽119高43

**SIGN_DATE** ：宽119高20

### 是否支持外国人和港澳台居民签署

由于公安库里没有提供外国人的信息以供对比，目前外国人签署只能通过**形式签** （具体请咨询电子签客户经理）。

港澳台居民目前支持的证件类型请参见 [常见个人证件类型介绍](https://qian.tencent.com/developers/company/id_card_support "https://qian.tencent.com/developers/company/id_card_support")。

**注意：**

如果是通过 H5签署，由于人脸数据库的关系，除了身份证以外，目前仅支持少量830开头的港澳台居民居住证。

需在企业扩展服务（第三方应用是在子客企业扩展服务）中打开以下选项： 

﻿

﻿

### 电子签支持哪些签署终端，该如何选择？

支持通过电子签小程序、H5、Web 页面进行签署。集成哪种签署终端一般来说取决于自有应用的形态，如果业务是：

**小程序** ：集成电子签小程序。可以通过全屏或者半屏（[演示视频](https://qian.tencent.com/developers/video/?menu=scene&id=59 "https://qian.tencent.com/developers/video/?menu=scene&id=59") ）拉起电子签小程序进行签署。

**APP** ：集成电子签小程序或 H5

**Web** ：集成电子签 Web 页面（仅支持企业签署方）

### 合同撤销和废弃有何区别？

**撤销** ：合同撤销是指在当前至少还有一方没有签署时，可以结束此合同的行为。撤销后的合同即为“已撤销”状态，是一种合同终态。 **废弃** ：合同废弃是指合同所有签署方已经签署完毕，但需要作废此合同的行为。具体做法和线下一样，实际上是另外发起一份“解除协议”用以作废当前合同。 

﻿

﻿

### 支持签署的时候自由设置签章位置吗？

#### 自建应用

文件发起合同时通过设置 [CreateFlowByFiles](https://qian.tencent.com/developers/companyApis/startFlows/CreateFlowByFiles "https://qian.tencent.com/developers/companyApis/startFlows/CreateFlowByFiles") 接口中的 `SignBeanTag` 参数为1，可不指定签署控件，由各签署方在签署时通过拖拽的方式先设置控件，然后再进行签署（[视频演示](https://qian.tencent.com/developers/video/?menu=scene&id=38 "https://qian.tencent.com/developers/video/?menu=scene&id=38")）。需要注意以下几点：

SignBeanTag 是合同维度的参数，一旦设定，合同涉及到的所有签署方均不能在发起时设置签署控件。

可通过签署方结构体 [ApproverInfo](https://qian.tencent.com/developers/companyApis/dataTypes/#approverinfo "https://qian.tencent.com/developers/companyApis/dataTypes/#approverinfo") 中的 `AddSignComponentsLimits` 字段限制签署时可选的控件类型（如印章/骑缝章）和值（具体哪个印章）

不能设置企业自动签署

模板发起不支持此功能

#### 第三方应用

文件发起合同时通过设置 [ChannelCreateFlowByFiles](https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateFlowByFiles "https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateFlowByFiles") 接口中的 `SignBeanTag` 参数为1，可不指定签署控件，由各签署方在签署时通过拖拽的方式先设置控件，然后再进行签署（[视频演示](https://qian.tencent.com/developers/video/?menu=scene&id=38 "https://qian.tencent.com/developers/video/?menu=scene&id=38")）。需要注意以下几点：

SignBeanTag 是合同维度的参数，一旦设定，合同涉及到的所有签署方均不能在发起时设置签署控件。

可通过签署方结构体 [FlowApproverInfo](https://qian.tencent.com/developers/partnerApis/dataTypes/#flowapproverinfo "https://qian.tencent.com/developers/partnerApis/dataTypes/#flowapproverinfo") 中的 `AddSignComponentsLimits` 字段限制签署时可选的控件类型（如印章/骑缝章）和值（具体哪个印章）

不能设置企业自动签署

模板发起不支持此功能

### 支持一个签署流程，签署多份合同文件吗？

电子签支持批量签署合同：

签署方为企业员工，参考 [批量签署指引](https://qian.tencent.com/document/92777 "https://qian.tencent.com/document/92777") 先完成授权，之后可以在 PC 和小程序进行批量签署；

签署方为个人，可直接进入小程序进行批量签署。

如果集成了电子签 API，也可以通过**获取批量签署链接** ，引导用户进行批量签署：

自建应用：

[小程序批量签署](https://qian.tencent.com/developers/companyApis/startFlows/CreateBatchSignUrl "https://qian.tencent.com/developers/companyApis/startFlows/CreateBatchSignUrl")

[H5批量签署](https://qian.tencent.com/developers/companyApis/startFlows/CreateBatchQuickSignUrl "https://qian.tencent.com/developers/companyApis/startFlows/CreateBatchQuickSignUrl")

﻿[PC 批量签署](https://qian.tencent.com/developers/companyApis/embedPages/CreateOrganizationBatchSignUrl)（此方式只支持本方或他方企业员工签署，不支持个人端）

第三方应用：

[小程序批量签署](https://qian.tencent.com/developers/partnerApis/operateFlows/ChannelCreateBatchSignUrl "https://qian.tencent.com/developers/partnerApis/operateFlows/ChannelCreateBatchSignUrl")

[H5批量签署](https://qian.tencent.com/developers/partnerApis/operateFlows/ChannelCreateBatchQuickSignUrl "https://qian.tencent.com/developers/partnerApis/operateFlows/ChannelCreateBatchQuickSignUrl")

﻿[PC 批量签署](https://qian.tencent.com/developers/partnerApis/embedPages/ChannelCreateOrganizationBatchSignUrl)（此方式只支持本方或他方企业员工签署，不支持个人端）

### 已经签署完成的合同怎么作废删除

可以通过签署**解除协议** 解除原合同（暂不支持通过界面解除） ，签署解除协议之后，原合同状态将会变成已解除。目前仅支持通过 API 解除协议，具体请参见：

[自建应用发起解除协议](https://qian.tencent.com/developers/companyApis/operateFlows/CreateReleaseFlow "https://qian.tencent.com/developers/companyApis/operateFlows/CreateReleaseFlow")

[第三方应用发起解除协议](https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateReleaseFlow/ "https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateReleaseFlow/")

**注意：**

也可以通过电子签提供的解除协议模板，或者由接入方法务确认过的其他解除模版来发起解除协议。另外，通过控制台发起的解除协议不能改变原合同的状态。

### 发起合同前如何实现预览？

#### 自建应用

**通过模板发起：**

可以通过设置 [CreateDocument](https://qian.tencent.com/developers/companyApis/startFlows/CreateDocument "https://qian.tencent.com/developers/companyApis/startFlows/CreateDocument") 接口的 `NeedPreview` 字段实现合同预览：

﻿

﻿

**通过文件发起：**

可以通过设置 [CreateFlowByFiles](https://qian.tencent.com/developers/companyApis/startFlows/CreateFlowByFiles "https://qian.tencent.com/developers/companyApis/startFlows/CreateFlowByFiles") 接口的 `NeedPreview` 字段实现合同预览。

#### 第三方应用

**通过模板发起：**

可以通过设置 [CreateFlowsByTemplates](https://qian.tencent.com/developers/partnerApis/startFlows/CreateFlowsByTemplates/ "https://qian.tencent.com/developers/partnerApis/startFlows/CreateFlowsByTemplates/") 接口的 `NeedPreview` 字段实现合同预览。

**通过文件发起：**

可以通过设置 [ChannelCreateFlowByFiles](https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateFlowByFiles/ "https://qian.tencent.com/developers/partnerApis/startFlows/ChannelCreateFlowByFiles/") 接口的 `NeedPreview` 字段实现合同预览。

### 单份合同内的签章个数有上限的吗？

最多支持50方签署，每个签署方可以多个签名（无限制）。

### 合同撤销后会返还合同份额吗

所有签署方未签署（不包含自动签）的撤回会返还合同份额的。如果有其中一个签署方签署了就不会返还。