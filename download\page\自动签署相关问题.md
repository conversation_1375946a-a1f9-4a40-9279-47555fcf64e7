# 自动签署相关问题

> 导航路径: 自动签署相关问题
> 来源: https://qian.tencent.com/document/78889/
> 抓取时间: 2025-06-15 16:42:06

---

### 如何发起自动签署？

1. 使用文件创建合同接入需要在 CreateFlowByFiles 接口传入 ApproverInfo 数组时，设置签署者的 ApproverType 为3。注意目前仅支持发起者（企业方）进行自动签署。 

2. 使用模板创建合同接入登录 [腾讯电子签控制台](https://ess.tencent.cn/template-mgr "https://ess.tencent.cn/template-mgr")，进入**模板管理** ，在编辑或者新增模板**配置模板信息** 步骤中设置己方签署方式为**自动签署** ，完成后保存模板并以此模板 ID 重新发起合同。 

﻿

﻿

﻿  

### 能够让非发起方企业进行自动签署么？ 

自动签署需要控制对应企业的印章，且签署具有法律效益，故业务上仅允许发起方企业控制己方进行自动签署。

### 使用自动签署时为什么不能给签署方添加填写控件?

企业自动签署在发起时，需要保证合同内容的完整，所以发起后，签署方不能进行随意填写。

﻿