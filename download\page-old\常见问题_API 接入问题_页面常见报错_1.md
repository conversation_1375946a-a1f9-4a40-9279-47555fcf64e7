# 页面常见报错

> 导航路径: 常见问题 > API 接入问题 > 页面常见报错
> 来源: https://qian.tencent.com/document/116844
> 抓取时间: 2025-06-15 16:33:43

---

### 您暂无权限访问该内容。您当前登录的账号暂无权限访问该内容

如果页面出现以下错误，可能会有下面几种情况： 

﻿

﻿

情况一： 如果合同签署人有张三和李四，给`张三`生成的签署链接如果让`李四`打开，就会报此错误。 **解决方式：** 如果想让李四签署，在生成签署链接时需传入李四（而非张三）的信息。

情况二： 如果在半屏拉起时出现以上错误，请检查是否在联调环境拉起了正式的小程序。 **解决方式：**

联调环境需拉起 demo 小程序。

情况三：

如果在通过小程序领取指定了「动态签署方」的合同时出现以上错误。需要考虑是否是变更了模板所导致。由于生成领取链接时需要传入 `RecipientId`，而此 RecipientId 如果在集成时是通过查询模板信息而来，就可能会有问题，因为模板的任何调整都可能会引起 RecipientId 发生变化。

**解决方式：**

获取 `RecipientId` 通过创建合同时的返回，或者查询合同详情。不要查询模板获得。

**说明：**

例如，12点变更的模板，变之前的 recipientid 是 A，变后是 B。 但合同 a 是12点前发起的合同，12点后再去获取链接，这里 recipientid 要传 A。

### 您暂无权限访问该内容。当前登录用户不在此合同参与方名单中

情况一： 如果在合同发起时指定的签署人只有张三和李四，那么`王五`打开签署页面就会报如下错误。 

﻿

**﻿**

**解决方式：** 请确认是发起时指定错了签署人，还是把签署链接分享错了人。

情况二： 如果合同发起时指定的签署人是张三，**手机号** 为 `n1`，但张三打开签署页面的小程序当前绑定的手机号为 `n2`，就会报如下错误。 

﻿

**﻿**

**解决方式：**

方式1：张三在小程序中 [更换绑定的手机号](https://qian.tencent.com/document/76828 "https://qian.tencent.com/document/76828") 为 n1后，再打开签署页面。

方式2：发起合同时张三的手机号指定为 n2。

方式3：发起合同时带上张三的身份证号，同样可以用 n2签署合同。

**注意：**

如果限制了签署的校验方式为短信验证码，以上方式1是 n1收验证码，方式2和3是 n2收验证码，请根据情况处理。

### 认证失败。验证未通过，该手机号在运营商绑定的身份证与当前提交者的身份证不一致

签署时如果遇到以下报错，是由于运营商三要素校验未通过：

小程序 

﻿

﻿

H5 

﻿

﻿

**解决方式：**

情况一：客户手机号确实不是本人实名（即非本人去营业厅办理的手机号），建议更换成其他的签署认证方式（人脸或者签署密码）。

情况二：用户近期变更成自己实名手机号的情况：运营商数据都是月更的，移动和联通一般是每月的10号到20号左右更新，电信一般是每月的15号到次月的5号更新，有过变更的都是建议下个月再试试的，也不排除个别数据上报延迟导致更新有延迟/遗漏的情况。

### 当前手机号已被其他身份证绑定使用，无法继续认证，建议您更换其他手机号

该手机号已经被其他人注册实名电子签账号了，如果当前操作人要用这个手机号，可以先使用其他手机号注册，然后再换绑成这个手机号：

﻿

﻿

﻿  

### 您暂无权限访问该内容。当前签署链接属于指定的他人

在小程序签署时，如果报以下错误，是由于签署链接中指定了签署人比如是**张三** ，结果是**李四** 打开了这个签署链接。 

﻿

﻿

**注意：**

所谓「签署链接中指定」不仅包括了在生成链接时显式指定，也包括系统的判断。比如生成链接时只是指定了 `RecipientId`，但系统会根据 `RecipientId` 推断出当前链接该由哪个签署人签署。

### 非签署方企业的操作员

出现以下错误，是由于电子签要求**签署方** 都需要注册腾讯电子签账号。企业签署方如果不是超管，其他员工需要先加入企业才能签署。 

﻿

﻿

另外一个常见原因是，张三作为合同经办人虽然已经加入了企业，但加入企业时绑定的手机号为 `n1`，而合同发起时指定的张三手机号为 `n2`。此时可以让张三在腾讯电子签的小程序中 [更换其企业身份绑定的手机号](https://qian.tencent.com/document/93413/ "https://qian.tencent.com/document/93413/")。

**注意：**

电子签中的手机号分为个人身份和企业身份，这里需要关注的是企业身份的手机号。

### 登录手机号与签署人不一致

如果在小程序签署时出现如下错误，是由于发起合同时指定签署人张三的手机号为 `n1`（下图中的手机号），而张三在登录电子签时用的手机号为 `n2`（未实名）。 

﻿

﻿

**解决方式：**

方式1：根据下图引导，重新用 n1登录后进行签署。

方式2：重新发起合同，指定张三手机号为 n2。

### 暂不支持创建法定代表人章

在创建法人章时如果报以下错误，请检查企业（包括测试企业）的统一信用代码是否是**91** 开头，只有**91** 开头的企业才能创建法人章。 

﻿

目前系统仅支持以特定代码开头（51、52、53、91、12、N1、N2、N3、54）的统一社会信用代码的组织创建法人章。这些代码代表不同类型的机构，具体如下：

1机构编制：包括机关。

5民政：涵盖社会团体、民办非企业单位、基金会、村民委员会。

9市场监管：主要是企业。

N农业：包括组级集体经济组织、村级集体经济组织、乡镇级集体经济组织。

### 您使用的手机号与合同中的手机号不一致，导致无法查看合同

如果出现以下错误弹框，是由于此签署人之前已经在电子签实名过了，但绑定的手机并非合同发起时传入的手机号。 

﻿

﻿

**解决方式：**

方式1：引导用户在电子签小程序中 [更换绑定的手机号](https://qian.tencent.com/document/76828 "https://qian.tencent.com/document/76828") 后，再打开签署页面。

方式2：重发合同，手机号指定为用户在电子签绑定的手机号。

方式3：获取小程序签署链接时无需传入身份证号，签署时会自动进入换绑流程。

### 输入信息与合同参与人信息不一致，请检查手机号是否填写正确

如果出现以下错误弹框，是由于此签署人之前已经在电子签实名过了，但绑定的手机并非合同发起时传入的手机号。 

﻿

﻿

**解决方式：**

方式1：引导用户在电子签小程序中 [更换绑定的手机号](https://qian.tencent.com/document/76828 "https://qian.tencent.com/document/76828") 后，再打开签署页面。

方式2：重发合同，手机号指定为用户在电子签绑定的手机号。

方式3：获取小程序签署链接时无需传入身份证号，签署时会自动进入换绑流程。

### 人脸识别（H5）白屏是怎么回事

请检查是否使用了 iframe 嵌套。电子签的 H5链接不支持在 iframe 中使用。

### 张三登录电子签时要求李四扫脸

这种情况出现在张三首次登录电子签时，用了李四（已经在电子签绑定）的手机号，此时允许张三把这个手机号抢占过来，作为自己在电子签的手机号。此时需要注意走以下流程： 

﻿

﻿

### 合同查询方式已变更

如果个人用户在小程序端查询合同出现以下提示，是因为开启了“个人签后隐藏”功能。具体请咨询电子签客户经理。 

﻿

﻿

### 已存在实名账号 请使用原账号登录或申请换绑

客户的身份信息已经用其他手机号注册过电子签了，可以让用户确认实际要用的手机号，操作换绑或者用原手机号登录： 

﻿

﻿

### H5拉起人脸失败：抱歉，您无权限访问该页面

在集成电子签 H5页面时，如果拉起人脸时报以下错误，请检查是否给打开 H5的 App（包括浏览器）授权了摄像头权限。

﻿

﻿

﻿  

### 操作失败，缺少 mobile 参数

如果发起合同时，签署人的关联的手机号为 `xxx`，在此签署人还未签署时，另有他人实名认证时“抢占”了 `xxx`，原签署人再打开合同就会出现以下报错： 

﻿

﻿

### 您暂无权限访问该内容。无效的流程合同 ID

如果出现以下错误，且合同 ID 没有传错，很可能是因为在联调时拉起电子签的正式小程序。

﻿

﻿

﻿  

**解决方式：**

联调时有专门的 Demo 小程序，线上需换成正式小程序：

小程序| AppID| 原始 ID  
---|---|---  
腾讯电子签（正式版）| wxa023b292fd19d41d| gh_da88f6188665  
腾讯电子签 Demo| wx371151823f6f3edf| gh_39a5d3de69fa  
  
### 为什么 App 在集成 H5后人脸识别时报错？

请参考 [人脸核身 App 调用 H5兼容性配置指引](https://cloud.tencent.com/document/product/1007/61076) 进行适配处理。