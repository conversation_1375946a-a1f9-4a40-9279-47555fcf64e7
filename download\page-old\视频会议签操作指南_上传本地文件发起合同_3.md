# 上传本地文件发起合同

> 导航路径: 视频会议签操作指南 > 上传本地文件发起合同
> 来源: https://qian.tencent.com/document/78873/
> 抓取时间: 2025-06-15 16:34:25

---

本文档主要介绍如何在腾讯会议中通过腾讯电子签应用，上传文件发起合同。

## 前提条件 

请先完成腾讯电子签应用添加，请参见 [添加腾讯电子签应用](https://qian.tencent.com/document/78871)。

## 操作步骤

1. 单击**导入本地文件** ，选择需要签约的合同文件（目前支持60M以内的 pdf、word、excel 格式文件）。

﻿

﻿

﻿  

2. 合同文件上传后可以进行预览，确认后单击**与签约方投屏共享** 。 

﻿

﻿

﻿  

3. 确定开始共享应用画面，电子签应用内的合同页面将被投屏共享。

﻿

﻿

﻿  

4. 为了保证合同查看及填写的体验，应用将在投屏时独立弹出，并进入合同发起流程。**配置签约信息：** 左侧区域查看合同，右侧填写签署方信息以及合同信息（合同名称、签署截止时间），完成后单击**下一步** 。

﻿

﻿

﻿  

5. **指定签约区域：** 拖动每个签署方的签署控件到合同内容相应位置，完成后单击**下一步** 。

﻿

﻿

﻿  

6. **微信扫码签署：** 确认发起合同后，每个签署方均可使用微信扫码，或通过单击短信链接进入腾讯电子签小程序，进行签署及人脸识别验证。

﻿

﻿

﻿  

7. 所有签署方签署完成后，显示签署成功页面，可以将合同下载至本地，也可直接关闭电子签应用页面，结束屏幕共享。

﻿

﻿

﻿