#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版腾讯千帆文档爬虫 - 主程序
支持完整的侧边栏链接发现和JavaScript渲染
"""

import os
import sys
import argparse
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from enhanced_crawler import EnhancedTencentQianCrawler
    from url_manager import URLManager
    from config import OUTPUT_DIR, BASE_URL
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖文件都在正确位置")
    sys.exit(1)


def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'requests': 'requests',
        'bs4': 'beautifulsoup4',
        'html2text': 'html2text',
        'lxml': 'lxml',
        'jieba': 'jieba',
        'tqdm': 'tqdm'
    }
    
    missing_packages = []
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def create_output_directory():
    """创建输出目录"""
    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        print(f"输出目录: {OUTPUT_DIR}")
        return True
    except Exception as e:
        print(f"创建输出目录失败: {e}")
        return False


def show_status():
    """显示当前状态"""
    if not os.path.exists(OUTPUT_DIR):
        print("尚未开始爬取")
        return
        
    url_manager = URLManager(OUTPUT_DIR, BASE_URL)
    stats = url_manager.get_statistics()
    
    print("\n当前状态:")
    print(f"  发现URL总数: {stats['total_urls']}")
    print(f"  已完成: {stats['completed_urls']}")
    print(f"  失败: {stats['failed_urls']}")
    print(f"  待处理: {stats['pending_urls']}")
    print(f"  完成率: {stats['completion_rate']:.2f}%")
    
    # 显示最近的文件
    if os.path.exists(OUTPUT_DIR):
        md_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith('.md')]
        if md_files:
            print(f"\n已保存 {len(md_files)} 个Markdown文件")
            # 显示最新的几个文件
            md_files.sort(key=lambda x: os.path.getmtime(os.path.join(OUTPUT_DIR, x)), reverse=True)
            print("最新文件:")
            for f in md_files[:5]:
                print(f"  - {f}")


def discover_links_only():
    """仅发现链接，不进行抓取"""
    print("开始发现所有文档链接...")
    
    crawler = EnhancedTencentQianCrawler()
    crawler.discover_all_links()
    
    stats = crawler.url_manager.get_statistics()
    print(f"\n链接发现完成!")
    print(f"总共发现 {stats['total_urls']} 个URL")
    print(f"保存到: {OUTPUT_DIR}")


def run_crawler(args):
    """运行爬虫"""
    print("="*60)
    print("增强版腾讯千帆文档爬虫")
    print("="*60)
    
    # 创建爬虫实例
    crawler = EnhancedTencentQianCrawler()
    
    # 运行爬虫
    start_time = time.time()
    
    try:
        crawler.run(
            max_pages=args.pages,
            max_workers=args.workers,
            discover_links=not args.no_discover
        )
    except KeyboardInterrupt:
        print("\n用户中断爬取")
    except Exception as e:
        print(f"\n爬取过程中发生错误: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n总耗时: {duration:.2f} 秒")


def reset_progress():
    """重置进度"""
    if not os.path.exists(OUTPUT_DIR):
        print("没有找到进度文件")
        return
        
    confirm = input("确定要重置所有进度吗? (y/N): ")
    if confirm.lower() == 'y':
        url_manager = URLManager(OUTPUT_DIR, BASE_URL)
        url_manager.reset_progress()
        print("进度已重置")
    else:
        print("取消重置")


def generate_report():
    """生成详细报告"""
    if not os.path.exists(OUTPUT_DIR):
        print("没有找到数据文件")
        return
        
    url_manager = URLManager(OUTPUT_DIR, BASE_URL)
    report_file = url_manager.export_report()
    print(f"报告已生成: {report_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="增强版腾讯千帆文档爬虫",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s                          # 运行完整爬虫
  %(prog)s -p 50                    # 限制抓取50页
  %(prog)s -w 5                     # 使用5个并发线程
  %(prog)s --discover-only          # 仅发现链接，不抓取
  %(prog)s --status                 # 查看当前状态
  %(prog)s --reset                  # 重置进度
  %(prog)s --report                 # 生成报告
        """
    )
    
    # 基本选项
    parser.add_argument(
        '-p', '--pages',
        type=int,
        default=None,
        help='最大抓取页面数 (默认: 无限制)'
    )
    
    parser.add_argument(
        '-w', '--workers',
        type=int,
        default=3,
        help='并发线程数 (默认: 3)'
    )
    
    parser.add_argument(
        '--no-discover',
        action='store_true',
        help='跳过链接发现阶段'
    )
    
    # 功能选项
    parser.add_argument(
        '--discover-only',
        action='store_true',
        help='仅发现链接，不进行抓取'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前状态'
    )
    
    parser.add_argument(
        '--reset',
        action='store_true',
        help='重置进度'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='生成详细报告'
    )
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建输出目录
    if not create_output_directory():
        sys.exit(1)
    
    # 执行相应功能
    if args.status:
        show_status()
    elif args.reset:
        reset_progress()
    elif args.report:
        generate_report()
    elif args.discover_only:
        discover_links_only()
    else:
        # 运行完整爬虫
        run_crawler(args)


if __name__ == "__main__":
    main() 