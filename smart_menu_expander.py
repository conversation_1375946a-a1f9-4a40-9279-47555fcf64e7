#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能菜单展开器 - 专门处理JavaScript动态菜单
针对Docusaurus框架优化
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class SmartMenuExpander:
    """智能菜单展开器"""
    
    def __init__(self, driver, logger=None):
        self.driver = driver
        self.logger = logger or logging.getLogger(__name__)
        self.expanded_elements = set()  # 记录已展开的元素
        self.total_expanded = 0
    
    def expand_all_menus(self, max_rounds=10):
        """智能展开所有菜单"""
        
        self.logger.info("开始智能菜单展开")
        
        for round_num in range(max_rounds):
            round_expanded = 0
            
            # 1. 查找所有可能的折叠元素
            collapsible_elements = self.find_collapsible_elements()
            
            # 2. 过滤已处理的元素
            new_elements = [elem for elem in collapsible_elements 
                          if self.get_element_id(elem) not in self.expanded_elements]
            
            if not new_elements:
                self.logger.info(f"第{round_num + 1}轮：没有发现新的折叠元素")
                break
                
            self.logger.info(f"第{round_num + 1}轮：发现 {len(new_elements)} 个新的折叠元素")
            
            # 3. 逐个展开元素
            for element in new_elements:
                if self.expand_element(element):
                    round_expanded += 1
                    self.total_expanded += 1
                    self.expanded_elements.add(self.get_element_id(element))
            
            # 4. 等待动态内容加载
            self.wait_for_content_update()
            
            self.logger.info(f"第{round_num + 1}轮：成功展开 {round_expanded} 个元素")
            
            # 如果这轮没有展开任何元素，提前结束
            if round_expanded == 0:
                break
        
        self.logger.info(f"菜单展开完成，总共展开了 {self.total_expanded} 个菜单元素")
        return self.total_expanded
    
    def find_collapsible_elements(self):
        """查找所有可折叠元素"""
        
        # Docusaurus特定的选择器
        docusaurus_selectors = [
            # 主要的折叠菜单选择器
            '.menu__list-item--collapsed .menu__link--sublist-caret',
            '.theme-doc-sidebar-item-category--collapsed .menu__link--sublist-caret',
            
            # 通用的折叠状态选择器
            '.menu__list-item--collapsed',
            '.theme-doc-sidebar-item-category--collapsed',
            
            # aria属性选择器
            '[aria-expanded="false"]',
            
            # 其他可能的选择器
            '.collapsed .menu__link--sublist-caret',
            '.menu__link--sublist[aria-expanded="false"]'
        ]
        
        # 备用选择器
        backup_selectors = [
            '.collapsed .expand-button',
            '.collapsed .toggle-button',
            '.sidebar-item.collapsed',
            '.nav-item.collapsed',
            '.tree-item.collapsed',
            '[class*="collapsed"]',
            '[class*="closed"]',
            '[class*="folded"]'
        ]
        
        all_elements = []
        
        # 首先尝试Docusaurus特定选择器
        for selector in docusaurus_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    if self.is_valid_collapsible_element(elem):
                        all_elements.append(elem)
                        
                if elements:  # 如果找到了元素，优先使用这些
                    self.logger.debug(f"使用Docusaurus选择器 {selector} 找到 {len(elements)} 个元素")
                    
            except Exception as e:
                self.logger.debug(f"Docusaurus选择器 {selector} 查找失败: {e}")
        
        # 如果Docusaurus选择器没找到足够的元素，使用备用选择器
        if len(all_elements) < 5:
            for selector in backup_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for elem in elements:
                        if self.is_valid_collapsible_element(elem):
                            all_elements.append(elem)
                except Exception as e:
                    self.logger.debug(f"备用选择器 {selector} 查找失败: {e}")
        
        # 去重
        unique_elements = []
        seen_ids = set()
        
        for elem in all_elements:
            elem_id = self.get_element_id(elem)
            if elem_id not in seen_ids:
                unique_elements.append(elem)
                seen_ids.add(elem_id)
        
        self.logger.debug(f"总共找到 {len(unique_elements)} 个唯一的可折叠元素")
        return unique_elements
    
    def is_valid_collapsible_element(self, element):
        """验证元素是否为有效的可折叠元素"""
        try:
            # 检查元素是否可见
            if not element.is_displayed():
                return False
            
            # 检查元素大小
            size = element.size
            if size['width'] == 0 or size['height'] == 0:
                return False
            
            # 检查是否在视口内（允许一定的偏移）
            location = element.location
            if location['y'] < -2000 or location['y'] > 10000:
                return False
            
            # 检查是否已经展开
            aria_expanded = element.get_attribute('aria-expanded')
            if aria_expanded == 'true':
                return False
            
            # 检查类名
            class_names = element.get_attribute('class') or ''
            
            # 对于Docusaurus，检查特定的折叠类
            if 'menu__list-item--collapsed' in class_names:
                return True
            if 'theme-doc-sidebar-item-category--collapsed' in class_names:
                return True
            
            # 通用的折叠检查
            if any(keyword in class_names.lower() for keyword in ['collapsed', 'closed', 'folded']):
                # 进一步检查父元素
                try:
                    parent = element.find_element(By.XPATH, '..')
                    parent_classes = parent.get_attribute('class') or ''
                    if any(keyword in parent_classes.lower() for keyword in ['collapsed', 'closed']):
                        return True
                except:
                    pass
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"验证元素失败: {e}")
            return False
    
    def expand_element(self, element):
        """展开单个元素"""
        try:
            # 滚动到元素可见
            self.scroll_to_element(element)
            
            # 尝试多种点击方式
            click_methods = [
                lambda: element.click(),
                lambda: self.driver.execute_script("arguments[0].click();", element),
                lambda: ActionChains(self.driver).click(element).perform(),
                lambda: self.click_with_js_event(element),
                lambda: self.click_parent_element(element)
            ]
            
            for i, click_method in enumerate(click_methods):
                try:
                    # 记录点击前的状态
                    before_aria = element.get_attribute('aria-expanded')
                    before_class = element.get_attribute('class') or ''
                    
                    click_method()
                    
                    # 等待展开动画
                    time.sleep(1.5)
                    
                    # 验证是否成功展开
                    if self.verify_expansion(element, before_aria, before_class):
                        self.logger.debug(f"使用方法{i+1}成功展开元素")
                        return True
                    
                except Exception as e:
                    self.logger.debug(f"点击方法{i+1}失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.debug(f"展开元素失败: {e}")
            return False
    
    def scroll_to_element(self, element):
        """滚动到元素可见"""
        try:
            # 滚动到元素中心
            self.driver.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            """, element)
            
            time.sleep(0.8)
            
        except Exception as e:
            self.logger.debug(f"滚动到元素失败: {e}")
    
    def click_with_js_event(self, element):
        """使用JavaScript事件点击"""
        self.driver.execute_script("""
            var element = arguments[0];
            var event = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        """, element)
    
    def click_parent_element(self, element):
        """点击父元素（有时需要点击包含元素）"""
        try:
            parent = element.find_element(By.XPATH, '..')
            parent.click()
        except Exception as e:
            self.logger.debug(f"点击父元素失败: {e}")
            raise e
    
    def verify_expansion(self, element, before_aria, before_class):
        """验证元素是否已展开"""
        try:
            # 等待一下让DOM更新
            time.sleep(0.5)
            
            # 检查aria-expanded属性变化
            after_aria = element.get_attribute('aria-expanded')
            if before_aria == 'false' and after_aria == 'true':
                return True
            
            # 检查类名变化
            after_class = element.get_attribute('class') or ''
            
            # Docusaurus特定的类名变化
            if 'menu__list-item--collapsed' in before_class and 'menu__list-item--collapsed' not in after_class:
                return True
            if 'theme-doc-sidebar-item-category--collapsed' in before_class and 'theme-doc-sidebar-item-category--collapsed' not in after_class:
                return True
            
            # 通用的类名变化检查
            if 'collapsed' in before_class.lower() and 'collapsed' not in after_class.lower():
                return True
            if 'expanded' in after_class.lower() or 'open' in after_class.lower():
                return True
            
            # 检查是否有新的子元素出现
            try:
                # 查找可能的子菜单容器
                submenu_selectors = [
                    '.menu__list',
                    'ul',
                    '.submenu',
                    '[class*="submenu"]'
                ]
                
                for selector in submenu_selectors:
                    submenus = element.find_elements(By.CSS_SELECTOR, selector)
                    for submenu in submenus:
                        if submenu.is_displayed() and submenu.size['height'] > 0:
                            return True
            except:
                pass
            
            return False
            
        except Exception as e:
            self.logger.debug(f"验证展开状态失败: {e}")
            return False
    
    def wait_for_content_update(self):
        """等待内容更新"""
        try:
            # 等待JavaScript执行完成
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 等待可能的AJAX请求完成
            try:
                WebDriverWait(self.driver, 5).until(
                    lambda driver: driver.execute_script(
                        "return (typeof jQuery !== 'undefined') ? jQuery.active === 0 : true"
                    )
                )
            except:
                pass  # 如果没有jQuery，忽略这个检查
            
            # 额外等待时间让动画完成
            time.sleep(2)
            
        except TimeoutException:
            self.logger.warning("等待内容更新超时")
    
    def get_element_id(self, element):
        """获取元素唯一标识"""
        try:
            # 尝试获取ID
            elem_id = element.get_attribute('id')
            if elem_id:
                return f"id:{elem_id}"
            
            # 尝试获取href属性
            href = element.get_attribute('href')
            if href:
                return f"href:{href}"
            
            # 使用文本内容和类名组合
            text = element.get_attribute('textContent') or ''
            class_name = element.get_attribute('class') or ''
            
            if text.strip():
                return f"text:{text.strip()[:50]}:{class_name}"
            
            # 最后使用XPath
            xpath = self.driver.execute_script("""
                function getXPath(element) {
                    if (element.id !== '') {
                        return 'id("' + element.id + '")';
                    }
                    if (element === document.body) {
                        return element.tagName;
                    }
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }
                return getXPath(arguments[0]);
            """, element)
            
            return f"xpath:{xpath}"
            
        except Exception:
            return f"hash:{hash(str(element))}"