#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL去重和管理工具
用于处理发现的URL，去除重复项，标准化格式
"""

import os
import re
from urllib.parse import urlparse, urljoin
from typing import Set, List, Dict
import logging

class URLDeduplicator:
    """URL去重和管理类"""
    
    def __init__(self, download_dir: str = "./download"):
        self.download_dir = download_dir
        self.logger = logging.getLogger(__name__)
        
        # 确保目录存在
        os.makedirs(download_dir, exist_ok=True)
        
        # 文件路径
        self.all_urls_file = os.path.join(download_dir, "all_urls.txt")
        self.completed_urls_file = os.path.join(download_dir, "completed_urls.txt")
        self.failed_urls_file = os.path.join(download_dir, "failed_urls.txt")
        self.pending_urls_file = os.path.join(download_dir, "pending_urls.txt")
        
    def normalize_url(self, url: str) -> str:
        """标准化URL格式"""
        if not url:
            return ""
            
        # 移除末尾的斜杠
        url = url.rstrip('/')
        
        # 确保是完整的URL
        if not url.startswith('http'):
            if url.startswith('/document/'):
                url = f"https://qian.tencent.com{url}"
            elif url.startswith('document/'):
                url = f"https://qian.tencent.com/{url}"
        
        return url
    
    def load_urls_from_file(self, filepath: str, normalize: bool = True) -> Set[str]:
        """从文件加载URL集合"""
        urls = set()
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    for line in f:
                        url = line.strip()
                        if url:
                            if normalize:
                                normalized_url = self.normalize_url(url)
                                if normalized_url:
                                    urls.add(normalized_url)
                            else:
                                urls.add(url)
            except Exception as e:
                self.logger.error(f"读取文件 {filepath} 失败: {e}")
        return urls
    
    def save_urls_to_file(self, urls: Set[str], filepath: str, preserve_original: bool = False):
        """保存URL集合到文件"""
        try:
            # 如果是保护原始文件，先备份
            if preserve_original and filepath == self.all_urls_file:
                backup_file = filepath + ".backup"
                if os.path.exists(filepath):
                    import shutil
                    shutil.copy2(filepath, backup_file)
                    self.logger.info(f"已备份原始文件到 {backup_file}")
            
            with open(filepath, 'w', encoding='utf-8') as f:
                for url in sorted(urls):
                    f.write(f"{url}\n")
            self.logger.info(f"已保存 {len(urls)} 个URL到 {filepath}")
        except Exception as e:
            self.logger.error(f"保存文件 {filepath} 失败: {e}")
    
    def analyze_urls_without_modification(self) -> Dict[str, int]:
        """分析URL状态但不修改原始all_urls.txt文件"""
        self.logger.info("开始分析URL状态（不修改原始文件）...")
        
        # 加载现有URL（不标准化，保持原始格式）
        all_urls_raw = self.load_urls_from_file(self.all_urls_file, normalize=False)
        
        # 标准化用于比较
        all_urls_normalized = set()
        for url in all_urls_raw:
            normalized = self.normalize_url(url)
            if normalized:
                all_urls_normalized.add(normalized)
        
        completed_urls = self.load_urls_from_file(self.completed_urls_file)
        failed_urls = self.load_urls_from_file(self.failed_urls_file)
        
        # 统计信息
        original_count = len(all_urls_raw)
        normalized_count = len(all_urls_normalized)
        
        # 计算待处理的URL
        pending_urls = all_urls_normalized - completed_urls - failed_urls
        
        # 只保存状态文件，不修改all_urls.txt
        self.save_urls_to_file(completed_urls, self.completed_urls_file)
        self.save_urls_to_file(failed_urls, self.failed_urls_file)
        self.save_urls_to_file(pending_urls, self.pending_urls_file)
        
        # 返回统计信息
        stats = {
            'total_urls_raw': len(all_urls_raw),
            'total_urls_normalized': len(all_urls_normalized),
            'completed_urls': len(completed_urls),
            'failed_urls': len(failed_urls),
            'pending_urls': len(pending_urls),
            'original_count': original_count,
            'duplicates_found': original_count - normalized_count
        }
        
        self.logger.info(f"分析完成: 原始URL {stats['total_urls_raw']}, "
                        f"去重后 {stats['total_urls_normalized']}, "
                        f"已完成 {stats['completed_urls']}, "
                        f"失败 {stats['failed_urls']}, "
                        f"待处理 {stats['pending_urls']}, "
                        f"发现重复 {stats['duplicates_found']}")
        
        return stats
    
    def add_discovered_urls(self, new_urls: List[str]) -> int:
        """添加新发现的URL到all_urls.txt（追加模式）"""
        if not new_urls:
            return 0
            
        # 加载现有URL（保持原始格式）
        existing_urls_raw = self.load_urls_from_file(self.all_urls_file, normalize=False)
        existing_urls_normalized = set()
        for url in existing_urls_raw:
            normalized = self.normalize_url(url)
            if normalized:
                existing_urls_normalized.add(normalized)
        
        # 找出真正新的URL
        new_urls_to_add = []
        for url in new_urls:
            normalized_url = self.normalize_url(url)
            if normalized_url and normalized_url not in existing_urls_normalized:
                new_urls_to_add.append(url)  # 保持原始格式
        
        # 追加到all_urls.txt文件
        if new_urls_to_add:
            try:
                with open(self.all_urls_file, 'a', encoding='utf-8') as f:
                    for url in new_urls_to_add:
                        f.write(f"{url}\n")
                self.logger.info(f"追加了 {len(new_urls_to_add)} 个新URL到 {self.all_urls_file}")
            except Exception as e:
                self.logger.error(f"追加URL失败: {e}")
                return 0
        
        # 更新待处理URL列表
        self.update_pending_urls()
        
        return len(new_urls_to_add)
    
    def mark_url_completed(self, url: str):
        """标记URL为已完成"""
        normalized_url = self.normalize_url(url)
        if not normalized_url:
            return
            
        # 加载现有URL
        completed_urls = self.load_urls_from_file(self.completed_urls_file)
        failed_urls = self.load_urls_from_file(self.failed_urls_file)
        
        # 添加到已完成列表，从失败列表移除
        completed_urls.add(normalized_url)
        failed_urls.discard(normalized_url)
        
        # 保存更新
        self.save_urls_to_file(completed_urls, self.completed_urls_file)
        self.save_urls_to_file(failed_urls, self.failed_urls_file)
        
        # 更新待处理列表
        self.update_pending_urls()
    
    def mark_url_failed(self, url: str):
        """标记URL为失败"""
        normalized_url = self.normalize_url(url)
        if not normalized_url:
            return
            
        # 加载现有URL
        completed_urls = self.load_urls_from_file(self.completed_urls_file)
        failed_urls = self.load_urls_from_file(self.failed_urls_file)
        
        # 添加到失败列表，从已完成列表移除
        failed_urls.add(normalized_url)
        completed_urls.discard(normalized_url)
        
        # 保存更新
        self.save_urls_to_file(completed_urls, self.completed_urls_file)
        self.save_urls_to_file(failed_urls, self.failed_urls_file)
        
        # 更新待处理列表
        self.update_pending_urls()
    
    def update_pending_urls(self):
        """更新待处理URL列表"""
        # 标准化all_urls用于比较
        all_urls_raw = self.load_urls_from_file(self.all_urls_file, normalize=False)
        all_urls_normalized = set()
        for url in all_urls_raw:
            normalized = self.normalize_url(url)
            if normalized:
                all_urls_normalized.add(normalized)
        
        completed_urls = self.load_urls_from_file(self.completed_urls_file)
        failed_urls = self.load_urls_from_file(self.failed_urls_file)
        
        pending_urls = all_urls_normalized - completed_urls - failed_urls
        self.save_urls_to_file(pending_urls, self.pending_urls_file)
    
    def get_statistics(self) -> Dict[str, int]:
        """获取URL统计信息"""
        # 获取原始和标准化的URL数量
        all_urls_raw = self.load_urls_from_file(self.all_urls_file, normalize=False)
        all_urls_normalized = set()
        for url in all_urls_raw:
            normalized = self.normalize_url(url)
            if normalized:
                all_urls_normalized.add(normalized)
        
        completed_urls = self.load_urls_from_file(self.completed_urls_file)
        failed_urls = self.load_urls_from_file(self.failed_urls_file)
        pending_urls = all_urls_normalized - completed_urls - failed_urls
        
        total_raw = len(all_urls_raw)
        total_normalized = len(all_urls_normalized)
        completed = len(completed_urls)
        failed = len(failed_urls)
        pending = len(pending_urls)
        
        completion_rate = (completed / total_normalized * 100) if total_normalized > 0 else 0
        
        return {
            'total_urls_raw': total_raw,
            'total_urls': total_normalized,
            'completed_urls': completed,
            'failed_urls': failed,
            'pending_urls': pending,
            'completion_rate': completion_rate,
            'duplicates': total_raw - total_normalized
        }
    
    def get_pending_urls(self, limit: int = None) -> List[str]:
        """获取待处理的URL列表"""
        pending_urls = self.load_urls_from_file(self.pending_urls_file)
        urls_list = sorted(list(pending_urls))
        
        if limit:
            return urls_list[:limit]
        return urls_list
    
    def generate_report(self) -> str:
        """生成URL管理报告"""
        stats = self.get_statistics()
        
        report = f"""# URL管理报告

## 统计信息
- **原始URL数量**: {stats['total_urls_raw']}
- **去重后URL数量**: {stats['total_urls']}
- **重复URL数量**: {stats['duplicates']}
- **已完成**: {stats['completed_urls']}
- **失败**: {stats['failed_urls']}
- **待处理**: {stats['pending_urls']}
- **完成率**: {stats['completion_rate']:.2f}%

## 文件位置
- 所有URL（原始）: {self.all_urls_file}
- 已完成URL: {self.completed_urls_file}
- 失败URL: {self.failed_urls_file}
- 待处理URL: {self.pending_urls_file}

## 进度状态
"""
        
        if stats['pending_urls'] > 0:
            report += f"还有 {stats['pending_urls']} 个URL待处理\n"
        else:
            report += "所有URL已处理完成！\n"
            
        return report


def main():
    """主函数 - 用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description="URL去重和管理工具")
    parser.add_argument("--analyze", action="store_true", help="分析URL状态（不修改原始文件）")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    parser.add_argument("--report", action="store_true", help="生成报告")
    parser.add_argument("--pending", type=int, help="显示待处理URL（指定数量）")
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    deduplicator = URLDeduplicator()
    
    if args.analyze:
        stats = deduplicator.analyze_urls_without_modification()
        print(f"分析完成！原始数量: {stats['total_urls_raw']}, 去重后: {stats['total_urls_normalized']}, 重复: {stats['duplicates_found']}")
        
    if args.stats:
        stats = deduplicator.get_statistics()
        print(f"原始URL: {stats['total_urls_raw']}")
        print(f"去重后URL: {stats['total_urls']}")
        print(f"重复URL: {stats['duplicates']}")
        print(f"已完成: {stats['completed_urls']}")
        print(f"失败: {stats['failed_urls']}")
        print(f"待处理: {stats['pending_urls']}")
        print(f"完成率: {stats['completion_rate']:.2f}%")
        
    if args.report:
        report = deduplicator.generate_report()
        print(report)
        
    if args.pending:
        pending_urls = deduplicator.get_pending_urls(args.pending)
        print(f"待处理URL（前{len(pending_urls)}个）:")
        for i, url in enumerate(pending_urls, 1):
            print(f"{i}. {url}")


if __name__ == "__main__":
    main()