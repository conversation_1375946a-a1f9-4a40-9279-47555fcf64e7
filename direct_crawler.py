#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接抓取脚本 - 绕过URL管理器初始化问题
"""

import os
import time
import random
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
from bs4 import BeautifulSoup
import html2text
from config import *
from utils import *

class DirectCrawler:
    """直接抓取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
        # HTML转Markdown
        self.html2text = html2text.HTML2Text()
        self.html2text.ignore_links = False
        self.html2text.ignore_images = True
        self.html2text.body_width = 0
        self.html2text.unicode_snob = True
        
        # 统计
        self.stats = {'success': 0, 'failed': 0}
        
    def setup_session(self):
        """设置HTTP会话"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(REQUEST_CONFIG['user_agents'])
    
    def fetch_page(self, url):
        """获取页面内容"""
        try:
            headers = {
                'User-Agent': self.get_random_user_agent(),
                'Accept-Charset': 'utf-8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
            }
            
            # 手动处理重定向
            current_url = url
            redirect_count = 0
            max_redirects = 3
            
            while redirect_count <= max_redirects:
                response = self.session.get(
                    current_url,
                    headers=headers,
                    timeout=30,
                    allow_redirects=False
                )
                
                if response.status_code in [301, 302, 303, 307, 308]:
                    redirect_url = response.headers.get('Location')
                    if redirect_url:
                        redirect_count += 1
                        if redirect_count > max_redirects:
                            return False, f"Too many redirects (>{max_redirects})"
                        
                        # 检查域名安全性
                        from urllib.parse import urlparse, urljoin
                        if not redirect_url.startswith('http'):
                            redirect_url = urljoin(current_url, redirect_url)
                        
                        redirect_domain = urlparse(redirect_url).netloc
                        if not (redirect_domain.endswith('.tencent.com') or redirect_domain == 'qian.tencent.com'):
                            return False, "Suspicious redirect domain"
                        
                        current_url = redirect_url
                        continue
                
                response.encoding = 'utf-8'
                if response.status_code == 200:
                    content = response.text
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='ignore')
                    return True, content
                else:
                    return False, f"HTTP {response.status_code}"
                    
        except Exception as e:
            return False, str(e)
    
    def extract_title(self, soup):
        """提取页面标题"""
        title_selectors = [
            'h1',
            '.theme-doc-markdown h1',
            'title',
            '.docTitle_node_modules',
            '[data-testid="doc-title"]',
            '.markdown h1'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                if title and title != "文档中心":
                    return title
                    
        return "未知标题"
    
    def extract_breadcrumb_path(self, soup):
        """提取面包屑导航路径"""
        breadcrumb_selectors = [
            '.breadcrumb',
            '.breadcrumbs',
            '.nav-breadcrumb',
            '.theme-doc-breadcrumbs',
            '[aria-label="breadcrumb"]',
            '.breadcrumb-container',
            '.page-breadcrumb'
        ]
        
        for selector in breadcrumb_selectors:
            breadcrumb_elem = soup.select_one(selector)
            if breadcrumb_elem:
                links = breadcrumb_elem.find_all(['a', 'span', 'li'])
                path_parts = []
                seen_parts = set()
                
                for link in links:
                    text = link.get_text().strip()
                    if text and text != "文档中心" and text != ">" and text != "/" and text not in seen_parts:
                        path_parts.append(text)
                        seen_parts.add(text)
                
                if len(path_parts) > 1:
                    return path_parts
        
        # 备用方案
        title = self.extract_title(soup)
        if title and title != "未知标题":
            return [title]
        return []
    
    def extract_main_content(self, soup):
        """提取主要内容"""
        content_selectors = [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR',
            'main .container',
            '.tea-editable'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                self.clean_content(content_elem)
                return str(content_elem)
                
        # 如果没找到特定容器，返回body内容
        body = soup.find('body')
        if body:
            self.clean_content(body)
            return str(body)
            
        return str(soup)
    
    def clean_content(self, content_element):
        """清理内容元素"""
        unwanted_selectors = [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb',
            '.pagination', '.feedback', '.advertisement',
            '[class*="ad-"]', '[id*="ad-"]',
            '.theme-doc-sidebar-container',
            '.tocCollapsible_ETCw',
            '.pagination-nav',
            '.feedback-panel'
        ]
        
        for selector in unwanted_selectors:
            for elem in content_element.select(selector):
                elem.decompose()
    
    def generate_filename_from_breadcrumb(self, breadcrumb_path):
        """根据面包屑路径生成文件名"""
        if not breadcrumb_path:
            return "未知页面"
        
        filtered_path = [part for part in breadcrumb_path if part != "文档中心"]
        if not filtered_path:
            return "未知页面"
        
        filename = "_".join(filtered_path)
        
        # 清理文件名中的非法字符
        forbidden_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for char in forbidden_chars:
            filename = filename.replace(char, '_')
        
        # 限制文件名长度
        if len(filename) > 50:
            filename = filename[:50]
        
        return filename
    
    def save_content(self, content_info):
        """保存内容到文件"""
        try:
            # 根据面包屑路径生成文件名
            breadcrumb_path = content_info.get('breadcrumb_path', [])
            if breadcrumb_path:
                filename = self.generate_filename_from_breadcrumb(breadcrumb_path)
            else:
                filename = content_info['title']
                forbidden_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
                for char in forbidden_chars:
                    filename = filename.replace(char, '_')
            
            # 确保文件名是有效的UTF-8字符串
            if isinstance(filename, bytes):
                filename = filename.decode('utf-8', errors='ignore')
            
            # 添加扩展名
            filename += '.md'
            
            # 确保页面输出目录存在
            page_output_dir = './download/page'
            if not os.path.exists(page_output_dir):
                os.makedirs(page_output_dir)
            
            # 确保文件名唯一
            filepath = os.path.join(page_output_dir, filename)
            counter = 1
            base_filepath = filepath
            while os.path.exists(filepath):
                name, ext = os.path.splitext(base_filepath)
                filepath = f"{name}_{counter}{ext}"
                counter += 1
            
            # 准备文件内容
            file_content = f"# {content_info['title']}\n\n"
            
            # 添加面包屑信息
            if breadcrumb_path:
                breadcrumb_str = " > ".join(breadcrumb_path)
                file_content += f"> 导航路径: {breadcrumb_str}\n"
            
            file_content += f"> 来源: {content_info['url']}\n"
            file_content += f"> 抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            file_content += "---\n\n"
            file_content += content_info['content']
            
            # 确保内容是字符串格式
            if isinstance(file_content, bytes):
                file_content = file_content.decode('utf-8', errors='ignore')
            
            # 保存文件（使用UTF-8编码）
            with open(filepath, 'w', encoding='utf-8', newline='\n') as f:
                f.write(file_content)
                
            safe_filename = os.path.basename(filepath)
            if isinstance(safe_filename, bytes):
                safe_filename = safe_filename.decode('utf-8', errors='replace')
            
            print(f"保存文件: {safe_filename}")
            return True
            
        except Exception as e:
            print(f"保存文件失败: {str(e)}")
            return False
    
    def extract_content(self, html, url):
        """提取页面内容"""
        soup = BeautifulSoup(html, 'lxml')
        
        # 提取标题
        title = self.extract_title(soup)
        
        # 提取面包屑路径
        breadcrumb_path = self.extract_breadcrumb_path(soup)
        
        # 提取主要内容
        main_content = self.extract_main_content(soup)
        
        # 转换为Markdown
        try:
            markdown_content = self.html2text.handle(main_content)
            # 清理多余的空行
            import re
            markdown_content = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_content)
            markdown_content = markdown_content.strip()
        except Exception as e:
            print(f"HTML转Markdown失败: {str(e)}")
            markdown_content = main_content
        
        return {
            'url': url,
            'title': title,
            'content': markdown_content,
            'breadcrumb_path': breadcrumb_path
        }
    
    def crawl_single_page(self, url):
        """爬取单个页面"""
        try:
            # 随机等待 200ms-2000ms
            wait_time = random.uniform(0.2, 2.0)
            time.sleep(wait_time)
            
            # 获取页面内容
            success, html_content = self.fetch_page(url)
            
            if not success:
                print(f"获取页面失败: {url} - {html_content}")
                self.stats['failed'] += 1
                return False
                
            # 提取内容
            content_info = self.extract_content(html_content, url)
            
            # 保存内容
            save_success = self.save_content(content_info)
            
            if save_success:
                self.stats['success'] += 1
            else:
                self.stats['failed'] += 1
                
            return save_success
            
        except Exception as e:
            print(f"爬取页面失败: {url} - {str(e)}")
            self.stats['failed'] += 1
            return False
    
    def crawl_urls(self, urls, max_workers=4):
        """并行抓取URL列表"""
        print(f"开始抓取 {len(urls)} 个页面，并发数: {max_workers}")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = {
                executor.submit(self.crawl_single_page, url): url 
                for url in urls
            }
            
            # 处理结果
            with tqdm(total=len(futures), desc="抓取进度", unit="页") as pbar:
                for future in as_completed(futures):
                    url = futures[future]
                    try:
                        result = future.result()
                    except Exception as e:
                        print(f"抓取异常 {url}: {e}")
                        self.stats['failed'] += 1
                    
                    pbar.update(1)
                    pbar.set_postfix({
                        '成功': self.stats['success'],
                        '失败': self.stats['failed']
                    })
        
        print(f"\n抓取完成！成功: {self.stats['success']}, 失败: {self.stats['failed']}")

def main():
    """主函数"""
    # 读取URL列表
    all_urls_file = './download/all_urls.txt'
    if not os.path.exists(all_urls_file):
        print("all_urls.txt文件不存在")
        return
    
    with open(all_urls_file, 'r', encoding='utf-8') as f:
        urls = [line.strip() for line in f if line.strip()]
    
    print(f"从all_urls.txt读取到 {len(urls)} 个URL")
    
    # 创建爬虫并开始抓取
    crawler = DirectCrawler()
    crawler.crawl_urls(urls, max_workers=4)

if __name__ == "__main__":
    main()