#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合策略爬虫 - 结合Selenium和requests的优势
专门针对JavaScript动态内容和大规模抓取优化
"""

import os
import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

from base_crawler import BaseCrawler
from comprehensive_link_discoverer import ComprehensiveLinkDiscoverer
from config import *
from utils import *


class HybridTencentQianCrawler(BaseCrawler):
    """混合策略爬虫 - 结合Selenium链接发现和requests内容抓取"""
    
    def __init__(self, headless=True, use_selenium=True):
        super().__init__()
        
        self.headless = headless
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        
        # Selenium驱动
        self.driver = None
        if self.use_selenium:
            self.setup_selenium()
        
        # 链接发现器
        self.link_discoverer = ComprehensiveLinkDiscoverer(self)
        
        self.logger.info(f"混合策略爬虫初始化完成，Selenium: {self.use_selenium}")
    
    def setup_selenium(self):
        """设置Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            self.logger.warning("Selenium不可用，将使用普通HTTP请求")
            self.use_selenium = False
            return
            
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 优化选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript-harmony-shipping')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            
            # 设置用户代理
            chrome_options.add_argument(f'--user-agent={random.choice(REQUEST_CONFIG["user_agents"])}')
            
            # 设置窗口大小
            chrome_options.add_argument('--window-size=1920,1080')
            
            # 禁用日志
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 创建驱动
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            
            # 设置页面加载超时
            self.driver.set_page_load_timeout(30)
            
            self.logger.info("Selenium WebDriver初始化成功")
            
        except Exception as e:
            self.logger.error(f"Selenium初始化失败: {str(e)}")
            self.use_selenium = False
    
    def discover_links(self):
        """发现所有文档链接"""
        self.logger.info("开始混合策略链接发现")
        
        try:
            # 使用综合链接发现器
            all_links = self.link_discoverer.discover_all_links()
            
            # 更新URL管理器
            added_count = self.url_manager.add_urls_batch(all_links, self.start_url)
            self.logger.info(f"混合策略总共发现 {len(all_links)} 个链接，新增 {added_count} 个")
            
            return all_links
            
        except Exception as e:
            self.logger.error(f"链接发现失败: {str(e)}")
            return []
    
    def fetch_page(self, url):
        """获取页面内容 - 优先使用requests"""
        return self.fetch_page_requests(url)
    
    def crawl_content_parallel(self, urls, max_pages=None, max_workers=None):
        """并行内容抓取"""
        
        if max_workers is None:
            max_workers = PROGRESS_CONFIG['max_concurrent']
        
        if max_pages:
            urls = urls[:max_pages]
        
        self.logger.info(f"开始并行内容抓取，目标页面数: {len(urls)}, 并发数: {max_workers}")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = {
                executor.submit(self.crawl_single_page, url): url 
                for url in urls
            }
            
            # 处理结果
            with tqdm(total=len(futures), desc="抓取进度", unit="页") as pbar:
                for future in as_completed(futures):
                    url = futures[future]
                    try:
                        result = future.result()
                        if result:
                            self.stats['total_crawled'] += 1
                        else:
                            self.stats['total_failed'] += 1
                    except Exception as e:
                        self.logger.error(f"抓取失败 {url}: {e}")
                        self.stats['total_failed'] += 1
                    
                    pbar.update(1)
                    pbar.set_postfix({
                        '成功': self.stats['total_crawled'],
                        '失败': self.stats['total_failed']
                    })
                    
                    # 定期保存进度
                    if (self.stats['total_crawled'] + self.stats['total_failed']) % PROGRESS_CONFIG['save_interval'] == 0:
                        self.url_manager.save_urls()
    
    def run(self, max_pages=None, max_workers=None, discover_links=True):
        """运行混合策略爬虫"""
        
        if max_workers is None:
            max_workers = PROGRESS_CONFIG['max_concurrent']
        
        self.logger.info(f"开始混合策略爬取")
        self.logger.info(f"目标页面数: {max_pages or '无限制'}")
        self.logger.info(f"并发线程数: {max_workers}")
        self.logger.info(f"Selenium状态: {'启用' if self.use_selenium else '禁用'}")
        
        start_time = time.time()
        
        try:
            # 阶段1：链接发现（使用Selenium）
            if discover_links:
                self.logger.info("=" * 50)
                self.logger.info("阶段1：开始链接发现")
                self.logger.info("=" * 50)
                
                discovery_start = time.time()
                all_links = self.discover_links()
                discovery_time = time.time() - discovery_start
                
                self.logger.info(f"链接发现完成，耗时: {discovery_time:.2f}秒")
                self.logger.info(f"发现链接总数: {len(all_links)}")
                
                if len(all_links) == 0:
                    self.logger.error("没有发现任何链接，退出")
                    return
            else:
                # 使用已有的URL
                all_links = list(self.url_manager.all_urls)
                self.logger.info(f"跳过链接发现，使用已有 {len(all_links)} 个链接")
            
            # 阶段2：内容抓取（使用requests多线程）
            self.logger.info("=" * 50)
            self.logger.info("阶段2：开始内容抓取")
            self.logger.info("=" * 50)
            
            # 关闭Selenium以释放资源
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.logger.info("Selenium驱动已关闭，释放资源")
            
            # 获取待处理的URL
            pending_urls = self.url_manager.get_pending_urls()
            if max_pages:
                pending_urls = pending_urls[:max_pages]
            
            if not pending_urls:
                self.logger.info("没有待处理的URL")
                return
            
            self.logger.info(f"开始抓取 {len(pending_urls)} 个页面")
            
            # 并行抓取内容
            self.crawl_content_parallel(pending_urls, max_pages, max_workers)
            
        except KeyboardInterrupt:
            self.logger.info("用户中断爬取")
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {str(e)}")
        finally:
            # 清理资源
            self.cleanup()
            
            # 保存最终状态
            self.url_manager.save_urls()
            
            # 打印摘要
            end_time = time.time()
            total_time = end_time - start_time
            
            self.logger.info("=" * 50)
            self.logger.info("爬取完成")
            self.logger.info("=" * 50)
            self.logger.info(f"总耗时: {total_time:.2f}秒")
            
            self.print_summary()
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("Selenium WebDriver已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebDriver失败: {str(e)}")
    
    def run_discovery_only(self):
        """仅运行链接发现"""
        self.logger.info("开始仅链接发现模式")
        
        try:
            all_links = self.discover_links()
            
            self.logger.info("=" * 50)
            self.logger.info("链接发现完成")
            self.logger.info("=" * 50)
            self.logger.info(f"总共发现: {len(all_links)} 个链接")
            
            # 保存链接
            self.url_manager.save_urls()
            
            # 生成报告
            self.url_manager.export_report()
            
            return all_links
            
        finally:
            self.cleanup()
    
    def run_content_only(self, max_pages=None, max_workers=None):
        """仅运行内容抓取（使用已发现的链接）"""
        self.logger.info("开始仅内容抓取模式")
        
        try:
            # 获取待处理的URL
            pending_urls = self.url_manager.get_pending_urls()
            if max_pages:
                pending_urls = pending_urls[:max_pages]
            
            if not pending_urls:
                self.logger.info("没有待处理的URL")
                return
            
            self.logger.info(f"开始抓取 {len(pending_urls)} 个页面")
            
            # 并行抓取内容
            self.crawl_content_parallel(pending_urls, max_pages, max_workers)
            
            # 打印摘要
            self.print_summary()
            
        finally:
            # 保存状态
            self.url_manager.save_urls()


if __name__ == "__main__":
    # 检查Selenium是否可用
    if not SELENIUM_AVAILABLE:
        print("警告：Selenium不可用，将使用普通HTTP请求模式")
        print("建议安装: pip install selenium")
        print("同时需要下载ChromeDriver")
    
    # 创建爬虫实例
    crawler = HybridTencentQianCrawler(headless=True, use_selenium=True)
    
    try:
        # 运行完整爬虫
        crawler.run(
            max_pages=300,      # 目标抓取300个页面
            max_workers=8,      # 8个并发线程
            discover_links=True # 启用链接发现
        )
    except KeyboardInterrupt:
        print("\n用户中断")
    finally:
        crawler.cleanup()