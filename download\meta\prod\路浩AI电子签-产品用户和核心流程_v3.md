# 路浩AI电子签 - 产品用户和核心流程（V3.0）

## 1. 核心用户角色与场景分析

| 角色分类 | 角色名称 | 核心职责与典型场景 |
| :--- | :--- | :--- |
| **个人用户** | 个人签约方 | **职责**: 作为独立的法律主体，完成个人事务的合同签署。<br>**场景**: 签署租房合同、借条、劳动合同、收据、设计师服务协议等。 |
| **企业内部** | 超级管理员 | **职责**: 企业平台的最高权限所有者，负责企业认证、组织架构搭建、核心权限分配、购买服务、配置全局安全策略。<br>**场景**: 初始化公司电子签平台；为新设立的法务部配置角色和权限；处理最紧急的权限问题。 |
| | 系统/IT管理员 | **职责**: 由超管指定，负责日常的员工账号管理、部门调整、角色成员分配、API密钥管理等。<br>**场景**: 为新入职员工创建账号；将离职员工账号禁用并交接其工作；为业务系统集成提供API密钥。 |
| | 业务管理员<br>(如:合同/印章/模板管理员) | **职责**: 负责特定业务领域的管理。合同管理员管理公司所有合同；印章管理员管理印章的创建、授权和用印审批；模板管理员负责标准合同模板的制作与维护。<br>**场景**: 印章管理员审批业务部门的用印申请；模板管理员将法务部审核通过的最新版销售合同制作成模板。 |
| | 部门负责人 | **职责**: 管理本部门及下属部门的业务，通常拥有本部门的数据查看权限和业务审批权限。<br>**场景**: 销售总监查看本部门所有销售合同的签署进度；研发总监审批下属员工的采购申请。 |
| | 普通员工/业务员 | **职责**: 企业内业务的执行者，在被授予的权限内发起合同、使用印章、处理自己的待办事项。<br>**场景**: 销售人员使用公司模板发起一份销售合同；采购人员提交一份采购合同的用印申请。 |
| **外部关系方** | 企业签约方 | **职责**: 代表其所属企业完成合同的签署。<br>**场景**: 供应商公司的经办人，登录平台签署由我方发起的采购合同。 |
| | 审批人 | **职责**: 在特定流程节点上进行审核，决定流程是否继续。<br>**场景**: 合同金额超过10万元时，需要财务总监作为审批人进行审批，通过后方可正式发起。 |
| | 关注方/抄送人 | **职责**: 无需操作，但需要知晓合同进展和结果。<br>**场景**: 一份重要合同在签署时，需要将公司CEO添加为关注方，使其能收到合同完成的通知。 |

## 2. 核心业务流程图 (Mermaid)

### 2.1 企业入驻与初始化配置流程

```mermaid
graph TD
    A[企业代表访问官网/小程序] --> B[使用个人已实名账号注册/登录];
    B --> C[选择“创建企业”];
    C --> D{选择企业认证方式};
    D -- 法人授权(推荐) --> E[填写企业信息, 发送授权链接给法人];
    E --> F[法人扫码人脸识别];
    D -- 对公打款 --> G[填写对公账户, 等待平台打款并回填金额];
    F --> H{认证成功};
    G --> H;
    H --> I[账号成为“超级管理员”];
    I --> J[进入企业管理后台];
    J --> K[**Step 1: 组织架构搭建**<br>创建部门, 批量导入/邀请员工];
    K --> L[**Step 2: 角色与权限分配**<br>为部门/员工分配系统角色或自定义角色];
    L --> M[**Step 3: 印章创建与授权**<br>创建企业公章/合同章, 并授权给相应角色或员工];
    M --> N[**Step 4: 模板制作(可选)**<br>将常用合同制作成标准模板];
    N --> O[初始化完成, 企业可正式使用];
```

### 2.2 AI辅助合同拟定与发起流程

```mermaid
sequenceDiagram
    participant User as 业务员
    participant AI_Assistant as AI助手
    participant Platform as 路浩AI电子签平台
    participant Reviewer as 法务/上级

    User->>Platform: 登录系统, 选择“AI生成合同”
    Platform->>AI_Assistant: 启动合同生成会话
    AI_Assistant-->>User: 你好, 我是合同助手, 请问您想生成什么类型的合同？
    User->>AI_Assistant: 我需要一份软件采购合同
    AI_Assistant-->>User: 好的, 请告诉我采购方和供应方信息、采购标的、金额、交付日期...
    User->>AI_Assistant: (提供关键信息)
    AI_Assistant->>Platform: 根据用户输入和知识库, 生成合同初稿
    Platform-->>User: 展示合同初稿, 并高亮AI建议的风险点
    User->>Platform: 在线编辑修改部分条款
    User->>Platform: 选择“发起审批”
    Platform->>Reviewer: 发送审批通知 (待办中心/企微/钉钉)
    Reviewer->>Platform: 登录并查看合同, 提出修改意见或直接批准
    alt 审批通过
        Platform-->>User: 通知: 合同已审批通过, 可以发起
        User->>Platform: 填写签署方信息, 确认并发起合同
        Platform->>Signers: 向各签署方发送签署通知
    else 审批驳回
        Platform-->>User: 通知: 合同被驳回, 请根据意见修改
    end
```

### 2.3 多方顺序签署与自动签流程

```mermaid
sequenceDiagram
    participant Initiator as 发起方 (企业A员工)
    participant SignerB as 签署方B (个人)
    participant SignerC as 签署方C (企业C经办人)
    participant Platform as 路浩AI电子签平台
    participant AutoSign as 企业A自动签服务

    Initiator->>Platform: 发起合同, 设置签署顺序: B -> C -> A(自动签)
    Platform->>SignerB: 发送签署邀请
    SignerB->>Platform: 查看合同, 完成手写签名和人脸识别
    Platform-->>SignerB: 签署完成
    Platform->>SignerC: 发送签署邀请
    SignerC->>Platform: 查看合同, 选择企业印章完成盖章
    Platform-->>SignerC: 签署完成
    Platform->>AutoSign: 触发自动签署条件 (前序节点全部完成)
    AutoSign->>Platform: 使用预设印章, 自动完成企业A的盖章
    Platform->>Platform: 合同所有方签署完毕, 生成最终版PDF并存证
    Platform-->>Initiator: 通知: 合同已完成
    Platform-->>SignerB: 通知: 合同已完成
    Platform-->>SignerC: 通知: 合同已完成
```

### 2.4 集团主企业代子企业发起合同流程

```mermaid
graph TD
    A[集团合同管理员登录主企业账号] --> B[选择“发起合同”];
    B --> C{选择发起方企业};
    C -- 选择“主企业” --> D[按正常流程为“主企业”发起合同];
    C -- 选择“XX子公司” --> E[主企业代子企业发起];
    E --> F[**数据隔离**: 签署方联系人、可选印章、模板库<br>均切换为“XX子公司”的资产];
    F --> G[配置合同内容与签署流程];
    G --> H[发起合同];
    H --> I[合同法律主体为“XX子公司”];
    I --> J[合同数据归档于“XX子公司”名下];
    J --> K[集团管理员在集团视图下可统一查看和管理];