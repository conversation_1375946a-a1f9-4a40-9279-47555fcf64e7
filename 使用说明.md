# 腾讯千帆文档爬虫使用说明

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Chrome浏览器
- ChromeDriver（自动下载或手动安装）

### 安装依赖
```bash
cd py_qian
pip install -r requirements.txt
```

### 基本使用
```bash
# 推荐配置：抓取300页，8线程并发
python main_hybrid.py -p 300 -w 8
```

## 📋 完整命令参考

### 基本选项
```bash
# 指定抓取页面数
python main_hybrid.py -p 300

# 指定并发线程数
python main_hybrid.py -w 8

# 禁用Selenium（不推荐）
python main_hybrid.py --no-selenium

# 显示浏览器窗口（调试用）
python main_hybrid.py --show-browser
```

### 功能选项
```bash
# 仅发现链接，不抓取内容
python main_hybrid.py --discover-only

# 仅抓取内容（使用已发现的链接）
python main_hybrid.py --content-only

# 跳过链接发现阶段
python main_hybrid.py --no-discover
```

### 状态管理
```bash
# 查看当前状态
python main_hybrid.py --status

# 生成详细报告
python main_hybrid.py --report

# 重置进度
python main_hybrid.py --reset
```

## 🎯 推荐使用场景

### 场景1：首次完整抓取
```bash
# 发现所有链接并抓取300页
python main_hybrid.py -p 300 -w 8
```

### 场景2：仅发现链接
```bash
# 先发现所有可用链接
python main_hybrid.py --discover-only
```

### 场景3：继续未完成的抓取
```bash
# 继续抓取剩余页面
python main_hybrid.py --content-only -p 200 -w 8
```

### 场景4：调试模式
```bash
# 显示浏览器窗口，抓取少量页面
python main_hybrid.py -p 10 --show-browser
```

## 📊 性能配置建议

### 高性能配置（推荐）
```bash
python main_hybrid.py -p 300 -w 8
```
- 适用于：性能较好的机器
- 预期时间：30-60分钟
- 内存使用：约500MB

### 中等性能配置
```bash
python main_hybrid.py -p 200 -w 5
```
- 适用于：普通配置机器
- 预期时间：45-90分钟
- 内存使用：约300MB

### 低性能配置
```bash
python main_hybrid.py -p 100 -w 3
```
- 适用于：配置较低的机器
- 预期时间：60-120分钟
- 内存使用：约200MB

## 📁 输出文件说明

### 主要输出
- `download/*.md` - 抓取的Markdown文档
- `download/hybridtencentqiancrawler.log` - 运行日志
- `download/crawl_report.md` - 详细报告

### 状态文件
- `download/all_urls.txt` - 所有发现的URL
- `download/completed_urls.txt` - 已完成的URL
- `download/failed_urls.txt` - 失败的URL
- `download/url_metadata.json` - URL元数据

## 🔧 故障排除

### 常见问题

#### 1. Selenium相关错误
```
错误：Selenium初始化失败
解决：
1. 确保Chrome浏览器已安装
2. 下载对应版本的ChromeDriver
3. 将ChromeDriver添加到PATH环境变量
```

#### 2. 依赖包缺失
```
错误：ImportError: No module named 'xxx'
解决：
pip install -r requirements.txt
```

#### 3. 网络连接问题
```
错误：请求超时或连接失败
解决：
1. 检查网络连接
2. 尝试使用代理
3. 增加重试次数
```

#### 4. 内存不足
```
错误：MemoryError
解决：
1. 减少并发线程数：-w 3
2. 减少抓取页面数：-p 100
3. 关闭其他程序释放内存
```

### 调试技巧

#### 查看详细日志
```bash
# 查看实时日志
tail -f download/hybridtencentqiancrawler.log
```

#### 检查进度
```bash
# 查看当前状态
python main_hybrid.py --status
```

#### 生成报告
```bash
# 生成详细报告
python main_hybrid.py --report
```

## 🎨 高级用法

### 自定义配置
修改 `config.py` 文件中的配置：
```python
# 修改请求间隔
REQUEST_CONFIG['delay_min'] = 2
REQUEST_CONFIG['delay_max'] = 5

# 修改并发数
PROGRESS_CONFIG['max_concurrent'] = 10
```

### 分阶段执行
```bash
# 第一阶段：发现链接
python main_hybrid.py --discover-only

# 第二阶段：抓取内容
python main_hybrid.py --content-only -p 300 -w 8
```

## ⚠️ 注意事项

### 使用建议
1. **首次使用**：建议先用小数量测试（-p 10）
2. **网络稳定**：确保网络连接稳定
3. **资源监控**：注意CPU和内存使用情况
4. **合理间隔**：避免对目标服务器造成过大压力

### 法律合规
- 仅用于学习研究目的
- 遵守网站robots.txt协议
- 不用于商业用途
- 合理控制请求频率

## 🆕 新功能特点

### 智能菜单展开
- 自动识别Docusaurus框架的折叠菜单
- 多轮展开策略，最多15轮
- 支持多种点击方式和选择器

### 综合链接发现
- Selenium完整展开（主要策略）
- API接口探测
- 站点地图解析
- 样本页面递归
- 模式匹配生成

### 混合策略处理
- 阶段1：Selenium链接发现（专注完整性）
- 阶段2：requests内容抓取（专注速度）
- 资源优化：及时释放Selenium资源

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件：`download/hybridtencentqiancrawler.log`
2. 生成详细报告：`python main_hybrid.py --report`
3. 检查网络连接和依赖环境
4. 参考故障排除部分的解决方案

---

**版本**: 混合策略版本 v2.0  
**更新时间**: 2025年6月15日  
**支持页面数**: 300+页面深度抓取