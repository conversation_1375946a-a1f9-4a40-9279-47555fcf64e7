# 合同签署

> 导航路径: 企业版操作指南 > 合同相关 > 合同签署
> 来源: https://qian.tencent.com/document/92775/
> 抓取时间: 2025-06-15 15:54:20

---

## 网页端签署

### 操作入口

方式1：进入 [待办中心 > 待我处理 > 合同签署](https://qian.tencent.com/console/to-do-center) 的待办事务。

﻿

﻿

﻿  

方式2：[合同 > 我的待办](https://qian.tencent.com/console/contract-mgr)，单击列表上的**签署** 。

﻿

﻿

﻿  

### 填写合同

若存在需要本人填写的内容，则可以在合同文档上或右侧表单区域填写，提交后进入下一步签署合同。

﻿

﻿

﻿  

### 签署合同

1. 单击签署区，选择该区域所需要加盖的印章或签名。

﻿

﻿

﻿  

骑缝章签署区样式：

﻿

﻿

﻿  

如果页面为下图所示，无法签署，则是因为发起方设置了**签署审批** ，需审批通过后方可签署。

﻿

﻿

﻿  

如果有**填写** 区，为避免对其他签署方造成卡点，在审批通过前可以先补充填写区。

签署审批通过后才会**发送短信** 给签署方提醒签署。

此状态中，可以进行**下载签署码** 、**下载合同** 等更多操作（**拒签** 、**转他人处理** ）。

审批流程为发起方设置，需前往 [待办中心](https://qian.tencent.com/console/to-do-center) 进行处理。

2. 选择印章。

﻿

﻿

﻿  

若用户没有企业印章，可以**提交用印申请** ，等待管理员在 [待办中心](1323/89652) 审批通过后即可使用印章签署本次合同。

﻿

﻿

﻿  

选择印章并填写原因，上传需要的材料后提交申请。

﻿

﻿

﻿  

3. 若签署区域中需要个人用户进行签名，可以选择所需要签署的签名样式进行签名。

﻿

﻿

﻿  

选择个人签名/印章。

﻿

﻿

﻿  

可以选择的每种个人签名/印章类型的介绍如下：

个人签名/印章类型| 介绍| 签名示例  
---|---|---  
手写签名| 用户可以自由书写签名，更符合用户真实笔记| ﻿﻿﻿  
  
正楷临摹签名| 需要用户一笔一划书写自己姓名，并通过 AI 图像识别技术，保证用户签名准确无误| ﻿﻿﻿  
  
系统签名| 无需用户签名，自动根据用户姓名生成系统签名| ﻿﻿﻿  
  
姓名印章| 无需用户上传，自动根据用户姓名自动生成姓名印章| ﻿﻿﻿  
  
自定义图片印章| 用户可以自行上传姓名印章或执业章（例如会计注册师执业章等）的图片| ﻿﻿﻿  

4. 单击右上角的**确认签署** 。

﻿

﻿

﻿  

若发起方设置了多种签署认证方式，则签署方需要选择其中一种进行认证。

﻿

﻿

﻿  

人脸识别认证：通过微信扫码进行人脸识别。

﻿

﻿

﻿  

签署密码认证：输入6位数密码进行签署，如果是首次设置密码，则需要您输入短信验证码进行身份确认。

﻿

﻿

﻿  

手机号码认证：获取短信验证码进行签署。

﻿

﻿

﻿  

若在腾讯电子签小程序端签署且合同签署认证方式设置了使用面容 ID、指纹识别，则在小程序签署时可以选择面容 ID、指纹识别用于签署。

**注意：**

面容 ID、指纹识别签署需要您的手机设备支持面容 ID、指纹识别能力。一般情况，iPhone 手机支持面容 ID，Android 手机支持指纹识别。

﻿

﻿

﻿  

5. 签署合同成功。

﻿

﻿

﻿  

**注意：**

在完成签署前，可以**拒签合同** ，一方拒签后，此合同签署流程将终止。

## 移动端签署

签署人收到签署短信后，单击短信内的签署链接，按下述步骤操作即可完成签署（也可在微信聊天框中单击对方分享的签署提示）。

﻿

﻿

﻿  

**骑缝章** 签署区样式如下，其余流程同上：

﻿

﻿

﻿  

如果页面为下图所示，无法签署，则是因为发起方设置了**签署审批** ，需审批通过后方可签署。

﻿

﻿

﻿  

如果有**填写** 区，为避免对其他签署方造成卡点，在审批通过前可以先补充填写区。

签署审批通过后才会**发送短信** 给签署方提醒签署。

此状态中，可以在更多操作中**下载合同** 、**拒签** 、**转他人处理** 等。

审批流程为发起方设置，需在首页 - **待办中心** 中进行处理。

﻿

﻿

﻿  

若用户没有企业印章权限，可以根据以下步骤申请用印，管理员可以在 [待办中心](1323/89652) 审批通过后即可用印章签署本次合同。

﻿

﻿

﻿  

若需要用户签署个人签名/印章，可以选择所需要的签名类型进行签署合同。

﻿

﻿

﻿  

可以选择的每种个人签名/印章类型的介绍如下：

个人签名/印章类型| 介绍| 签名示例  
---|---|---  
手写签名| 用户可以自由书写签名，更符合用户真实笔记| ﻿﻿﻿  
  
正楷临摹签名| 需要用户一笔一划书写自己姓名，并通过 AI 图像识别技术，保证用户签名准确无误| ﻿﻿﻿  
  
系统签名| 无需用户签名，自动根据用户姓名生成系统签名| ﻿﻿﻿  
  
姓名印章| 无需用户上传，自动根据用户姓名自动生成姓名印章| ﻿﻿﻿  
  
自定义图片印章| 用户可以自行上传姓名印章或执业章（例如会计注册师执业章等）的图片| ﻿﻿﻿  

其中，用户可以选择合同签署认证的方式，这个方式在合同发起时进行指定。目前支持的认证方式有以下几种：

手机号认证：仅限中国大陆居民使用，通过接收短信验证码，并查验手机号、姓名、身份证号一致性，完成签署认证。

﻿

﻿

﻿  

**说明：**

如果签署方已经在腾讯电子签小程序实名登录，且合同发起时预留的手机号与当前登录的手机号相同，则签署方仅需正确回填短信验证码，即可完成签署。

签署密码：通过签署方本人设置的签署密码，快速完成签署认证。

﻿

﻿

﻿  

人脸识别：通过对比当前操作人与权威库中的人脸信息，完成签署认证。

﻿

﻿

﻿  

面容 ID：通过对比当前操作人与设备中录入的面容 ID 信息，完成签署认证。

﻿

﻿

指纹识别：通过对比当前操作人与设备中录入的指纹信息，完成签署认证。

﻿

﻿