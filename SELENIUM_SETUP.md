# Selenium环境配置指南

## 概述

本项目提供了基于Selenium的增强版爬虫，能够：
- 自动展开JavaScript折叠菜单
- 渲染动态加载的内容
- 获取更多隐藏的文档链接

## 安装步骤

### 1. 安装Python依赖

```bash
pip install selenium>=4.0.0
```

或者安装所有依赖：

```bash
pip install -r requirements.txt
```

### 2. 安装Chrome浏览器

确保系统已安装Chrome浏览器。如果没有，请从以下地址下载：
- https://www.google.com/chrome/

### 3. 下载ChromeDriver

#### 方法一：自动下载（推荐）

使用webdriver-manager自动管理ChromeDriver：

```bash
pip install webdriver-manager
```

然后修改代码使用自动管理的ChromeDriver。

#### 方法二：手动下载

1. 查看Chrome版本：
   - 打开Chrome浏览器
   - 地址栏输入：`chrome://version/`
   - 记录版本号

2. 下载对应版本的ChromeDriver：
   - 访问：https://chromedriver.chromium.org/downloads
   - 下载与Chrome版本匹配的ChromeDriver

3. 配置ChromeDriver：
   - **Windows**: 将`chromedriver.exe`放到项目目录或PATH环境变量中
   - **Linux/Mac**: 将`chromedriver`放到`/usr/local/bin/`或PATH中

### 4. 验证安装

运行以下命令验证Selenium是否正常工作：

```bash
python -c "from selenium import webdriver; driver = webdriver.Chrome(); driver.get('https://www.baidu.com'); print('Selenium工作正常'); driver.quit()"
```

## 使用方法

### 基本用法

```bash
# 使用Selenium发现所有链接（自动展开菜单）
python selenium_main.py --discover-only

# 运行完整的Selenium爬虫
python selenium_main.py -p 50

# 显示浏览器窗口（调试用）
python selenium_main.py --show-browser --discover-only
```

### 高级选项

```bash
# 不使用Selenium，回退到普通模式
python selenium_main.py --no-selenium

# 查看当前状态
python selenium_main.py --status

# 重置进度
python selenium_main.py --reset

# 生成详细报告
python selenium_main.py --report
```

## 性能对比

| 模式 | 发现链接数 | 速度 | 资源占用 | 稳定性 |
|------|------------|------|----------|--------|
| 普通HTTP | ~20个 | 快 | 低 | 高 |
| Selenium | 50+个 | 慢 | 高 | 中等 |

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   ```
   错误：This version of ChromeDriver only supports Chrome version XX
   解决：下载匹配的ChromeDriver版本
   ```

2. **ChromeDriver不在PATH中**
   ```
   错误：'chromedriver' executable needs to be in PATH
   解决：将chromedriver.exe添加到PATH环境变量
   ```

3. **Chrome浏览器未安装**
   ```
   错误：unknown error: cannot find Chrome binary
   解决：安装Chrome浏览器
   ```

4. **内存不足**
   ```
   错误：Chrome crashed
   解决：增加系统内存或使用--no-selenium参数
   ```

### 调试技巧

1. **显示浏览器窗口**：
   ```bash
   python selenium_main.py --show-browser --discover-only
   ```

2. **查看详细日志**：
   检查`download/selenium_crawler.log`文件

3. **降级到普通模式**：
   ```bash
   python selenium_main.py --no-selenium
   ```

## 配置优化

### 无头模式（默认）
- 不显示浏览器窗口
- 资源占用较低
- 适合生产环境

### 有头模式（调试）
- 显示浏览器窗口
- 可以观察爬取过程
- 适合开发调试

### 性能调优

1. **减少资源占用**：
   - 禁用图片加载
   - 禁用CSS
   - 使用无头模式

2. **提高稳定性**：
   - 增加等待时间
   - 添加重试机制
   - 处理异常情况

## 注意事项

1. **合规使用**：
   - 遵守网站robots.txt
   - 控制访问频率
   - 尊重服务器资源

2. **资源管理**：
   - Selenium会占用较多内存
   - 建议单线程运行
   - 及时关闭浏览器进程

3. **版本兼容**：
   - Chrome和ChromeDriver版本要匹配
   - 定期更新依赖包
   - 测试新版本兼容性

## 技术原理

### 自动展开菜单的实现

1. **查找折叠项**：
   ```css
   .menu__list-item--collapsed .menu__link--sublist-caret
   ```

2. **点击展开**：
   ```python
   element.click()
   ```

3. **等待加载**：
   ```python
   time.sleep(0.5)
   ```

4. **递归处理**：
   重复查找新出现的折叠项

### JavaScript渲染

- 等待页面完全加载
- 执行JavaScript代码
- 获取渲染后的HTML
- 提取动态生成的链接

这种方式能够获取到普通HTTP请求无法获取的动态内容。 