"""
URL管理器 - 负责URL的收集、存储和跟踪
"""

import os
import json
import time
from urllib.parse import urljoin, urlparse
from utils import is_valid_url, normalize_url


class URLManager:
    """URL管理器类"""
    
    def __init__(self, output_dir, base_url):
        self.output_dir = output_dir
        self.base_url = base_url
        
        # URL存储文件
        self.all_urls_file = os.path.join(output_dir, 'all_urls.txt')
        self.completed_urls_file = os.path.join(output_dir, 'completed_urls.txt')
        self.failed_urls_file = os.path.join(output_dir, 'failed_urls.txt')
        self.url_metadata_file = os.path.join(output_dir, 'url_metadata.json')
        
        # URL集合
        self.all_urls = set()
        self.completed_urls = set()
        self.failed_urls = set()
        self.url_metadata = {}  # 存储URL的元数据（发现时间、状态等）
        
        # 加载已有数据
        self.load_urls()
        
    def load_urls(self):
        """加载已保存的URL数据"""
        # 加载所有URL
        if os.path.exists(self.all_urls_file):
            try:
                with open(self.all_urls_file, 'r', encoding='utf-8') as f:
                    self.all_urls = set(line.strip() for line in f if line.strip())
            except Exception as e:
                print(f"加载所有URL失败: {e}")
        
        # 加载已完成URL
        if os.path.exists(self.completed_urls_file):
            try:
                with open(self.completed_urls_file, 'r', encoding='utf-8') as f:
                    self.completed_urls = set(line.strip() for line in f if line.strip())
            except Exception as e:
                print(f"加载已完成URL失败: {e}")
        
        # 加载失败URL
        if os.path.exists(self.failed_urls_file):
            try:
                with open(self.failed_urls_file, 'r', encoding='utf-8') as f:
                    self.failed_urls = set(line.strip() for line in f if line.strip())
            except Exception as e:
                print(f"加载失败URL失败: {e}")
        
        # 加载URL元数据
        if os.path.exists(self.url_metadata_file):
            try:
                with open(self.url_metadata_file, 'r', encoding='utf-8') as f:
                    self.url_metadata = json.load(f)
            except Exception as e:
                print(f"加载URL元数据失败: {e}")
                self.url_metadata = {}
    
    def save_urls(self):
        """保存URL数据到文件"""
        try:
            # 保存所有URL
            with open(self.all_urls_file, 'w', encoding='utf-8') as f:
                for url in sorted(self.all_urls):
                    f.write(f"{url}\n")
            
            # 保存已完成URL
            with open(self.completed_urls_file, 'w', encoding='utf-8') as f:
                for url in sorted(self.completed_urls):
                    f.write(f"{url}\n")
            
            # 保存失败URL
            with open(self.failed_urls_file, 'w', encoding='utf-8') as f:
                for url in sorted(self.failed_urls):
                    f.write(f"{url}\n")
            
            # 保存URL元数据
            with open(self.url_metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.url_metadata, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存URL数据失败: {e}")
    
    def add_url(self, url, source_url=None):
        """
        添加URL到管理器
        
        Args:
            url (str): 要添加的URL
            source_url (str): 发现此URL的源页面URL
            
        Returns:
            bool: 是否成功添加（新URL）
        """
        # 标准化URL
        normalized_url = normalize_url(url, self.base_url)
        
        if not normalized_url or not is_valid_url(normalized_url, self.base_url):
            return False
        
        # 检查是否已存在
        if normalized_url in self.all_urls:
            return False
        
        # 添加到集合
        self.all_urls.add(normalized_url)
        
        # 添加元数据
        self.url_metadata[normalized_url] = {
            'discovered_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'source_url': source_url,
            'status': 'pending'
        }
        
        return True
    
    def add_urls_batch(self, urls, source_url=None):
        """
        批量添加URL
        
        Args:
            urls (list): URL列表
            source_url (str): 发现这些URL的源页面URL
            
        Returns:
            int: 成功添加的URL数量
        """
        added_count = 0
        for url in urls:
            if self.add_url(url, source_url):
                added_count += 1
        return added_count
    
    def mark_completed(self, url):
        """标记URL为已完成"""
        if url in self.all_urls:
            self.completed_urls.add(url)
            if url in self.failed_urls:
                self.failed_urls.remove(url)
            
            # 更新元数据
            if url in self.url_metadata:
                self.url_metadata[url]['status'] = 'completed'
                self.url_metadata[url]['completed_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
    
    def mark_failed(self, url, error_msg=None):
        """标记URL为失败"""
        if url in self.all_urls:
            self.failed_urls.add(url)
            
            # 更新元数据
            if url in self.url_metadata:
                self.url_metadata[url]['status'] = 'failed'
                self.url_metadata[url]['failed_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
                if error_msg:
                    self.url_metadata[url]['error'] = error_msg
    
    def mark_pending(self, url):
        """将URL重新标记为待处理状态"""
        if url in self.all_urls:
            # 从失败和完成列表中移除
            if url in self.failed_urls:
                self.failed_urls.remove(url)
            if url in self.completed_urls:
                self.completed_urls.remove(url)
            
            # 更新元数据
            if url in self.url_metadata:
                self.url_metadata[url]['status'] = 'pending'
                self.url_metadata[url]['retry_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
    
    def get_pending_urls(self):
        """获取待处理的URL列表"""
        return list(self.all_urls - self.completed_urls - self.failed_urls)
    
    def get_next_url(self):
        """获取下一个待处理的URL"""
        pending_urls = self.get_pending_urls()
        return pending_urls[0] if pending_urls else None
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'total_urls': len(self.all_urls),
            'completed_urls': len(self.completed_urls),
            'failed_urls': len(self.failed_urls),
            'pending_urls': len(self.get_pending_urls()),
            'completion_rate': len(self.completed_urls) / len(self.all_urls) * 100 if self.all_urls else 0
        }
    
    def is_all_completed(self):
        """检查是否所有URL都已处理完成"""
        return len(self.get_pending_urls()) == 0
    
    def reset(self):
        """重置所有数据"""
        self.all_urls.clear()
        self.completed_urls.clear()
        self.failed_urls.clear()
        self.url_metadata.clear()
        
        # 删除文件
        for file_path in [self.all_urls_file, self.completed_urls_file, 
                         self.failed_urls_file, self.url_metadata_file]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")
    
    def export_report(self):
        """导出详细报告"""
        stats = self.get_statistics()
        
        report = f"""
# URL抓取报告

生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

## 统计信息
- 总URL数量: {stats['total_urls']}
- 已完成数量: {stats['completed_urls']}
- 失败数量: {stats['failed_urls']}
- 待处理数量: {stats['pending_urls']}
- 完成率: {stats['completion_rate']:.2f}%

## 状态详情
"""
        
        if self.failed_urls:
            report += "\n### 失败的URL:\n"
            for url in sorted(self.failed_urls):
                metadata = self.url_metadata.get(url, {})
                error = metadata.get('error', '未知错误')
                report += f"- {url} (错误: {error})\n"
        
        if self.get_pending_urls():
            report += "\n### 待处理的URL:\n"
            for url in sorted(self.get_pending_urls()):
                report += f"- {url}\n"
        
        # 保存报告
        report_file = os.path.join(self.output_dir, 'crawl_report.md')
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {report_file}")
        except Exception as e:
            print(f"保存报告失败: {e}")
        
        return report 