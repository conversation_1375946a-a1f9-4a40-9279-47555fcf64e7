# 路浩AI电子签 - 技术架构与实现（V4.2）

本文档旨在为"路浩AI电子签"V4.2版本提供一份全面、清晰的技术实现蓝图。内容涵盖整体架构、技术选型、数据库方案、核心安全设计、AI能力集成策略以及部署运维等，旨在指导技术团队进行高效、规范的开发工作。

## 1. 整体架构与技术选型

平台采用领先的云原生微服务架构，以领域驱动设计（DDD）为指导思想，确保系统的高内聚、低耦合、高可用和可扩展性。

### 1.1 前后端技术栈

| 领域 | 技术选型 | 核心框架/库 | 选型理由 |
| :--- | :--- | :--- | :--- |
| **后端微服务** | **Java (主力) & Golang (辅助)** | **Java**: Spring Boot 3.x, Spring Cloud Alibaba<br>**Go**: Go-Kratos | **Java**: 生态成熟，拥有强大的事务管理和复杂的业务逻辑处理能力，适合构建核心业务域服务（如合同、组织、计费）。<br>**Go**: 高并发性能卓越，编译速度快，适合构建中间件、网关、AI服务等对性能要求高的基础设施。 |
| **前端应用** | **React** | Next.js, **Tailwind CSS, ShadCN/UI** | **React**: 业界主流，组件化开发模式成熟。<br>**Tailwind CSS**: 原子化CSS框架，提供极高的开发效率和定制自由度。<br>**ShadCN/UI**: 提供一套设计精良、可访问性高的无头组件，与Tailwind完美结合，能快速构建美观、统一的UI界面。 |

### 1.2 数据库与存储方案

为应对不同类型的数据存储需求，我们采用混合数据库架构。

| 方案 | 数据库/服务 | 存储内容 | 选型理由 |
| :--- | :--- | :--- | :--- |
| **关系型数据库** | **PostgreSQL 15+** | 核心业务数据：用户信息、企业信息、组织架构、合同元数据、订单、印章信息等。 | **PG**相比MySQL，在GIS、复杂查询、自定义数据类型和扩展性方面更具优势。其先进的MVCC实现和JSONB字段对非结构化数据的支持，能更好地满足未来业务扩展需求。 |
| **文档数据库** | **MongoDB 7.x** | 非结构化/半结构化数据：合同版本历史、操作日志、用户行为数据、Webhook推送记录等。 | Schema-less特性使其非常适合存储结构多变、写入频繁的日志类和文档类数据，查询性能高。 |
| **搜索引擎** | **Elasticsearch 8.x** | 合同正文、附件内容（需OCR提取）、各类可供检索的元数据。 | 提供强大的全文检索和聚合分析能力，是实现合同内容搜索、自然语言搜索的关键。 |
| **对象存储** | **MinIO (私有化) / Aliyun OSS (公有云)** | 最终版合同PDF、附件、上传的印章图片、人脸识别影像等所有非结构化文件。 | 提供高可用、高可靠的海量文件存储能力，支持S3标准协议，易于扩展和维护。 |
| **键值/缓存数据库** | **Redis 7.x** | Session会话、热点数据缓存（如权限、配置）、分布式锁、消息队列的简单替代等。 | 内存读写性能极高，能有效降低数据库压力，提升系统响应速度。 |

### 1.3 中间件与可观测性

| 类型 | 技术选型 | 用途 |
| :--- | :--- | :--- |
| **消息队列** | **RabbitMQ** | 服务间的异步通信、任务解耦、流量削峰。例如，合同签署完成后的多渠道通知、异步生成报表等。 | 成熟稳定，支持多种消息模式（如Fanout, Direct, Topic），并有完善的延迟队列和死信队列机制，足以满足当前业务需求。 |
| **监控告警** | **Prometheus + Grafana** | 采集和存储所有微服务的性能指标（Metrics），通过Grafana进行可视化展示和配置告警规则。 | 云原生领域的事实标准，与K8s生态无缝集成，便于对服务进行全方位的性能监控。 |
| **日志系统** | **ELK Stack (Elasticsearch, Logstash, Kibana)** | 集中收集、存储和分析所有微服务的应用日志。Kibana提供强大的日志查询和可视化界面。 | 成熟的日志解决方案，便于开发者进行问题排查和系统审计。 |

### 1.4 分布式存储设计

针对平台中海量的非结构化文件（合同、附件、印章图片等），我们设计了统一的分布式存储方案，其核心目标是高可用、高持久、安全可控。

*   **统一上传流程**:
    1.  前端向业务服务（如`contract-service`）请求上传凭证。
    2.  业务服务生成一个预签名的URL（Pre-signed URL），该URL具有时效性（如5分钟）和特定的上传权限，并包含文件最终在对象存储中的路径（Object Key）。路径命名规范：`{tenant_id}/{business_type}/{date}/{uuid}.{ext}`。
    3.  前端使用该URL，通过HTTP PUT请求直接将文件流上传至对象存储（MinIO/S3），绕过业务服务器，避免不必要的带宽和内存消耗。
    4.  上传成功后，前端将Object Key和文件元数据（文件名、大小、Hash等）提交给业务服务。
    5.  业务服务将文件元数据与其业务数据（如合同ID）在数据库中进行关联。

*   **访问控制与安全**:
    *   所有存储桶（Bucket）均设置为 **私有读写**。
    *   外部用户或前端应用绝不直接通过永久密钥访问，所有访问（上传/下载）均通过上述有时效性的预签名URL进行，实现最小权限和租户隔离。
    *   启用服务端加密（SSE-S3），由对象存储服务自动对写入的文件进行加密，进一步增强数据安全性。

*   **高可用与持久性**:
    *   **私有化部署 (MinIO)**: 采用纠删码（Erasure Coding）模式部署，例如`EC:4`表示数据被分成4个数据块和4个校验块，存储在8台不同的服务器上。这种模式允许最多4台服务器宕机而不丢失数据，极大地提高了存储的利用率和可靠性。
    *   **公有云部署 (OSS)**: 直接利用云厂商提供的多副本、跨可用区（AZ）存储能力，数据持久性可达99.9999999999%（12个9），免去自行维护的复杂性。

## 2. 安全加密与唯一性设计

安全是电子签平台的生命线。我们从数据、传输、存储、合规等多个维度构建纵深防御体系。

### 2.1 合同唯一性与防篡改

*   **合同唯一ID**: 每份合同（每条签署流程）在创建时，系统会生成一个全局唯一的、趋势递增的ID（如使用雪花算法`Snowflake`）。此ID将贯穿合同的整个生命周期。
*   **文件摘要算法**: 所有上传的文件在进入签署流程前，都会使用国密 **`SM3`** 算法（或 **`SHA-256`** 作为备选）计算其内容的哈希摘要。此摘要将作为文件的唯一"数字指纹"。
*   **数字签名**: 每一方签署时，平台会调用CA（证书颁发机构）服务，使用签署人的个人/企业数字证书，对 **当前文件版本的内容摘要** 和 **关键签署信息**（如签署人身份、时间、IP）进行数字签名。
*   **时间戳**: 每次签名操作都会加盖一个由权威TSA（时间戳服务机构）颁发的、具有法律效力的可信时间戳，精确记录签署发生的法定时间。
*   **防篡改机制**: 最终生成的合同PDF，会将所有签署方的数字签名、时间戳、证据链信息嵌入其中。任何对PDF内容的微小改动都会导致至少一个数字签名失效，通过合同验签功能即可立即识别。

### 2.2 数据加密方案

*   **传输加密**: 客户端与服务器之间的所有通信，均强制使用 **HTTPS (TLS 1.3)** 协议进行加密，防止数据在传输过程中被窃听或篡改。
*   **存储加密 (静态数据加密)**:
    *   **文件加密**: 所有存储在对象存储（MinIO/OSS）中的合同、附件等敏感文件，均使用 **AES-256** 对称加密算法进行加密存储。每个文件使用独立的密钥（DEK）。
    *   **密钥管理**: 文件加密密钥（DEK）本身，则使用 **RSA-2048** 非对称加密或通过KMS（密钥管理服务）的主密钥进行加密保护。这确保了即使存储介质泄露，文件内容也无法被解密。
    *   **数据库加密**: 核心敏感字段（如用户身份证号、手机号）在数据库中采用加密或脱敏方式存储。

## 3. AI模块技术实现策略

AI是本平台的核心差异化能力。我们将自研与第三方服务相结合，构建强大的AI赋能域。

### 3.1 核心AI能力栈

*   **大语言模型 (LLM)**: 支持灵活接入和切换多种业界领先的大模型，如 **Deepseek-V2**, **Qwen-Max**, **ChatGLM-4**, **豆包Pro** 等。通过统一的接口层进行封装，方便进行模型评估和路由。
*   **向量数据库**: 使用 **Milvus** 或 **PostgreSQL (pgvector)** 来存储合同条款、法律法规、企业知识库等文本的向量化表示，是实现RAG（检索增强生成）的核心。
*   **AI计算框架**: 使用 **Python (FastAPI/PyTorch)** 来构建AI服务，提供模型推理接口。

### 3.2 AI功能实现方案

*   **合同智能生成与审查 (RAG)**:
    1.  **知识库构建**: 将高质量的合同范本、法律法规、企业自己的标准合同等进行切片、向量化，存入向量数据库，构建成专业知识库。
    2.  **用户意图理解**: 当用户通过对话或上传合同进行交互时，首先用LLM理解其核心需求。
    3.  **检索增强**: 将用户需求或待审查的合同文本转换为向量，在知识库中检索最相关的条款或风险点作为上下文（Context）。
    4.  **增强生成**: 将原始请求和检索到的上下文一起打包成一个结构化的Prompt，发送给LLM，生成更精准、更专业的合同文本或审查报告。

*   **印章OCR识别与智能抠图**:
    1.  **模型选型**: 使用 **U-Net** 或类似的图像分割模型，结合传统的计算机视觉技术（如霍夫圆变换、边缘检测）进行印章识别和定位。
    2.  **数据增强**: 在训练模型时，使用大量不同光照、角度、模糊程度的印章图片进行数据增强，提升模型鲁棒性。
    3.  **后处理**: 对模型输出的掩码（Mask）进行精细化处理，去除毛刺和背景噪声，生成高保真的、带透明通道的PNG图片。

## 4. 部署与运维架构

*   **容器化与编排**: 所有微服务都将被打包成 **Docker** 镜像。使用 **Kubernetes (K8s)** 作为容器编排平台，实现服务的自动化部署、弹性伸缩和故障自愈。
*   **CI/CD**: 采用 **GitLab CI/CD** 或 **Jenkins** 构建自动化的持续集成与持续部署流水线。代码提交后，自动触发编译、测试、镜像构建和部署流程。
*   **服务治理**: 借助于 **Istio** 服务网格（或Spring Cloud/Kratos自带能力），实现服务发现、负载均衡、熔断、限流、灰度发布等高级治理能力。

## 5. 核心数据库表结构设计

为确保模型清晰和数据一致性，以下为核心业务微服务所依赖的PostgreSQL数据库表结构设计（仅列出核心字段）。

### 5.1 账户服务 (account-service)

*   `users` (用户信息表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 用户唯一ID |
| `mobile` | `varchar(20)` | `Unique` | 手机号（加密存储） |
| `email` | `varchar(100)` | | 邮箱（加密存储） |
| `user_status` | `smallint` | | 用户状态 (1:未实名, 2:已实名, 3:已注销) |
| `kyc_info` | `jsonb` | | 实名信息 (姓名, 身份证号, 国籍等)（加密） |
| `created_at` | `timestamp` | | 创建时间 |

*   `enterprises` (企业信息表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 企业唯一ID |
| `enterprise_name` | `varchar(100)` | | 企业名称 |
| `credit_code` | `varchar(50)` | `Unique` | 统一社会信用代码 |
| `auth_status` | `smallint` | | 认证状态 (1:未认证, 2:认证中, 3:已认证) |
| `legal_person_info`| `jsonb` | | 法人信息 |
| `super_admin_id`| `bigint` | `FK(users.id)` | 超级管理员的用户ID |

### 5.2 组织权限服务 (org-permission-service)

*   `org_nodes` (组织架构节点表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 节点ID |
| `enterprise_id` | `bigint` | `FK(enterprises.id)`| 所属企业ID |
| `parent_id` | `bigint` | | 父节点ID (根节点为0) |
| `node_name` | `varchar(50)` | | 部门名称 |
| `node_type` | `smallint` | | 节点类型 (1:部门, 2:员工) |
| `user_id` | `bigint` | `FK(users.id)` | 如果是员工节点，对应的用户ID |

*   `roles` (角色表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 角色ID |
| `enterprise_id` | `bigint` | `FK(enterprises.id)`| 所属企业ID (0代表系统预设角色) |
| `role_name` | `varchar(50)` | | 角色名称 |
| `permissions` | `jsonb` | | 权限码集合, e.g., `["contract:create", "seal:use"]`|

*   `user_roles` (用户角色关联表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `user_id` | `bigint` | `PK, FK` | 用户ID |
| `role_id` | `bigint` | `PK, FK` | 角色ID |
| `enterprise_id` | `bigint` | `PK, FK` | 企业ID |

### 5.3 合同与印章服务 (contract & seal service)

*   `contracts` (合同主表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 合同ID (业务ID) |
| `title` | `varchar(255)` | | 合同标题 |
| `enterprise_id` | `bigint` | `FK` | 发起方企业ID |
| `initiator_id` | `bigint` | `FK` | 发起人用户ID |
| `flow_status` | `smallint` | | 流程状态 (1:草稿, 2:签署中, 3:已完成, 4:已撤销, 5:已作废) |
| `created_at` | `timestamp` | | 创建时间 |

*   `contract_files` (合同文件表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 文件ID |
| `contract_id` | `bigint` | `FK` | 合同ID |
| `file_name` | `varchar(255)` | | 原始文件名 |
| `object_key` | `varchar(255)` | | 在对象存储中的路径 |
| `file_hash` | `varchar(100)` | | 文件内容摘要 (SM3/SHA256) |
| `version` | `int` | | 文件版本号（每次签署后递增） |

*   `contract_signers` (合同签署方表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | |
| `contract_id` | `bigint` | `FK` | 合同ID |
| `signer_type` | `smallint` | | 签署方类型 (1:个人, 2:企业) |
| `signer_id` | `bigint` | | 签署主体ID (个人为user_id, 企业为enterprise_id) |
| `actor_user_id` | `bigint` | | 经办人用户ID (企业签署时) |
| `sign_order` | `int` | | 签署顺序 |
| `sign_status` | `smallint` | | 签署状态 (1:待签, 2:已签, 3:已拒) |
| `signed_at` | `timestamp` | | 签署时间 |

*   `seals` (印章表)
| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `bigint` | `PK` | 印章ID |
| `owner_id` | `bigint` | | 所属主体ID (个人为user_id, 企业为enterprise_id) |
| `seal_type` | `smallint` | | 印章类型 (1:个人签名, 2:企业公章, 3:合同章) |
| `seal_name` | `varchar(50)` | | 印章名称 |
| `object_key` | `varchar(255)` | | 印章图片在对象存储中的路径 |
| `created_at` | `timestamp` | | 创建时间 |

## 6. 核心消息队列(MQ)流转设计

为实现服务解耦和异步处理，我们使用RabbitMQ，并定义以下核心的消息流转模式。

| 业务场景 | 生产者 | Exchange名称<br>(类型) | Routing Key | Queue名称 | 消费者 | 核心消息体 (JSON) |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **合同状态变更** | `SigningSvc` | `contract.topic`<br>(Topic) | `contract.signed.first`<br>`contract.signed.all`<br>`contract.rejected` | `notify_queue`<br>`webhook_queue`<br>`log_queue` | `MessageSvc`<br>`WebhookSvc`<br>`LogSvc` | `{ "contractId": "...", "eventType": "...", "timestamp": "...", "operatorId": "..." }` |
| **异步生成证据链** | `SigningSvc` | `evidence.direct`<br>(Direct) | `evidence.generate` | `evidence_gen_queue` | `EvidenceSvc` | `{ "contractId": "...", "requestorId": "..." }` |
| **AI处理任务** | `ContractSvc` | `ai_task.direct`<br>(Direct) | `ai.review.start`<br>`ai.extract.start` | `ai_review_queue`<br>`ai_extract_queue` | `AISvc` | `{ "taskId": "...", "fileObjectKey": "...", "taskType": "..." }` |
| **用户/组织变更** | `AccountSvc`<br>`OrgSvc` | `iam.fanout`<br>(Fanout) | (无) | `iam_sync_to_contract`<br>`iam_sync_to_seal` | `ContractSvc`<br>`SealSvc` | `{ "changeType": "user.rename", "userId": "...", "oldName": "...", "newName": "..." }` |

*   **`contract.topic` (主题交换机)**: 用于广播合同相关的各类事件。`MessageSvc` 订阅所有消息以发送通知；`WebhookSvc` 订阅以触发对外回调；`LogSvc` 订阅以记录审计日志。
*   **`evidence.direct` (直接交换机)**: 用于需要可靠投递的、一对一的任务处理，如生成耗时较长的证据链报告。
*   **`iam.fanout` (扇出交换机)**: 用于广播用户和组织信息的变更事件。当用户更名或企业认证信息变更时，所有关心这些数据的下游服务（如合同服务、印章服务，需要同步冗余信息）都能收到通知并更新自己的数据，保证最终一致性。

## 7. 部署与运维架构

*   **容器化与编排**: 所有微服务都将被打包成 **Docker** 镜像。使用 **Kubernetes (K8s)** 作为容器编排平台，实现服务的自动化部署、弹性伸缩和故障自愈。
*   **CI/CD**: 采用 **GitLab CI/CD** 或 **Jenkins** 构建自动化的持续集成与持续部署流水线。代码提交后，自动触发编译、测试、镜像构建和部署流程。
*   **服务治理**: 借助于 **Istio** 服务网格（或Spring Cloud/Kratos自带能力），实现服务发现、负载均衡、熔断、限流、灰度发布等高级治理能力。 