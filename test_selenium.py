#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium环境测试脚本
"""

import sys
import time

def test_selenium_import():
    """测试Selenium导入"""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        print("✓ Selenium导入成功")
        return True
    except ImportError as e:
        print(f"✗ Selenium导入失败: {e}")
        print("请运行: pip install selenium")
        return False

def test_chrome_driver():
    """测试ChromeDriver"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # 创建驱动
        driver = webdriver.Chrome(options=chrome_options)
        
        # 测试访问网页
        driver.get("https://www.baidu.com")
        title = driver.title
        
        driver.quit()
        
        print(f"✓ ChromeDriver工作正常，测试页面标题: {title}")
        return True
        
    except Exception as e:
        print(f"✗ ChromeDriver测试失败: {e}")
        print("\n可能的解决方案:")
        print("1. 安装Chrome浏览器")
        print("2. 下载ChromeDriver并放到PATH中")
        print("3. 确保ChromeDriver版本与Chrome版本匹配")
        return False

def test_target_website():
    """测试目标网站访问"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 访问目标网站
        target_url = "https://qian.tencent.com/document/53799"
        print(f"正在访问: {target_url}")
        
        driver.get(target_url)
        
        # 等待页面加载
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 查找侧边栏
        sidebar_found = False
        sidebar_selectors = [
            '.theme-doc-sidebar-container',
            'nav[aria-label*="侧边栏"]',
            '.sidebar',
            '.menu'
        ]
        
        for selector in sidebar_selectors:
            try:
                sidebar = driver.find_element(By.CSS_SELECTOR, selector)
                if sidebar:
                    sidebar_found = True
                    print(f"✓ 找到侧边栏: {selector}")
                    break
            except:
                continue
        
        if not sidebar_found:
            print("⚠ 未找到侧边栏，但页面加载成功")
        
        # 查找折叠菜单项
        try:
            collapsed_items = driver.find_elements(
                By.CSS_SELECTOR, 
                '.menu__list-item--collapsed .menu__link--sublist-caret'
            )
            print(f"✓ 找到 {len(collapsed_items)} 个折叠菜单项")
        except Exception as e:
            print(f"⚠ 查找折叠菜单项时出错: {e}")
        
        # 获取页面标题
        title = driver.title
        print(f"✓ 页面标题: {title}")
        
        # 获取所有链接数量
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"✓ 页面包含 {len(links)} 个链接")
        
        driver.quit()
        print("✓ 目标网站访问测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 目标网站访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("="*50)
    print("Selenium环境测试")
    print("="*50)
    
    tests = [
        ("Selenium导入测试", test_selenium_import),
        ("ChromeDriver测试", test_chrome_driver),
        ("目标网站访问测试", test_target_website)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print("测试失败，请检查上述错误信息")
        except Exception as e:
            print(f"测试异常: {e}")
        
        time.sleep(1)  # 短暂暂停
    
    print("\n" + "="*50)
    print(f"测试结果: {passed}/{total} 通过")
    print("="*50)
    
    if passed == total:
        print("🎉 所有测试通过！可以使用Selenium爬虫了")
        print("\n下一步:")
        print("python selenium_main.py --discover-only")
    else:
        print("❌ 部分测试失败，请根据上述提示解决问题")
        print("\n如果无法解决Selenium问题，可以使用普通模式:")
        print("python enhanced_main.py --discover-only")

if __name__ == "__main__":
    main() 