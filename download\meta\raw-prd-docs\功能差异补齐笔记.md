# 功能差异补齐笔记 (对标竞品分析)

**文件目的**: 本文档旨在通过对标腾讯电子签、e签宝、法大大、契约锁、上上签、爱签等行业主流产品，识别“路浩AI电子签”在V3版本中存在的功能差距，并提出V42版本的核心补齐与超越建议。本文档是后续产品详细设计、技术选型和工作量评估的输入。

---

## 1. 核心签约与合同管理模块

| 功能点 | 对标产品表现 | “路浩AI电子签”V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **合同验签** | **腾讯/e签宝**: 提供独立的验签工具，用户上传已签署的PDF，系统验证数字签名是否有效、文件是否被篡改。 | **[补齐]** 必须提供独立的、公开的验签入口。API层面也要提供验签接口。这是建立信任的基础。 | ★★★★★ |
| **合同对比** | **腾讯/法大大**: 提供智能对比工具，高亮显示两份合同（如不同版本）的文本差异。 | **[补齐]** 提供PDF内容对比功能。**[超越]** 利用AI，不仅对比字面差异，还能分析条款语义层面的重大变更，并给出风险提示。 | ★★★★☆ |
| **骑缝章** | **腾讯/契约锁**: 作为高级功能提供，配置合同时可添加骑缝章控件，签署后印章自动均分在每页侧边。 | **[补齐]** 必须支持标准骑缝章功能，这是合同规范性的重要体现。支持配置边距、方向。 | ★★★★★ |
| **合同模板市场** | **e签宝/法大大**: 不仅有企业内部模板，还有一个官方模板市场，提供各行业经过法务审核的标准合同模板，用户可付费使用。 | **[补齐]** 建立官方模板库，覆盖劳动、租赁、采购、销售等高频场景。**[超越]** AI可根据用户行业和需求，从模板市场智能推荐最合适的模板。 | ★★★★☆ |
| **合同解除/作废** | **腾讯/契约锁**: 通过线上发起并签署《解除协议》的方式，使原合同状态变更为"已解除"，流程闭环。作废需要审批流。 | **[补齐]** 提供标准化的线上合同解除/作废流程。作废必须强制关联审批流，并记录操作原因。 | ★★★★☆ |
| **履约管理** | **契约锁**: 核心优势之一。支持在合同中设置履约计划（如付款节点、交付日期），到期自动提醒，并可上传履约凭证。 | **[补齐]** V42应包含基础的履约提醒功能。可从合同文本中通过AI自动识别关键日期和金额，并建议用户创建提醒。 | ★★★☆☆ |

---

## 2. 企业组织与权限管理

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **精细化审批流** | **腾讯/契约锁**: 支持配置多级审批、会签/或签。可将审批流与用印、发合同、模板使用等场景绑定。支持与企业微信/钉钉审批打通。 | **[补齐]** 必须建立一个强大的、独立的审批流引擎。支持图形化配置，满足企业复杂的"先审后签"或"用印申请"需求。 | ★★★★★ |
| **子公司/集团管控** | **腾讯/e签宝**: 提供集团账户功能，主企业可集中管理、查看、审计成员子企业的合同、印章，并能统一分享模板、共享套餐。 | **[补齐]** 明确支持集团模型。主企业可以代子企业发起合同，可以统一管理印章授权。这是服务大型客户的必备功能。 | ★★★★★ |
| **业务员/代理人** | **法大大**: 支持"业务员"角色，业务员自己名下的合同数据相互隔离，但其上级可以查看。 | **[补齐]** 在RBAC（基于角色的权限控制）中，数据权限需要支持"本部门及子部门"、"仅本人"之外，增加"本人及下属"这一维度。 | ★★★★☆ |
| **外部联系人管理** | **腾讯/e签宝**: 提供企业级的外部联系人地址库，方便在发起合同时快速选择、填充签署方信息。 | **[优化]** V3已有基础，但需强化。支持批量导入，并记录对方的历史签署信息（如认证状态）。 | ★★★☆☆ |

---

## 3. AI赋能与智能化

这是"路浩AI电子签"实现差异化竞争的关键领域。

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **AI合同审查** | **法大大/部分竞品开始尝试**: 提供AI审查，识别缺少关键条款、条款约定不明、存在不公平条款等风险。通常作为增值服务。 | **[核心-超越]** 这是我们的核心优势。必须深度打造。不仅能审查，还能**一键生成修改建议**。支持用户上传自己的审查标准和条款库，形成企业专属的审查模型。 | ★★★★★ |
| **AI合同生成** | **V3已规划**: 通过问答式生成合同。 | **[核心-强化]** V42需落地。模型需覆盖更广的合同类型。**[超越]** 实现"草稿续写"和"条款优化"功能，在用户编辑合同时，AI能像Copilot一样提供智能补全和润色建议。 | ★★★★★ |
| **智能归档与检索** | **腾讯/e签宝**: 支持按合同类型、签署方、时间等多维度检索。部分支持OCR全文检索。 | **[超越]** 利用AI自动为合同打上标签（如"含保密条款"、"3年期"、"固定资产采购"），并支持**自然语言搜索**，例如输入"查找去年跟华为签的所有服务器采购合同"。 | ★★★★☆ |
| **智能印章OCR** | **V3已规划**: 上传印章图片，AI自动抠图。 | **[核心-补齐]** 必须实现高精度的AI抠图和像素优化。**[超越]** 增加印章真伪辅助识别功能，通过比对印章线条、文字布局等，对疑似伪造的印章进行风险提示。 | ★★★★☆ |

---

## 4. 安全、合规与开放能力

| 功能点 | 对标产品表现 | "路浩AI电子签"V42补齐/超越策略 | 优先级 |
| :--- | :--- | :--- | :--- |
| **国密算法支持** | **e签宝/契约锁**: 明确支持SM2/SM3/SM4国密算法，以满足政务、金融等信创领域客户需求。 | **[补齐]** 技术架构必须将国密算法作为核心选项，允许用户在签署时选择使用国密标准进行摘要和加密。 | ★★★★★ |
| **区块链存证** | **腾讯/法大大**: 普遍采用区块链技术进行存证，并能出具包含区块链哈希的证据报告，增强司法采信度。 | **[补齐]** 必须整合区块链存证能力，无论是自建联盟链还是对接第三方权威区块链（如至信链、司法链）。 | ★★★★☆ |
| **出证报告** | **所有竞品**: 都能提供具备法律效力的《电子文件签署报告》，作为发生纠纷时的核心证据。 | **[补齐]** 提供一键申请出证报告功能。报告内容需详尽，包含操作日志、IP地址、时间戳、数字签名信息、区块链存证信息等。 | ★★★★★ |
| **嵌入式组件(SDK)** | **腾讯/e签宝**: 提供成熟的前端嵌入式SDK，让用户在自己的业务系统里就能完成签署，无需跳转。 | **[补齐]** 必须提供功能完善且UI可定制的前端组件，这是实现与客户业务系统无缝集成的关键。 | ★★★★★ |
| **事件回调(Webhook)** | **腾讯/所有主流平台**: 提供可靠的Webhook机制，能实时将合同状态（已签署、已完成、已拒签等）通知给客户的业务系统。 | **[补齐]** Webhook是开放能力的基石。需要支持消息重试、签名验证，并提供详尽的事件类型供客户订阅。 | ★★★★☆ |

---

**总结**: V42版本需要在补齐行业标准功能（如骑缝章、验签、审批流、集团管控）的基础上，将 **AI合同审查与生成** 作为核心壁垒进行深度打造，并完善 **国密支持** 与 **开放集成能力**，以此确立产品在市场中的差异化竞争优势。 