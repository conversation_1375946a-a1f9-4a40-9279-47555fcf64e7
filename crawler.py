"""
腾讯千帆文档爬虫主程序
"""

import requests
import time
import random
import json
import os
import logging
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import html2text
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from config import *
from utils import *
from url_manager import URLManager

class TencentQianCrawler:
    """腾讯千帆文档爬虫"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.start_url = START_URL
        self.output_dir = OUTPUT_DIR
        
        # URL管理器
        self.url_manager = URLManager(self.output_dir, self.base_url)
        self.url_lock = threading.Lock()
        
        # 会话管理
        self.session = requests.Session()
        self.setup_session()
        
        # 内容处理
        self.html2text = html2text.HTML2Text()
        self.setup_html2text()
        
        # 进度管理
        self.progress_file = os.path.join(self.output_dir, PROGRESS_CONFIG['progress_file'])
        self.stats = {
            'total_found': 0,
            'total_crawled': 0,
            'total_failed': 0,
            'total_saved': 0
        }
        
        # 设置日志
        self.setup_logging()
        
        # 初始化起始URL
        self.url_manager.add_url(self.start_url)
        
    def setup_session(self):
        """配置请求会话"""
        self.session.headers.update(REQUEST_CONFIG['headers'])
        
    def setup_html2text(self):
        """配置HTML转Markdown转换器"""
        self.html2text.ignore_links = MARKDOWN_CONFIG['ignore_links']
        self.html2text.ignore_images = MARKDOWN_CONFIG['ignore_images']
        self.html2text.body_width = MARKDOWN_CONFIG['body_width']
        self.html2text.protect_links = MARKDOWN_CONFIG['protect_links']
        self.html2text.unicode_snob = MARKDOWN_CONFIG['unicode_snob']
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, LOG_CONFIG['level']),
            format=LOG_CONFIG['format'],
            handlers=[
                logging.FileHandler(
                    os.path.join(self.output_dir, LOG_CONFIG['file']),
                    encoding='utf-8'
                ),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(REQUEST_CONFIG['user_agents'])
        
    def fetch_page(self, url, retries=None):
        """
        获取网页内容，确保UTF-8编码处理
        
        Args:
            url (str): 网页URL
            retries (int): 重试次数
            
        Returns:
            tuple: (是否成功, 响应内容/错误信息)
        """
        if retries is None:
            retries = REQUEST_CONFIG['retry_times']
            
        for attempt in range(retries + 1):
            try:
                # 设置随机User-Agent和编码相关头部
                headers = {
                    'User-Agent': self.get_random_user_agent(),
                    'Accept-Charset': 'utf-8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
                
                # 发送请求
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=REQUEST_CONFIG['timeout']
                )
                
                # 强制设置编码为UTF-8
                response.encoding = 'utf-8'
                
                # 检查响应状态
                if response.status_code == 200:
                    self.logger.info(f"成功获取页面: {url}")
                    # 确保返回的文本是UTF-8编码
                    content = response.text
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='ignore')
                    return True, content
                elif response.status_code == 404:
                    self.logger.warning(f"页面不存在: {url}")
                    return False, "404 Not Found"
                else:
                    self.logger.warning(f"HTTP错误 {response.status_code}: {url}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{retries + 1}): {url} - {str(e)}")
                
                if attempt < retries:
                    # 随机等待后重试
                    wait_time = random.uniform(2, 5) * (attempt + 1)
                    time.sleep(wait_time)
                    
        return False, "Max retries exceeded"
        
    def extract_content(self, html, url):
        """
        提取页面主要内容
        
        Args:
            html (str): HTML内容
            url (str): 页面URL
            
        Returns:
            dict: 提取的内容信息
        """
        soup = BeautifulSoup(html, 'lxml')
        
        # 提取标题
        title = self.extract_title(soup)
        
        # 提取主要内容
        main_content = self.extract_main_content(soup)
        
        # 提取链接
        links = self.extract_links(soup, url)
        
        # 清理内容
        if main_content:
            main_content = self.clean_content(main_content)
            
        return {
            'title': title,
            'content': main_content,
            'links': links,
            'url': url
        }
        
    def extract_title(self, soup):
        """提取页面标题"""
        for selector in CONTENT_CONFIG['title_selectors']:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True)
                if title:
                    return title
        return "Untitled"
        
    def extract_main_content(self, soup):
        """提取主要内容区域"""
        # 移除不需要的元素
        for selector in CONTENT_CONFIG['remove_selectors']:
            for element in soup.select(selector):
                element.decompose()
        
        # 查找主要内容区域
        for selector in CONTENT_CONFIG['main_content_selectors']:
            main_element = soup.select_one(selector)
            if main_element:
                return main_element
                
        # 如果没找到主要内容区域，返回body
        body = soup.find('body')
        if body:
            return body
            
        return soup
        
    def extract_links(self, soup, base_url):
        """提取页面中的链接"""
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            
            # 标准化URL
            normalized_url = normalize_url(href, base_url)
            
            if normalized_url and is_valid_url(normalized_url, self.base_url):
                links.append(normalized_url)
                
        return list(set(links))  # 去重
        
    def clean_content(self, content_element):
        """清理内容元素"""
        # 移除脚本和样式
        for script in content_element(['script', 'style']):
            script.decompose()
            
        # 移除注释
        from bs4 import Comment
        comments = content_element.find_all(string=lambda text: isinstance(text, Comment))
        for comment in comments:
            comment.extract()
            
        return content_element
        
    def convert_to_markdown(self, html_content):
        """将HTML转换为Markdown"""
        try:
            markdown_content = self.html2text.handle(str(html_content))
            
            # 后处理：清理多余的空行
            lines = markdown_content.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                line = line.rstrip()
                if line:
                    cleaned_lines.append(line)
                    prev_empty = False
                elif not prev_empty:
                    cleaned_lines.append('')
                    prev_empty = True
                    
            return '\n'.join(cleaned_lines)
            
        except Exception as e:
            self.logger.error(f"Markdown转换失败: {str(e)}")
            return str(html_content)
            
    def save_content(self, content_info):
        """
        保存内容到文件
        
        Args:
            content_info (dict): 内容信息
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 转换为Markdown
            if content_info['content']:
                markdown_content = self.convert_to_markdown(content_info['content'])
            else:
                markdown_content = "无内容"
                
            # 生成文件名
            filename = generate_filename_from_content(
                content_info['title'],
                markdown_content[:500],  # 使用前500字符生成文件名
                content_info['url']
            )
            
            # 确保文件名是有效的UTF-8字符串
            if isinstance(filename, bytes):
                filename = filename.decode('utf-8', errors='ignore')
            
            # 添加扩展名
            filename += FILE_CONFIG['markdown_extension']
            
            # 确保文件名唯一
            filepath = os.path.join(self.output_dir, filename)
            filepath = ensure_unique_filename(filepath)
            
            # 创建文件内容，确保所有内容都是UTF-8编码
            title = content_info['title'] if content_info['title'] else "无标题"
            if isinstance(title, bytes):
                title = title.decode('utf-8', errors='ignore')
            
            file_content = f"""# {title}

> 来源: {content_info['url']}
> 抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

---

{markdown_content}
"""
            
            # 保存文件，强制使用UTF-8编码，添加BOM以确保Windows兼容性
            with open(filepath, 'w', encoding='utf-8-sig', newline='\n') as f:
                f.write(file_content)
                
            # 记录日志时使用安全的文件名显示
            safe_filename = os.path.basename(filepath)
            if isinstance(safe_filename, bytes):
                safe_filename = safe_filename.decode('utf-8', errors='replace')
            
            self.logger.info(f"保存文件: {safe_filename}")
            self.stats['total_saved'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            return False
            
    def add_urls_to_queue(self, urls, source_url=None):
        """添加URL到待爬取队列"""
        with self.url_lock:
            added_count = self.url_manager.add_urls_batch(urls, source_url)
            self.stats['total_found'] += added_count
            if added_count > 0:
                self.url_manager.save_urls()  # 立即保存新发现的URL
                    
    def crawl_single_page(self, url):
        """
        爬取单个页面
        
        Args:
            url (str): 页面URL
            
        Returns:
            bool: 是否成功
        """
        try:
            # 随机等待
            random_sleep(
                REQUEST_CONFIG['delay_min'],
                REQUEST_CONFIG['delay_max']
            )
            
            # 获取页面内容
            success, html_content = self.fetch_page(url)
            
            if not success:
                with self.url_lock:
                    self.url_manager.mark_failed(url, html_content)
                    self.stats['total_failed'] += 1
                return False
                
            # 提取内容
            content_info = self.extract_content(html_content, url)
            
            # 添加新发现的链接到队列
            if content_info['links']:
                self.add_urls_to_queue(content_info['links'], url)
                
            # 保存内容
            save_success = self.save_content(content_info)
            
            # 更新状态
            with self.url_lock:
                if save_success:
                    self.url_manager.mark_completed(url)
                    self.stats['total_crawled'] += 1
                else:
                    self.url_manager.mark_failed(url, "保存文件失败")
                
            return save_success
            
        except Exception as e:
            self.logger.error(f"爬取页面失败: {url} - {str(e)}")
            with self.url_lock:
                self.url_manager.mark_failed(url, str(e))
                self.stats['total_failed'] += 1
            return False
            

                
    def run(self, max_pages=None, max_workers=None):
        """
        运行爬虫
        
        Args:
            max_pages (int): 最大页面数，None表示不限制
            max_workers (int): 最大工作线程数
        """
        if max_workers is None:
            max_workers = PROGRESS_CONFIG['max_concurrent']
            
        self.logger.info(f"开始爬取，最大页面数: {max_pages or '无限制'}, 最大线程数: {max_workers}")
        
        # 创建进度条
        pbar = tqdm(
            desc="爬取进度",
            unit="页",
            dynamic_ncols=True
        )
        
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                while not self.url_manager.is_all_completed():
                    # 检查是否达到最大页面数
                    if max_pages and self.stats['total_crawled'] >= max_pages:
                        self.logger.info(f"已达到最大页面数: {max_pages}")
                        break
                        
                    # 获取一批待处理的URL
                    with self.url_lock:
                        pending_urls = self.url_manager.get_pending_urls()
                        current_batch = pending_urls[:max_workers]
                    
                    if not current_batch:
                        break
                        
                    # 提交任务
                    futures = [
                        executor.submit(self.crawl_single_page, url)
                        for url in current_batch
                    ]
                    
                    # 等待完成
                    for future in as_completed(futures):
                        result = future.result()
                        pbar.update(1)
                        
                        # 获取最新统计信息
                        url_stats = self.url_manager.get_statistics()
                        pbar.set_postfix({
                            '成功': self.stats['total_crawled'],
                            '失败': self.stats['total_failed'],
                            '待处理': url_stats['pending_urls']
                        })
                        
                    # 定期保存进度
                    if self.stats['total_crawled'] % PROGRESS_CONFIG['save_interval'] == 0:
                        self.url_manager.save_urls()
                        
        except KeyboardInterrupt:
            self.logger.info("用户中断爬取")
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {str(e)}")
        finally:
            pbar.close()
            # 保存最终状态
            self.url_manager.save_urls()
            self.print_summary()
            
    def print_summary(self):
        """打印爬取摘要"""
        # 获取最终统计信息
        url_stats = self.url_manager.get_statistics()
        
        print("\n" + "="*50)
        print("爬取摘要")
        print("="*50)
        print(f"发现页面数: {url_stats['total_urls']}")
        print(f"成功爬取: {url_stats['completed_urls']}")
        print(f"爬取失败: {url_stats['failed_urls']}")
        print(f"保存文件: {self.stats['total_saved']}")
        print(f"待处理页面: {url_stats['pending_urls']}")
        print(f"完成率: {url_stats['completion_rate']:.2f}%")
        print("="*50)
        
        # 生成详细报告
        self.url_manager.export_report()
        
        # 检查是否全部完成
        if self.url_manager.is_all_completed():
            print("所有URL都已处理完成!")
        else:
            print("还有未完成的URL，可以重新运行继续抓取")

if __name__ == "__main__":
    crawler = TencentQianCrawler()
    
    # 运行爬虫
    crawler.run(
        max_pages=100,  # 限制最大页面数，测试时可以设置较小的值
        max_workers=3   # 并发数，不要设置太大以免对服务器造成压力
    )