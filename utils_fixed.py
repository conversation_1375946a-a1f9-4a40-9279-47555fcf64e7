"""
工具函数模块 - 修复版本
修复了函数命名不一致的问题
"""

import re
import os
import time
import random
import hashlib
from urllib.parse import urljoin, urlparse
import jieba
from config import FILE_CONFIG

def clean_filename(filename):
    """
    清理文件名，移除非法字符，确保UTF-8编码兼容
    
    Args:
        filename (str): 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    if not filename:
        return 'untitled'
    
    # 确保是字符串类型
    if not isinstance(filename, str):
        filename = str(filename)
    
    # 移除HTML标签
    filename = re.sub(r'<[^>]+>', '', filename)
    
    # 替换非法字符
    for char in FILE_CONFIG['forbidden_chars']:
        filename = filename.replace(char, FILE_CONFIG['replacement_char'])
    
    # 移除多余的空白字符
    filename = re.sub(r'\s+', ' ', filename).strip()
    
    # 移除控制字符和不可见字符
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # 限制长度（考虑中文字符）
    if len(filename) > FILE_CONFIG['max_filename_length']:
        # 对于中文，按字符数而不是字节数截取
        filename = filename[:FILE_CONFIG['max_filename_length']]
        # 确保不在中文字符中间截断
        if filename and ord(filename[-1]) > 127:
            filename = filename[:-1]
    
    # 如果文件名为空或只包含特殊字符，使用默认名称
    if not filename or filename.replace(FILE_CONFIG['replacement_char'], '').strip() == '':
        filename = 'untitled'
    
    return filename

def generate_filename_from_content(title, content, url):
    """
    基于内容生成中文文件名
    
    Args:
        title (str): 页面标题
        content (str): 页面内容
        url (str): 页面URL
        
    Returns:
        str: 生成的文件名（不含扩展名）
    """
    # 优先使用标题
    if title and title.strip():
        filename = clean_filename(title.strip())
        if len(filename) > 5:  # 标题足够长
            return filename
    
    # 从内容中提取关键词
    if content:
        # 提取前200个字符用于关键词分析
        content_sample = content[:200]
        
        # 使用jieba分词提取关键词
        words = jieba.cut(content_sample)
        
        # 过滤无意义的词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', 
                     '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', 
                     '会', '着', '没有', '看', '好', '自己', '这', '那', '它', '他', '她'}
        
        keywords = []
        for word in words:
            word = word.strip()
            if len(word) > 1 and word not in stop_words and word.isalnum():
                keywords.append(word)
        
        if keywords:
            # 取前3-5个关键词组成文件名
            filename = '_'.join(keywords[:5])
            filename = clean_filename(filename)
            if len(filename) > 5:
                return filename
    
    # 如果都失败，从URL中提取
    if url:
        parsed = urlparse(url)
        path_parts = [part for part in parsed.path.split('/') if part]
        if path_parts:
            last_part = path_parts[-1]
            # 移除数字ID
            last_part = re.sub(r'\d+', '', last_part)
            if last_part:
                filename = clean_filename(last_part)
                if len(filename) > 3:
                    return filename
    
    # 最后的备用方案：使用URL的hash值
    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
    return f"page_{url_hash}"

# 添加兼容性函数，解决函数命名不一致问题
def generate_filename(title, url):
    """
    兼容性函数，调用标准函数
    用于修复selenium_crawler.py中的函数调用错误
    
    Args:
        title (str): 页面标题
        url (str): 页面URL
        
    Returns:
        str: 生成的文件名（不含扩展名）
    """
    return generate_filename_from_content(title, "", url)

def is_valid_url(url, base_url):
    """
    验证URL是否有效且在允许的范围内
    
    Args:
        url (str): 待验证的URL
        base_url (str): 基础URL
        
    Returns:
        bool: 是否有效
    """
    if not url:
        return False
    
    # 解析URL
    parsed = urlparse(url)
    base_parsed = urlparse(base_url)
    
    # 检查域名
    if parsed.netloc and parsed.netloc != base_parsed.netloc:
        return False
    
    # 检查路径
    if not parsed.path.startswith('/document'):
        return False
    
    # 排除特定扩展名
    excluded_extensions = ['.pdf', '.doc', '.docx', '.zip', '.rar', '.exe']
    if any(parsed.path.lower().endswith(ext) for ext in excluded_extensions):
        return False
    
    # 排除特定模式
    excluded_patterns = [
        r'/api/',
        r'/login',
        r'/logout', 
        r'/search',
        r'#',
        r'javascript:',
        r'mailto:',
        r'tel:'
    ]
    
    for pattern in excluded_patterns:
        if re.search(pattern, url, re.IGNORECASE):
            return False
    
    return True

def normalize_url(url, base_url):
    """
    标准化URL
    
    Args:
        url (str): 原始URL
        base_url (str): 基础URL
        
    Returns:
        str: 标准化后的URL
    """
    if not url:
        return None
    
    # 处理相对URL
    if url.startswith('//'):
        url = 'https:' + url
    elif url.startswith('/'):
        base_parsed = urlparse(base_url)
        url = f"{base_parsed.scheme}://{base_parsed.netloc}{url}"
    elif not url.startswith('http'):
        url = urljoin(base_url, url)
    
    # 移除fragment
    parsed = urlparse(url)
    url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    if parsed.query:
        url += f"?{parsed.query}"
    
    return url

def random_sleep(min_time=1, max_time=3):
    """
    随机睡眠
    
    Args:
        min_time (float): 最小睡眠时间
        max_time (float): 最大睡眠时间
    """
    sleep_time = random.uniform(min_time, max_time)
    time.sleep(sleep_time)

def extract_text_summary(text, max_length=100):
    """
    提取文本摘要
    
    Args:
        text (str): 原始文本
        max_length (int): 最大长度
        
    Returns:
        str: 文本摘要
    """
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 如果文本较短，直接返回
    if len(text) <= max_length:
        return text
    
    # 截取并确保在词边界结束
    summary = text[:max_length]
    
    # 尝试在句号处截断
    last_period = summary.rfind('。')
    if last_period > max_length // 2:
        summary = summary[:last_period + 1]
    else:
        # 尝试在逗号处截断
        last_comma = summary.rfind('，')
        if last_comma > max_length // 2:
            summary = summary[:last_comma + 1]
        else:
            # 添加省略号
            summary = summary + '...'
    
    return summary

def ensure_unique_filename(filepath):
    """
    确保文件名唯一，如果存在重名则添加序号
    
    Args:
        filepath (str): 文件路径
        
    Returns:
        str: 唯一的文件路径
    """
    if not os.path.exists(filepath):
        return filepath
    
    base, ext = os.path.splitext(filepath)
    counter = 1
    
    while True:
        new_filepath = f"{base}_{counter}{ext}"
        if not os.path.exists(new_filepath):
            return new_filepath
        counter += 1

def format_file_size(size_bytes):
    """
    格式化文件大小
    
    Args:
        size_bytes (int): 字节数
        
    Returns:
        str: 格式化的大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def is_chinese_char(char):
    """
    判断是否为中文字符
    
    Args:
        char (str): 字符
        
    Returns:
        bool: 是否为中文字符
    """
    return '\u4e00' <= char <= '\u9fff'

def has_chinese(text):
    """
    判断文本是否包含中文
    
    Args:
        text (str): 文本
        
    Returns:
        bool: 是否包含中文
    """
    return any(is_chinese_char(char) for char in text)

def truncate_text(text, max_length, suffix='...'):
    """
    截断文本
    
    Args:
        text (str): 原始文本
        max_length (int): 最大长度
        suffix (str): 后缀
        
    Returns:
        str: 截断后的文本
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix