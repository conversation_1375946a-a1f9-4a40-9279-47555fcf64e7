# 自定义审批流常见问题

> 导航路径: 常见问题 > 企业微信端常见问题 > 自定义审批流常见问题
> 来源: https://qian.tencent.com/document/79407/
> 抓取时间: 2025-06-15 15:54:18

---

### 审批流如何配置或切换？

腾讯电子签内集成了**企业微信审批流引擎和电子签内置审批流引擎** ，支持对创建印章审批流和合同用印申请审批流使用，企业可以根据自身业务场景选择合适的审批流切换并执行，如何配置审批流详情参见 [审批流配置](/document/77772 "/document/77772") 文档。

### 如何为不同合同类型的设置不同的审批流程？

**说明：**

当前仅支持企业微信自定义审批流设置条件分支审批（如不同合同类型设置不同审批分支），电子签内置审批流敬请期待。

客户可以在合同管理中设置多个合同类型，例如：**合同类型1、合同类型2** 。如果想实现签署**合同类型1** 的合同时，所发起的用印申请/合同审批由**员工 A** 进行审批；签署**合同类型2** 的合同时，所发起的用印申请/合同审批由**员工 B** 进行审批，请参考如下指引：

可以通过企业微信自定义审批流，来实现不同合同类型的用印申请/合同审批走不同的审批流程，具体步骤如下：

1. 查看**合同类型审批分支编号** 。需要腾讯电子签企业的超级管理员打开腾讯电子签工作台，找到**合同 > 合同类型设置，**如下图所示，单击右上角齿轮并勾选合同类型审批分支编号。勾选后，在列表中就会显示不同合同类型对应的审批分支编号（后面步骤中会使用到合同类型审批分支编号）。

﻿

﻿

﻿  

2. 在**企业设置 > 扩展服务 > 审批流配置**中，将企业的审批流引擎调整为**企业微信审批流-自定义审批流** ，并找到**腾讯电子签合同用印申请** /**合同审批审批流** ，单击**去配置** 。

﻿

﻿

﻿  

3. 请确认是否配置自定义审批流程，即将打开企业微信管理后台配置审批流，请单击**确认** ，继续配置自定义审批流。

**说明：**

如您不是企业微信管理员身份，请联系企业微信管理员协助您登录企业微信管理后台。

﻿

﻿

﻿  

4. 系统跳转至企业微信管理后台的审批应用详情页面，请在**模板管理** 下的其他模板中选择用印申请/合同审批的审批模板，并单击**编辑** 进入。

**说明：**

请确保您已经在审批应用的可见范围内，如果不在，请联系企业微信管理员，前往企业微信后台将您纳入应用可见范围。

﻿

﻿

﻿  

5. 将页面切换进入规则设置，并单击**设置** 进入企业微信审批流程页面。

﻿

﻿

﻿  

6. 因为不同场景需要审批到不同责任人，则需要配置条件分支，单击 **\+ 图标 ＞ 条件分支** 。

﻿

﻿

﻿  

7. 创建完新的条件分支，可以单击**请设置条件** ，实现不同合同类型的用印申请/合同审批走不同审批分支的效果。例如在条件分支设置中了**合同类型审批分支编号 = 102，** 编号102合同类型的合同进行签署/发起时，发起的用印申请/合同审批就会走该审批分支。

﻿

﻿

﻿  

### 如何为不同印章的用印申请设置不同的审批流程？

**说明：**

当前仅支持企业微信自定义审批流设置条件分支审批（如不同印章类型设置不同审批分支），电子签内置审批流敬请期待。

客户可以在电子签添加多个同类型的印章，例如：**合同专用章1、合同专用章2** 。如果想实现申请使用**合同专用章1** 时，所发起的用印申请由**员工 A** 进行审批；申请使用**合同专用章2** 时，所发起的用印申请由**员工 B** 进行审批，请参考如下指引：

可以通过企业微信自定义审批流，来实现不同印章的用印申请走不同的审批流程，具体步骤如下：

1. 查看**印章审批分支编号** 。需要腾讯电子签企业的印章管理员打开腾讯电子签工作台，找到**印章模块** 下某个具体印章（例如合同专用章）的印章详情，在基本信息内查看印章审批分支编号（后面步骤中会使用到该编号）。

﻿

﻿

﻿  

2. 在**企业设置 > 扩展服务 > 审批流配置**中，将企业的审批流引擎调整为**企业微信审批流-自定义审批流** ，并找到**腾讯电子签合同用印申请审批流** ，单击**去配置** 。

﻿

﻿

﻿  

3. 请确认是否配置自定义审批流程，即将打开企业微信管理后台配置审批流，请单击**确认** ，继续配置自定义审批流。

**说明：**

如您不是企业微信管理员身份，请联系企业微信管理员协助您登录企业微信管理后台。

﻿

﻿

﻿  

4. 系统跳转至企业微信管理后台的审批应用详情页面，请在**模板管理** 下的其他模板中选择用印申请审批模板，并单击**编辑** 进入。

**说明：**

请确保您已经在审批应用的可见范围内，如果不在，请联系企业微信管理员，前往企业微信后台将您纳入应用可见范围。

﻿

﻿

﻿  

5. 将页面切换进入规则设置，并单击**设置** 进入企业微信审批流程页面。

﻿

﻿

﻿  

6. 因为不同印章需要审批到不同责任人，则需要配置条件分支，单击 **\+ 图标 ＞ 条件分支** 。

﻿

﻿

﻿  

7. 创建完新的条件分支，可以单击**请设置条件** ，实现不同印章的用印申请走不同审批分支的效果。例如在条件分支设置中了**印章审批分支编号 = 1** ，申请编号为1的印章盖章时，发起的用印申请就会走该审批分支。

﻿

﻿

﻿  

### 如何为不同子企业的用印申请设置不同的审批流程？

主、子企业都在同一企业微信办公的集团客户，使用腾讯电子签会碰到如下场景：我在主企业中签署子企业 A 的合同时，需要加盖子**企业 A** 的印章，发起的用印申请由**子企业 A** 的相关负责人进行审批，如果签**子企业 B** 的合同时，则由**子企业 B** 的相关负责人进行审批。

可以通过企业微信自定义审批流，来实现不同子企业的用印申请走不同的审批流程，具体步骤如下：

1. 查看不同子企业的**成员企业审批分支编号** 。打开腾讯电子签工作台，找到**组织管理 > 集团组织管理，**如下图所示，单击右上角齿轮并勾选成员企业审批编号。勾选后，在列表中就会显示不同子企业对应的编号（后面步骤中会使用到成员企业审批分支编号）。

﻿

﻿

﻿  

2. 在**企业设置 > 扩展服务 > 审批流配置**中，将企业的审批流引擎调整为**企业微信审批流-自定义审批流** ，并找到**腾讯电子签合同用印申请审批流** ，单击**去配置** 。

﻿

﻿

﻿  

3. 请确认是否配置自定义审批流程，即将打开企业微信管理后台配置审批流，请单击**确认** ，继续配置自定义审批流。

**说明：**

如您不是企业微信管理员身份，请联系企业微信管理员协助您登录企业微信管理后台。

﻿

﻿

﻿  

4. 系统跳转至企业微信管理后台的审批应用详情页面，请在**模板管理** 下的其他模板中选择用印申请审批模板，并单击**编辑** 进入。

**说明：**

请确保您已经在审批应用的可见范围内，如果不在，请联系企业微信管理员，前往企业微信后台将您纳入应用可见范围。

﻿

﻿

﻿  

5. 将页面切换进入规则设置，并单击**设置** 进入企业微信审批流程页面。

﻿

﻿

﻿  

6. 因为不同场景需要审批到不同责任人，则需要配置条件分支，单击 **\+ 图标 ＞ 条件分支** 。

﻿

﻿

﻿  

7. 创建完新的条件分支，可以单击**请设置条件** ，实现不同子企业的用印申请走不同审批分支的效果。例如在条件分支设置中了**成员企业审批分支编号 = 2，** 编号2对应企业的用印申请，就会走该审批分支。

﻿

﻿

﻿