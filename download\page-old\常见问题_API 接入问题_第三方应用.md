# 第三方应用

> 导航路径: 常见问题 > API 接入问题 > 第三方应用
> 来源: https://qian.tencent.com/document/116842
> 抓取时间: 2025-06-15 15:54:18

---

### 报错“Agent.ProxyOperator.OpenId 指定的这个子企业员工未完成注册”

第三方应用集成的所有接口都需要**已认证** 的子客企业员工发起，具体认证流程请参见 [接入前必读](https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E6%8E%A5%E5%85%A5%E5%89%8D%E5%BF%85%E8%AF%BB "https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E6%8E%A5%E5%85%A5%E5%89%8D%E5%BF%85%E8%AF%BB") 和 [子客入驻](https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E5%AD%90%E5%AE%A2%E5%85%A5%E9%A9%BB) 小节。

### 报错“Agent.ProxyOrganizationOpenId 指定的这个子企业还没有完成认证，无法完成此操作”

第三方应用集成的所有接口都需要**已认证** 的子客企业员工发起，具体认证流程请参见 [接入前必读](https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E6%8E%A5%E5%85%A5%E5%89%8D%E5%BF%85%E8%AF%BB "https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E6%8E%A5%E5%85%A5%E5%89%8D%E5%BF%85%E8%AF%BB") 和 [子客入驻](https://qian.tencent.com/developers/integration-assistant/result/?solutionId=4d4af055-d35d-4c60-ae75-5500884fe87c#%E5%AD%90%E5%AE%A2%E5%85%A5%E9%A9%BB) 小节。

### 认证报错“用户不匹配。该链接已被 XX 占用并发起认证流程，您无法继续使用该链接进行认证”

出现以下报错是由于 XX 已经在认证中了，此时再把认证链接给到其他人，则会报错。

﻿

﻿

﻿  

**解决方式：**

方式1：让 XX 继续走认证流程。

方式2：如果要换人认证，需要更换 `Agent.ProxyOperator.OpenId`，重新 [生成认证链接](https://qian.tencent.com/developers/partnerApis/accounts/CreateConsoleLoginUrl "https://qian.tencent.com/developers/partnerApis/accounts/CreateConsoleLoginUrl")。

### 如何修改子客企业的 OpenId？

子客企业的 OpenId 无法修改，除非 [注销企业](https://qian.tencent.com/developers/partnerApis/accounts/CreateCloseOrganizationUrl "https://qian.tencent.com/developers/partnerApis/accounts/CreateCloseOrganizationUrl") 后重新认证。子客企业员工的 OpenId 也无法修改，除非让 [员工离职](https://qian.tencent.com/developers/partnerApis/accounts/SyncProxyOrganizationOperators "https://qian.tencent.com/developers/partnerApis/accounts/SyncProxyOrganizationOperators") 后重新加入。

### 同一个人可以同时加入一个应用下的两个子客企业吗？

可以。但在认证时需要定义不同的 `ProxyOperator.Openld`。

### 报错“渠道应用号和此渠道子客不具有授权关系，请确认参数或授权关系后重试”

报错是指传参中的子客企业（`Agent.ProxyOrganizationOpenId`）未在所传应用号（`AppId`） 下认证。

### 员工加入报错“该身份证号已完成实名认证并加入渠道企业”

例如张三已经加入到了子客企业 A，openId为 `openId1`，此时张三又以 `OpenId2` 为标识再次走加入子客 A 的流程，则会报以下错误：

﻿

﻿

﻿  

**注意：**

如果想更改张三在子客 A 中的 openId，需要离职再重新加入。

### 子客员工无权下载合同是怎么回事？

子客员工在小程序中下载合同报以下权限错误，是由于其角色（默认业务员）不具备下载权限： 

﻿

﻿

此时需要给对应员工分配**业务管理员** 权限： 

﻿

﻿

### 报错“参数错误，企业经办人 OpenId 已经在其他企业实名，或者是用模板发起合同时模板配置的渠道子客签署的角色传递了其他企业签署方”

在调 [用模板创建签署流程](https://qian.tencent.com/developers/partnerApis/startFlows/CreateFlowsByTemplates/ "https://qian.tencent.com/developers/partnerApis/startFlows/CreateFlowsByTemplates/") 接口时报此错误，很可能是在平台企业的「应用模板库」中配置模板时指定错了签署方，请参考下图检查：

﻿

﻿

﻿  

### 配置模板时找不到数据表格控件？

在平台企业的**应用模板库管理** 中配置带有数据表格的模板时，需要先在**应用模板控件管理** 中添加数据表格控件：

﻿

﻿

﻿  

### 报错“参数错误，指定的应用号不存在”

常见原因是，指定的 `AppId` 传入了正式/测试环境的 AppId，而接口请求到了测试/正式环境。

**联调环境地址：**

环境| 地址| 说明  
---|---|---  
文件服务的 EndPoint| file.test.ess.tencent.cn| UploadFiles 接口使用  
通用接口请求 EndPoint| essbasic.test.ess.tencent.cn| 除UploadFiles 外其他接口使用  
  
**线上环境地址：**

环境| 地址| 说明  
---|---|---  
文件服务的 EndPoint| file.ess.tencent.cn| UploadFiles 接口使用  
通用接口请求 EndPoint| essbasic.tencentcloudapi.com| 除UploadFiles 外其他接口使用  
  
### 报错“被授权企业未认证”

在调用 [创建企业自动签授权链接](https://qian.tencent.com/developers/partnerApis/enterpriseUsers/CreatePartnerAutoSignAuthUrl/ "https://qian.tencent.com/developers/partnerApis/enterpriseUsers/CreatePartnerAutoSignAuthUrl/") 接口时如果报此错误，请检查接口中的 `AuthorizedOrganizationId` 字段是否传入的是**企业 ID** （非企业 OpenId）。