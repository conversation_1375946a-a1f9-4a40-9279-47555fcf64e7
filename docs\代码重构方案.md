# 代码重构方案

## 重构目标

1. **消除代码重复**：创建基础类，减少60%以上的重复代码
2. **修复函数命名问题**：统一函数命名规范
3. **优化JavaScript处理**：增强Selenium菜单展开能力
4. **提升代码质量**：改善可维护性和扩展性

## 重构计划

### 第一步：创建基础爬虫类

#### 1.1 BaseCrawler类设计
```python
# base_crawler.py
class BaseCrawler:
    """基础爬虫类，包含所有通用功能"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.start_url = START_URL
        self.output_dir = OUTPUT_DIR
        self.url_manager = URLManager(self.output_dir, self.base_url)
        self.session = requests.Session()
        self.setup_session()
        self.setup_html2text()
        self.setup_logging()
        self.stats = {'total_found': 0, 'total_crawled': 0, 'total_failed': 0, 'total_saved': 0}
    
    def setup_session(self):
        """设置HTTP会话 - 通用方法"""
        pass
    
    def setup_html2text(self):
        """设置HTML转Markdown - 通用方法"""
        pass
    
    def setup_logging(self):
        """设置日志 - 通用方法"""
        pass
    
    def extract_title(self, soup):
        """提取页面标题 - 通用方法"""
        pass
    
    def extract_main_content(self, soup):
        """提取主要内容 - 通用方法"""
        pass
    
    def clean_content(self, content_element):
        """清理内容元素 - 通用方法"""
        pass
    
    def convert_to_markdown(self, html_content):
        """转换HTML为Markdown - 通用方法"""
        pass
    
    def save_content(self, content_info):
        """保存内容到文件 - 通用方法"""
        pass
    
    def extract_content(self, html, url):
        """提取页面内容 - 通用方法"""
        pass
    
    def print_summary(self):
        """打印爬取摘要 - 通用方法"""
        pass
```

#### 1.2 具体实现类
```python
# enhanced_crawler_v2.py
class EnhancedTencentQianCrawler(BaseCrawler):
    """增强版爬虫 - 继承基础功能"""
    
    def __init__(self):
        super().__init__()
        # 只需要实现特有的初始化逻辑
    
    def extract_sidebar_links(self, html, base_url):
        """增强版特有的链接提取逻辑"""
        pass
    
    def discover_all_links(self):
        """增强版特有的链接发现逻辑"""
        pass

# selenium_crawler_v2.py  
class SeleniumTencentQianCrawler(BaseCrawler):
    """Selenium版爬虫 - 继承基础功能"""
    
    def __init__(self, headless=True):
        super().__init__()
        self.headless = headless
        self.driver = None
        self.setup_selenium()
    
    def setup_selenium(self):
        """Selenium特有的设置"""
        pass
    
    def expand_all_menus_enhanced(self):
        """增强的菜单展开策略"""
        pass
```

### 第二步：修复函数命名问题

#### 2.1 统一函数命名
```python
# utils.py 修复
def generate_filename_from_content(title, content, url):
    """标准函数名 - 保持不变"""
    pass

# 添加兼容性函数
def generate_filename(title, url):
    """兼容性函数，调用标准函数"""
    return generate_filename_from_content(title, "", url)
```

#### 2.2 修复selenium_crawler.py
```python
# 第467行修复
# 原代码：filename = generate_filename(content_info['title'], content_info['url'])
# 修复为：
filename = generate_filename_from_content(
    content_info['title'],
    content_info['content'][:500],
    content_info['url']
)
```

### 第三步：增强JavaScript处理

#### 3.1 智能菜单展开策略
```python
class EnhancedSeleniumCrawler(BaseCrawler):
    """增强的Selenium爬虫"""
    
    def expand_all_menus_smart(self, max_attempts=5):
        """智能菜单展开策略"""
        
        # 1. 多种选择器策略
        selectors = [
            # 主要选择器
            '.menu__list-item--collapsed .menu__link--sublist-caret',
            '[aria-expanded="false"]',
            '.collapsed .expand-button',
            
            # 备用选择器
            '.sidebar-item.collapsed',
            '.nav-item.collapsed',
            '.tree-item.collapsed',
            
            # 通用选择器
            '[class*="collapsed"]',
            '[class*="closed"]'
        ]
        
        total_expanded = 0
        
        for attempt in range(max_attempts):
            attempt_expanded = 0
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if self.is_element_expandable(element):
                            if self.expand_element_safely(element):
                                attempt_expanded += 1
                                total_expanded += 1
                                
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
                    continue
            
            if attempt_expanded == 0:
                self.logger.info(f"第{attempt + 1}轮没有新的展开项，停止尝试")
                break
            
            self.logger.info(f"第{attempt + 1}轮展开了 {attempt_expanded} 个菜单项")
            
            # 等待动态内容加载
            self.wait_for_dynamic_content()
        
        self.logger.info(f"总共展开了 {total_expanded} 个菜单项")
        return total_expanded
    
    def is_element_expandable(self, element):
        """判断元素是否可展开"""
        try:
            # 检查元素是否可见
            if not element.is_displayed():
                return False
            
            # 检查是否已经展开
            if element.get_attribute('aria-expanded') == 'true':
                return False
            
            # 检查是否包含折叠类名
            class_names = element.get_attribute('class') or ''
            if 'collapsed' not in class_names.lower():
                return False
            
            return True
            
        except Exception:
            return False
    
    def expand_element_safely(self, element):
        """安全地展开元素"""
        try:
            # 滚动到元素可见
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)
            
            # 尝试点击
            element.click()
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.debug(f"展开元素失败: {e}")
            return False
    
    def wait_for_dynamic_content(self, timeout=10):
        """等待动态内容加载"""
        try:
            # 等待AJAX请求完成
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script(
                    "return (typeof jQuery !== 'undefined') ? jQuery.active === 0 : true"
                )
            )
            
            # 等待新元素出现
            time.sleep(2)
            
        except TimeoutException:
            self.logger.warning("等待动态内容超时")
```

#### 3.2 多策略链接发现
```python
def discover_all_links_comprehensive(self):
    """综合链接发现策略"""
    
    all_links = set()
    
    # 策略1：Selenium完整展开
    selenium_links = self.discover_with_selenium()
    all_links.update(selenium_links)
    self.logger.info(f"Selenium策略发现 {len(selenium_links)} 个链接")
    
    # 策略2：API接口探测
    api_links = self.discover_with_api()
    all_links.update(api_links)
    self.logger.info(f"API策略发现 {len(api_links)} 个新链接")
    
    # 策略3：站点地图解析
    sitemap_links = self.discover_with_sitemap()
    all_links.update(sitemap_links)
    self.logger.info(f"站点地图策略发现 {len(sitemap_links)} 个新链接")
    
    # 策略4：深度递归
    recursive_links = self.discover_recursive(list(all_links)[:10])
    all_links.update(recursive_links)
    self.logger.info(f"递归策略发现 {len(recursive_links)} 个新链接")
    
    # 更新URL管理器
    added_count = self.url_manager.add_urls_batch(list(all_links), self.start_url)
    self.logger.info(f"总共发现 {len(all_links)} 个链接，新增 {added_count} 个")
    
    return list(all_links)

def discover_with_api(self):
    """尝试通过API发现链接"""
    api_links = set()
    
    # 常见的API端点
    api_endpoints = [
        '/api/docs/list',
        '/api/navigation',
        '/api/sitemap',
        '/document/api/list'
    ]
    
    for endpoint in api_endpoints:
        try:
            api_url = urljoin(self.base_url, endpoint)
            response = self.session.get(api_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                links = self.extract_links_from_api_response(data)
                api_links.update(links)
                
        except Exception as e:
            self.logger.debug(f"API端点 {endpoint} 失败: {e}")
    
    return api_links

def discover_with_sitemap(self):
    """通过站点地图发现链接"""
    sitemap_links = set()
    
    sitemap_urls = [
        '/sitemap.xml',
        '/sitemap_index.xml',
        '/robots.txt'
    ]
    
    for sitemap_url in sitemap_urls:
        try:
            full_url = urljoin(self.base_url, sitemap_url)
            response = self.session.get(full_url, timeout=10)
            
            if response.status_code == 200:
                if sitemap_url.endswith('.xml'):
                    links = self.parse_sitemap_xml(response.text)
                else:
                    links = self.parse_robots_txt(response.text)
                
                sitemap_links.update(links)
                
        except Exception as e:
            self.logger.debug(f"站点地图 {sitemap_url} 失败: {e}")
    
    return sitemap_links
```

### 第四步：性能优化

#### 4.1 分阶段并发策略
```python
class OptimizedCrawler(BaseCrawler):
    """优化的爬虫类"""
    
    def __init__(self):
        super().__init__()
        self.selenium_pool = ThreadPoolExecutor(max_workers=1)  # Selenium单线程
        self.requests_pool = ThreadPoolExecutor(max_workers=5)  # 请求多线程
    
    def run_optimized(self, max_pages=None):
        """优化的运行策略"""
        
        # 阶段1：链接发现（Selenium单线程）
        self.logger.info("阶段1：开始链接发现")
        with self.selenium_pool:
            all_links = self.discover_all_links_comprehensive()
        
        # 阶段2：内容抓取（多线程requests）
        self.logger.info("阶段2：开始内容抓取")
        self.crawl_content_parallel(all_links, max_pages)
    
    def crawl_content_parallel(self, urls, max_pages=None):
        """并行内容抓取"""
        
        if max_pages:
            urls = urls[:max_pages]
        
        with self.requests_pool as executor:
            # 提交所有任务
            futures = {
                executor.submit(self.crawl_single_page_requests, url): url 
                for url in urls
            }
            
            # 处理结果
            with tqdm(total=len(futures), desc="抓取进度") as pbar:
                for future in as_completed(futures):
                    url = futures[future]
                    try:
                        result = future.result()
                        if result:
                            self.stats['total_crawled'] += 1
                        else:
                            self.stats['total_failed'] += 1
                    except Exception as e:
                        self.logger.error(f"抓取失败 {url}: {e}")
                        self.stats['total_failed'] += 1
                    
                    pbar.update(1)
                    pbar.set_postfix({
                        '成功': self.stats['total_crawled'],
                        '失败': self.stats['total_failed']
                    })
    
    def crawl_single_page_requests(self, url):
        """使用requests抓取单页面（用于并行）"""
        try:
            # 使用requests而不是selenium进行内容抓取
            success, html_content = self.fetch_page_requests(url)
            
            if not success:
                self.url_manager.mark_failed(url, html_content)
                return False
            
            content_info = self.extract_content(html_content, url)
            save_success = self.save_content(content_info)
            
            if save_success:
                self.url_manager.mark_completed(url)
            else:
                self.url_manager.mark_failed(url, "保存文件失败")
            
            return save_success
            
        except Exception as e:
            self.logger.error(f"抓取页面失败: {url} - {str(e)}")
            self.url_manager.mark_failed(url, str(e))
            return False
```

### 第五步：配置管理优化

#### 5.1 增强配置系统
```python
# config_enhanced.py
class CrawlerConfig:
    """增强的配置管理类"""
    
    # 网站配置
    SITE = {
        'base_url': 'https://qian.tencent.com/document',
        'start_url': 'https://qian.tencent.com/document/53799',
        'allowed_domains': ['qian.tencent.com'],
        'allowed_paths': ['/document']
    }
    
    # Selenium配置
    SELENIUM = {
        'headless': True,
        'window_size': (1920, 1080),
        'page_load_timeout': 30,
        'implicit_wait': 10,
        'expand_selectors': [
            '.menu__list-item--collapsed .menu__link--sublist-caret',
            '[aria-expanded="false"]',
            '.collapsed .expand-button',
            '.sidebar-item.collapsed',
            '[class*="collapsed"]'
        ],
        'max_expand_attempts': 5,
        'expand_wait_time': 2
    }
    
    # 内容提取配置
    EXTRACTION = {
        'title_selectors': [
            'h1',
            '.theme-doc-markdown h1', 
            'title',
            '.docTitle_node_modules',
            '[data-testid="doc-title"]'
        ],
        'content_selectors': [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR',
            'main .container'
        ],
        'remove_selectors': [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb',
            '.pagination', '.feedback', '.advertisement'
        ]
    }
    
    # 性能配置
    PERFORMANCE = {
        'selenium_workers': 1,
        'requests_workers': 5,
        'request_timeout': 30,
        'retry_times': 3,
        'delay_min': 1,
        'delay_max': 3
    }
```

## 实施步骤

### 步骤1：创建基础类（1-2天）
1. 创建`base_crawler.py`
2. 提取通用方法到基础类
3. 测试基础功能

### 步骤2：重构现有类（2-3天）
1. 修改三个爬虫类继承基础类
2. 移除重复代码
3. 修复函数命名问题
4. 测试重构后功能

### 步骤3：增强Selenium功能（3-4天）
1. 实现智能菜单展开
2. 添加多策略链接发现
3. 优化等待机制
4. 测试链接发现效果

### 步骤4：性能优化（2-3天）
1. 实现分阶段并发
2. 优化内存使用
3. 添加缓存机制
4. 性能测试

### 步骤5：配置优化（1天）
1. 重构配置系统
2. 统一配置管理
3. 添加配置验证

## 预期效果

### 代码质量
- 减少重复代码60%以上
- 提高可维护性
- 统一编码规范

### 功能提升
- 链接发现能力：从20个提升到200+个
- 抓取成功率：从38%提升到90%以上
- 处理速度：提升3-5倍

### 稳定性
- 减少因重复代码导致的bug
- 提高错误处理能力
- 增强系统健壮性

这个重构方案将显著提升项目的质量和性能，解决当前存在的主要问题。