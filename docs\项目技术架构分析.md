# 腾讯千帆文档爬虫项目技术架构分析

## 项目概述

### 目标
抓取腾讯千帆文档网站（https://qian.tencent.com/document）的所有页面，转换为Markdown格式并本地存储。

### 核心挑战
1. **JavaScript动态内容**：网站大量使用JavaScript生成链接，特别是左侧菜单
2. **链接发现完整性**：如何发现所有隐藏在折叠菜单中的链接
3. **中文编码处理**：确保文件名和内容的UTF-8编码正确性
4. **状态管理**：支持断点续传和进度跟踪

## 技术架构

### 核心技术栈
- **Python 3.12**：主要开发语言
- **requests**：HTTP请求库
- **BeautifulSoup4**：HTML解析
- **html2text**：HTML到Markdown转换
- **jieba**：中文分词，用于智能文件命名
- **selenium**：JavaScript渲染和动态内容获取
- **tqdm**：进度条显示
- **lxml**：高性能XML/HTML解析器

### 架构设计

#### 1. 多层次爬虫架构
```
基础版 (crawler.py)
    ↓
增强版 (enhanced_crawler.py)
    ↓
Selenium版 (selenium_crawler.py)
```

**基础版特点**：
- 使用requests进行HTTP请求
- 基础的HTML解析和链接提取
- 适合静态内容抓取
- 发现链接数：约8个

**增强版特点**：
- 改进的链接发现算法
- 多样本页面递归链接提取
- 更智能的侧边栏解析
- 发现链接数：约20个

**Selenium版特点**：
- 支持JavaScript渲染
- 自动展开折叠菜单
- 完整的动态内容获取
- 预期发现链接数：50+个

#### 2. 模块化设计

```
py_qian/
├── 核心模块
│   ├── crawler.py              # 基础爬虫类
│   ├── enhanced_crawler.py     # 增强版爬虫类
│   ├── selenium_crawler.py     # Selenium版爬虫类
│   └── url_manager.py          # URL状态管理
├── 工具模块
│   ├── utils.py                # 工具函数
│   └── config.py               # 配置管理
├── 主程序
│   ├── main.py                 # 基础版主程序
│   ├── enhanced_main.py        # 增强版主程序
│   └── selenium_main.py        # Selenium版主程序
└── 数据存储
    ├── download/               # 下载文件目录
    └── docs/                   # 文档目录
```

#### 3. URL管理系统

**URLManager类**负责：
- URL收集和去重
- 状态跟踪（待处理、已完成、失败）
- 断点续传支持
- 统计信息生成

**状态文件**：
- `all_urls.txt`：所有发现的URL
- `completed_urls.txt`：已完成的URL
- `failed_urls.txt`：失败的URL
- `url_metadata.json`：详细元数据

#### 4. 智能文件命名系统

**命名策略**（优先级递减）：
1. 页面标题直接使用
2. jieba分词提取关键词
3. URL路径解析
4. MD5哈希值备用

**特点**：
- 支持中文文件名
- 自动去重和唯一性保证
- UTF-8编码兼容性

## 发现的问题

### 1. 代码重复性问题

**问题描述**：三个爬虫类存在大量重复代码

**具体表现**：
- `extract_title()`、`extract_main_content()`、`clean_content()`等方法在三个类中几乎相同
- HTML到Markdown转换逻辑重复
- 会话设置和日志配置重复

**影响**：
- 代码维护困难
- 修改需要同步多个文件
- 增加了bug风险

### 2. 函数命名不一致

**问题描述**：不同模块间函数调用不匹配

**具体表现**：
- `selenium_crawler.py`第467行调用`generate_filename()`
- 但`utils.py`中实际函数名是`generate_filename_from_content()`
- 参数数量不匹配：前者2个参数，后者3个参数

**后果**：
- 导致13个URL保存失败
- 错误信息："name 'generate_filename' is not defined"

### 3. JavaScript内容获取不完整

**问题描述**：左侧菜单大量使用JavaScript动态生成

**具体表现**：
- 普通HTTP请求无法获取折叠菜单中的链接
- 基础版只能发现8个链接，实际网站有200+页面
- 增强版通过多样本提取提升到20个，但仍不完整

**技术原因**：
- 菜单项通过JavaScript动态加载
- 折叠状态需要用户交互才能展开
- AJAX请求获取子菜单内容

### 4. 配置管理分散

**问题描述**：配置信息分散在多个文件中

**具体表现**：
- 请求参数、内容提取规则、文件配置等分散
- 硬编码的选择器和参数
- 缺乏统一的配置管理

## 解决方案

### 1. 代码重构方案

#### 1.1 创建基础爬虫类
```python
class BaseCrawler:
    """基础爬虫类，包含通用功能"""
    
    def __init__(self):
        self.setup_session()
        self.setup_html2text()
        self.setup_logging()
    
    def extract_title(self, soup):
        """通用标题提取"""
        pass
    
    def extract_main_content(self, soup):
        """通用内容提取"""
        pass
    
    def clean_content(self, content_element):
        """通用内容清理"""
        pass
    
    def convert_to_markdown(self, html_content):
        """通用Markdown转换"""
        pass
```

#### 1.2 继承重构
```python
class TencentQianCrawler(BaseCrawler):
    """基础版爬虫"""
    pass

class EnhancedTencentQianCrawler(BaseCrawler):
    """增强版爬虫"""
    pass

class SeleniumTencentQianCrawler(BaseCrawler):
    """Selenium版爬虫"""
    pass
```

### 2. JavaScript内容获取方案

#### 2.1 Selenium自动化策略
```python
def expand_all_menus_enhanced(self, max_attempts=5):
    """增强的菜单展开策略"""
    
    # 1. 等待页面完全加载
    self.wait_for_page_load()
    
    # 2. 多种选择器策略
    selectors = [
        '.menu__list-item--collapsed .menu__link--sublist-caret',
        '[aria-expanded="false"]',
        '.collapsed .expand-button',
        '.sidebar-item.collapsed'
    ]
    
    # 3. 递归展开策略
    for attempt in range(max_attempts):
        expanded_any = False
        for selector in selectors:
            if self.expand_by_selector(selector):
                expanded_any = True
        
        if not expanded_any:
            break
            
        # 4. 等待动态内容加载
        self.wait_for_dynamic_content()
    
    # 5. 滚动页面确保所有内容可见
    self.scroll_to_load_all()
```

#### 2.2 混合策略
```python
class HybridCrawler:
    """混合策略爬虫"""
    
    def discover_links(self):
        # 1. Selenium获取完整菜单结构
        selenium_links = self.selenium_discover()
        
        # 2. API接口探测（如果存在）
        api_links = self.api_discover()
        
        # 3. 站点地图解析
        sitemap_links = self.sitemap_discover()
        
        # 4. 合并去重
        all_links = self.merge_and_dedupe(
            selenium_links, api_links, sitemap_links
        )
        
        return all_links
```

#### 2.3 智能等待策略
```python
def smart_wait_for_content(self, timeout=30):
    """智能等待内容加载"""
    
    # 等待基础DOM
    WebDriverWait(self.driver, timeout).until(
        EC.presence_of_element_located((By.TAG_NAME, "body"))
    )
    
    # 等待侧边栏
    WebDriverWait(self.driver, timeout).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".sidebar, .menu"))
    )
    
    # 等待JavaScript执行完成
    WebDriverWait(self.driver, timeout).until(
        lambda driver: driver.execute_script("return document.readyState") == "complete"
    )
    
    # 等待AJAX请求完成
    WebDriverWait(self.driver, timeout).until(
        lambda driver: driver.execute_script("return jQuery.active == 0") if self.has_jquery() else True
    )
```

### 3. 性能优化方案

#### 3.1 并发策略优化
```python
class OptimizedCrawler:
    def __init__(self):
        self.link_discovery_pool = ThreadPoolExecutor(max_workers=1)  # Selenium单线程
        self.content_crawl_pool = ThreadPoolExecutor(max_workers=5)   # 内容抓取多线程
    
    def run_optimized(self):
        # 阶段1：链接发现（Selenium单线程）
        with self.link_discovery_pool:
            all_links = self.discover_all_links()
        
        # 阶段2：内容抓取（多线程requests）
        with self.content_crawl_pool:
            self.crawl_content_parallel(all_links)
```

#### 3.2 缓存策略
```python
class CachedCrawler:
    def __init__(self):
        self.page_cache = {}
        self.link_cache = {}
    
    def fetch_with_cache(self, url):
        if url in self.page_cache:
            return self.page_cache[url]
        
        content = self.fetch_page(url)
        self.page_cache[url] = content
        return content
```

### 4. 配置管理优化

#### 4.1 统一配置文件
```python
# config.py 增强版
class CrawlerConfig:
    """爬虫配置类"""
    
    # 网站配置
    SITE_CONFIG = {
        'base_url': 'https://qian.tencent.com/document',
        'start_url': 'https://qian.tencent.com/document/53799',
        'allowed_domains': ['qian.tencent.com'],
        'allowed_paths': ['/document']
    }
    
    # Selenium配置
    SELENIUM_CONFIG = {
        'headless': True,
        'window_size': (1920, 1080),
        'page_load_timeout': 30,
        'implicit_wait': 10,
        'menu_expand_selectors': [
            '.menu__list-item--collapsed .menu__link--sublist-caret',
            '[aria-expanded="false"]',
            '.collapsed .expand-button'
        ]
    }
    
    # 内容提取配置
    EXTRACTION_CONFIG = {
        'title_selectors': [
            'h1',
            '.theme-doc-markdown h1',
            'title',
            '.docTitle_node_modules'
        ],
        'content_selectors': [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR'
        ],
        'remove_selectors': [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb'
        ]
    }
```

### 5. 错误处理增强

#### 5.1 分层错误处理
```python
class ErrorHandler:
    """错误处理器"""
    
    def handle_selenium_error(self, error, url):
        """处理Selenium相关错误"""
        if "timeout" in str(error).lower():
            return self.retry_with_longer_timeout(url)
        elif "element not found" in str(error).lower():
            return self.try_alternative_selectors(url)
        else:
            return self.fallback_to_requests(url)
    
    def handle_network_error(self, error, url):
        """处理网络错误"""
        if error.response and error.response.status_code == 429:
            return self.handle_rate_limit(url)
        else:
            return self.retry_with_backoff(url)
```

## 实施建议

### 阶段1：代码重构（优先级：高）
1. 创建BaseCrawler基类
2. 重构三个爬虫类继承基类
3. 修复函数命名不一致问题
4. 统一配置管理

### 阶段2：JavaScript处理优化（优先级：高）
1. 增强Selenium菜单展开策略
2. 实现智能等待机制
3. 添加多种选择器备用方案
4. 实现混合发现策略

### 阶段3：性能优化（优先级：中）
1. 实现分阶段并发策略
2. 添加缓存机制
3. 优化内存使用
4. 添加进度恢复机制

### 阶段4：功能扩展（优先级：低）
1. 添加增量更新功能
2. 实现内容去重
3. 添加图形界面
4. 支持多站点配置

## 预期效果

### 链接发现能力提升
- 当前：20个链接（增强版）
- 目标：200+个链接（优化后Selenium版）
- 提升：10倍以上

### 代码质量提升
- 减少重复代码60%以上
- 提高可维护性
- 降低bug风险

### 性能提升
- 链接发现阶段：保持现有速度
- 内容抓取阶段：提升3-5倍速度
- 整体完成时间：减少50%

## 总结

通过系统性的重构和优化，可以将项目从当前的"能用"状态提升到"好用"状态。重点解决JavaScript内容获取问题，同时提升代码质量和性能，为后续功能扩展打下坚实基础。