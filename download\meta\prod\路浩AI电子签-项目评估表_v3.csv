"分类","一级模块","二级模块","三级功能/具体任务","涉及岗位","产出物","工作量 (人天)","单价 (元/人天)","报价 (元)","备注"
"Part 1: 核心平台研发",,,,,,,,,"2,130,000"
,"平台基础","项目管理与设计","产品需求池(PRD)与原型(UI/UX)持续迭代、项目排期管理","产品经理,UI/UX设计师","PRD文档,高保真原型图,项目排期表","60","1500","90000","覆盖整个项目周期的持续性工作"
,,"技术基建","基于K8s的生产/测试环境搭建,CI/CD流水线(Jenkins/ArgoCD),监控告警(Prometheus),日志系统(EFK)","后端工程师,运维工程师(SRE)","环境部署脚本,CI/CD配置文件,监控大盘","40","2000","80000","项目启动初期的核心基建工作"
,"账户与认证中心","个人账户体系","多渠道注册/登录(手机/微信),统一实名认证(多证件),账户管理(更名/换绑/注销),个人签名/印章管理","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","40","1800","72000","涉及与第三方实名认证服务对接"
,,"企业账户体系","多通道企业认证(法人/打款/商户号),超管/法人体系,企业信息管理","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","45","1800","81000","企业认证流程复杂，需重点测试"
,"组织与权限(RBAC)","组织架构管理","无限层级部门管理,多方式员工管理(导入/邀请/同步),员工离职与交接","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","50","1800","90000",""
,,"角色权限体系","系统预设角色,自定义角色,操作权限+数据权限(企业/部门/个人)精细化配置","后端工程师,前端工程师,测试工程师","API接口,权限配置页面,测试用例","60","1800","108000","权限系统是B端核心，设计复杂"
,"印章中心","印章生命周期管理","多类型印章创建(模板/上传),印章授权,用印审批流,用印日志,启停用/删除","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","50","1800","90000",""
,"合同生命周期管理","合同拟定与发起","多源发起(模板/文件/草稿),在线协同编辑(多人/版本/评论),动态填写控件","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","70","1800","126000","在线协同编辑技术复杂度高"
,,"签署流程与执行","灵活签署流程(顺序/无序/混合),多重意愿认证配置,自动签署(本方),批量签署,拒签/撤销/转交","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","80","1800","144000","流程引擎状态机设计复杂"
,,"合同归档与管理","智能归档与分类,多维度智能检索,全链路操作日志视图,合同后处理(出证/解除)","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","60","1800","108000","需要与Elasticsearch深度集成"
,"高级功能与集成","集团企业解决方案","集团组织构建,集中管控(合同/印章/模板),资源共享","后端工程师,前端工程师,测试工程师","API接口,前端页面,测试用例","70","1800","126000",""
,,"场景化签约方案","一码多签,视频会议签插件开发,战略会议签方案","后端工程师,前端工程师,测试工程师","API接口,前端页面/插件,测试用例","60","1800","108000","会议签涉及与第三方会议软件集成"
,,"开放平台(API/SDK)","API Gateway配置,全功能API文档编写,多语言SDK开发,Webhook回调机制","后端工程师,技术文档工程师","API文档,SDK包,Webhook服务","50","1800","90000",""
"Part 2: AI能力研发与部署",,,,,,,,,"1,027,500"
,"AI模型层","大模型选型与评估","评估多种大模型(Deepseek, qwen, GLM等)在合同领域的综合效果、性能及成本","AI算法工程师,产品经理","模型评估报告","25","2500","62500",""
,,"知识库构建","法律法规/合同范本/行业数据清洗、切分、向量化(使用BGE等模型),构建向量数据库(Milvus)","AI算法工程师,数据工程师","向量数据库,数据处理脚本","40","2500","100000","高质量知识库是RAG效果的基础"
,,"模型部署与调优","大模型私有化部署,推理优化(vLLM/TGI),Prompt Engineering,模型微调(Fine-tuning)","AI算法工程师,运维工程师(SRE)","部署方案,优化后的模型服务,Prompt模板库","50","2500","125000",""
,"AI服务层","智能合同生成服务","提供智能问答生成合同、条款库推荐、风险审查与提示等能力的API服务","AI算法工程师,后端工程师,测试工程师","ai-generation-service,API接口,测试用例","80","2200","176000",""
,,"智能抠图与识别服务","提供电子章智能抠图、文档关键信息OCR识别等能力的API服务","AI算法工程师,后端工程师,测试工程师","ai-ocr-service,API接口,测试用例","60","2200","132000",""
"Part 3: 第三方服务与硬件成本 (年预估)",,,,,,,,,""
,"第三方服务费用",,"CA/时间戳服务","按签署份数或按年采购数字证书和时间戳服务","采购","","","100000","核心安全成本，必须采购"
,,,"短信/邮件服务","用于发送验证码、业务通知等","采购","","","50000","按量付费，预估值"
,,,"大模型API调用","若部分非核心场景使用公有云大模型API的费用","采购","","","100000","作为私有化模型的补充，按量付费"
,,,"工商信息查询","用于企业认证时的信息核验","采购","","","30000","按次调用，预估值"
,"硬件成本(本地部署)",,"AI服务器(训练+推理)","方案2: H200方案, 1台 (NVIDIA H200 * 8)","采购","","","2,926,000","参考截图方案，兼顾训练和推理"
,,,"应用服务器集群","用于部署微服务、数据库、中间件等，预估需要10台高性能服务器","采购","","","500000","按高可用标准配置"
,,,"网络设备","交换机、防火墙、负载均衡器等","采购","","","200000",""
"总计",,,,,,"","","6,993,500",