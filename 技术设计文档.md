# 腾讯千帆文档抓取器 - 技术设计文档

## 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Web Crawler System                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   URL管理   │  │   内容抓取   │  │   内容处理   │        │
│  │  URLManager │◄─┤ WebCrawler  ├─►│ContentParser│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   进度管理   │  │   异常处理   │  │ Markdown转换 │        │
│  │ProgressMgr  │  │ ErrorHandler│  │ MDConverter │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                            │                │
│                                            ▼                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   配置管理   │  │   日志系统   │  │   文件管理   │        │
│  │ConfigManager│  │ LogManager  │  │ FileManager │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块设计

### 1. WebCrawler 主爬虫类

**职责**: 负责网页请求、HTML获取、基础处理

```python
class WebCrawler:
    def __init__(self, base_url, config):
        self.base_url = base_url
        self.config = config
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """配置请求会话"""
        pass
    
    def fetch_page(self, url):
        """获取网页内容"""
        pass
    
    def is_valid_url(self, url):
        """验证URL是否符合爬取范围"""
        pass
```

**关键特性**:
- 请求重试机制
- User-Agent轮换
- Cookie管理
- 超时控制

### 2. URLManager URL队列管理器

**职责**: 管理待爬取和已爬取的URL队列

```python
class URLManager:
    def __init__(self):
        self.to_crawl = set()  # 待爬取
        self.crawled = set()   # 已爬取
        self.failed = set()    # 失败的
    
    def add_url(self, url):
        """添加URL到队列"""
        pass
    
    def get_next_url(self):
        """获取下一个待爬取URL"""
        pass
    
    def mark_crawled(self, url):
        """标记为已爬取"""
        pass
```

### 3. ContentExtractor 内容提取器

**职责**: 从HTML中提取主要内容，清理无关元素

```python
class ContentExtractor:
    def __init__(self):
        self.soup_parser = 'lxml'
    
    def extract_main_content(self, html):
        """提取主要内容区域"""
        pass
    
    def extract_title(self, soup):
        """提取页面标题"""
        pass
    
    def extract_links(self, soup, base_url):
        """提取页面中的链接"""
        pass
    
    def clean_html(self, soup):
        """清理HTML，移除无关元素"""
        pass
```

**内容提取策略**:
- 基于CSS选择器定位主内容区
- 移除导航、广告、脚本等无关元素
- 保留文档结构（标题、列表、表格等）

### 4. MarkdownConverter Markdown转换器

**职责**: 将清理后的HTML转换为Markdown格式

```python
class MarkdownConverter:
    def __init__(self):
        self.html2text = html2text.HTML2Text()
        self.setup_converter()
    
    def setup_converter(self):
        """配置转换器参数"""
        self.html2text.ignore_links = False
        self.html2text.ignore_images = False
        self.html2text.body_width = 0
    
    def convert(self, html_content):
        """转换HTML为Markdown"""
        pass
    
    def post_process(self, markdown_text):
        """后处理，优化格式"""
        pass
```

### 5. FileManager 文件管理器

**职责**: 管理文件命名、存储、目录结构

```python
class FileManager:
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.ensure_dir_exists()
    
    def generate_filename(self, title, content):
        """生成中文文件名"""
        pass
    
    def save_markdown(self, content, filename):
        """保存Markdown文件"""
        pass
    
    def check_file_exists(self, filename):
        """检查文件是否已存在"""
        pass
```

**文件命名策略**:
1. 提取页面主标题
2. 如标题过长，截取关键词
3. 处理特殊字符，确保文件名合法
4. 添加序号避免重名

## 数据流程

### 爬取流程
```
开始 → 初始化URL队列 → 获取下一个URL → 发送HTTP请求
   ↓
响应成功? → 否 → 记录失败 → 重试机制 → 放弃或重试
   ↓ 是
提取页面内容 → 提取新链接 → 添加到URL队列
   ↓
转换为Markdown → 生成文件名 → 保存到本地
   ↓
更新进度 → 队列为空? → 否 → 返回获取下一个URL
   ↓ 是
完成
```

### 错误处理流程
```
异常发生 → 判断异常类型
   ├─ 网络异常 → 等待重试
   ├─ 解析异常 → 记录日志，跳过
   ├─ 文件异常 → 检查权限，重试
   └─ 其他异常 → 记录详细信息
```

## 配置管理

### 配置文件结构
```python
CONFIG = {
    'base_url': 'https://qian.tencent.com/document',
    'start_url': 'https://qian.tencent.com/document/53799',
    'output_dir': './download',
    'request': {
        'timeout': 30,
        'retry_times': 3,
        'delay': 1,  # 请求间隔秒数
        'user_agents': [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
            # 更多User-Agent
        ]
    },
    'content': {
        'main_content_selector': '.main-content, article, .document-content',
        'title_selector': 'h1, .page-title, title',
        'remove_selectors': ['.navigation', '.sidebar', '.footer', '.ads']
    },
    'file': {
        'max_filename_length': 50,
        'forbidden_chars': ['<', '>', ':', '"', '|', '?', '*', '/']
    }
}
```

## 性能优化策略

### 1. 并发控制
- 使用线程池或异步IO
- 限制并发数量，避免对服务器造成压力
- 智能调节请求频率

### 2. 缓存机制
- 已爬取URL缓存
- 页面内容缓存（可选）
- 失败URL记录

### 3. 内存管理
- 及时释放大对象
- 使用生成器处理大数据集
- 分批处理避免内存溢出

## 安全性考虑

### 1. 请求安全
- 随机化请求间隔
- 模拟真实浏览器行为
- 避免过于频繁的请求

### 2. 数据安全
- 文件名安全性检查
- 路径遍历攻击防护
- 编码安全处理

## 监控与日志

### 日志级别
- DEBUG: 详细的调试信息
- INFO: 一般信息（开始/完成/进度）
- WARNING: 警告信息（重试、跳过）
- ERROR: 错误信息（失败、异常）

### 监控指标
- 总页面数量
- 已处理页面数
- 成功/失败比率
- 平均处理时间
- 文件存储统计

## 扩展性设计

### 插件机制
支持自定义处理器：
- 内容提取器插件
- 文件命名插件
- 格式转换插件

### 配置化设计
- 可配置的CSS选择器
- 可配置的处理规则
- 可配置的输出格式

这个设计确保了系统的可维护性、可扩展性和稳定性。