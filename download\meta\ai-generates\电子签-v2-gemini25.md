# 在线电子签产品架构设计文档

## 一、文档概述

### 1.1 文档目的

本文件旨在清晰、全面地梳理“路浩AI电子签”产品的整体设计框架与体系结构。文档整合并定义了产品的愿景定位、目标用户、核心功能模块、关键业务流程、权限与角色模型、版本演进规划以及后台运营支持策略。其核心目标是为产品、设计、研发、测试、运营及市场团队提供一份统一、详尽的行动纲领和参考基准，确保各方对产品有深入且一致的理解，从而支持跨团队的高效协同与高质量的产品交付。

### 1.2 面向读者

  - **产品团队**: 用于明确产品需求边界、功能优先级和迭代方向。
  - **UI/UX设计团队**: 作为设计输入，理解用户场景和功能逻辑，进行界面与交互设计。
  - **研发与测试团队**: 作为需求规格说明，用于技术架构设计、开发排期和测试用例编写。
  - **项目管理与运营团队**: 用于了解产品全貌，制定项目计划、运营策略和市场推广方案。
  - **法务/合规相关参与方**: 用于评估产品在法律合规性方面的设计与保障措施。

-----

## 二、产品背景与目标

### 2.1 产品愿景

致力于成为中国市场领先的、以AI技术为核心驱动的智能合同全生命周期管理平台。我们的目标是推动传统合同管理模式的变革，将繁琐的事务性工作转变为企业的数据资产与智能决策引擎，赋能每一个组织实现更高效、更安全、更智能的商业协作，最终成为政企与专业服务机构（如律所、知识产权机构）的首选签署平台。

### 2.2 产品定位

“路浩AI电子签”并非单纯的电子签名工具，而是一个融合了**合规签署**、**智能管理**与**业务赋能**三位一体的企业级SaaS解决方案。我们以金融级的安全合规电子签名为基石，将AI能力深度渗透到合同起草、审查、签署、履约、归档、检索的每一个环节，立志成为企业数字化转型战略中不可或缺的信任基础设施。

  - **目标用户群**：

      - **企业客户**: 从初创公司到大型集团，是平台的核心服务对象。需求多样，覆盖合同全生命周期管理，注重效率、成本、合规风控、组织权限管理和业务系统集成。
      - **法律/专利机构**: 律师、法务、专利代理人。对合同/文件的严谨性、合规性、版本追溯与检索效率要求极高。
      - **政务用户**: 政府机关、事业单位等，对安全、合规、审计和国产化（信创）有极高要求。
      - **个人用户**: C端普通用户，如自由职业者、租赁双方、C2C交易者等，需要快速、便捷、低成本地签署具有法律效力的个人文件。

  - **使用场景**：

      - **人力资源**: 雇佣合同、保密协议、入/离职证明的批量签署。
      - **商务采购**: 采购合同、销售合同、代理协议、框架协议的快速签署与流转。
      - **法务合规**: 知识产权转让协议、授权书、股东决议、法律文件的严谨签署与证据保全。
      - **金融服务**: 贷款合同、保险保单、理财协议的安全签署。
      - **个人事务**: 租房合同、借条、收据、个人声明的便捷签署。
      - **政务服务**: 电子政务公文流转、行政审批、与民众或企业间的各类电子凭证、证明、协议的签署与管理。

  - **核心价值**：

      - **极致效率 (Efficiency)**: 将数天甚至数周的合同流转周期缩短至几分钟，通过AI生成、批量发起、自动化流程等功能，显著提升商业成交速度与组织运营效率。
      - **绝对安全 (Security)**: 提供符合《电子签名法》及国密标准的全链路安全保障，通过PKI体系、区块链存证、多重身份认证和加密存储，确保每一份合同的法律效力与数据安全。
      - **深度智能 (Intelligence)**: 利用AI技术降低合同风险、挖掘数据价值。实现智能合同生成、风险审查、关键信息提取和自然语言检索，提供前所未有的合同洞察力，辅助企业决策。
      - **无缝集成 (Integration)**: 通过开放的全功能API和嵌入式组件（Embed SDK），将电子签能力无缝融入企业现有的ERP、CRM、HRM、OA等业务系统，打破数据孤岛，实现业务流程闭环。

-----

## 三、用户与角色分析

### 3.1 用户角色定义

平台根据用户在业务流程中的职责与权限范围，定义了以下核心角色：

| 角色分类 | 角色名称 | 简介与核心职责 | 典型权限范围 |
| :--- | :--- | :--- | :--- |
| **外部用户** | **个人用户** | 作为独立的法律主体，使用平台服务。**职责**: 签署个人相关的合同、协议、文件，并进行管理。 | 拥有个人账户管理、实名认证、个人签名/印章管理、发起/签署个人合同、管理个人合同等能力。 |
| | **签署人 (Signatory)** | 代表个人或企业，完成指定合同的签署操作。是临时的、任务性的角色。**职责**: 及时、准确地完成签署任务。 | 仅限于查看和签署被指定的合同，无法访问平台其他功能。 |
| **企业内部用户** | **超级管理员 (Super Admin)** | 企业平台的最高权限所有者。**职责**: 负责企业认证、购买服务、初始化配置、分配系统级管理员等。 | 拥有平台所有管理权限，是企业安全的最后一道防线。 |
| | **企业管理员 (Admin)** | 负责企业的日常运营管理。**职责**: 维护组织架构、管理员工账号、分配角色与权限。 | 由超管授予，通常管理除"超管设置"外的所有功能。 |
| | **法务人员 (Legal)** | 负责企业合同的合规性审查与风险控制。**职责**: 制定与管理标准合同模板、配置审批流、处理合同纠纷与出证申请。 | 通常被授予查看所有合同、管理合同模板、配置审批流、处理出证申请等权限。 |
| | **财务人员 (Finance)** | 负责企业的费用与支付管理。**职责**: 管理费用中心、申请与核销发票、进行对公打款认证，并常作为高金额合同审批流中的关键节点。 | 拥有费用中心访问权限，通常作为高金额合同审批流中的关键节点。 |
| | **业务人员 (Employee)** | 企业内业务的执行者。**职责**: 在被授予的权限内发起和管理与自身业务相关的合同，如销售、采购等。 | 权限范围由其角色决定，如发起合同、申请用印、查看自己参与的合同等。 |
| **政务用户** | **机构管理员** | 负责政务机构的组织与人员管理。**职责**: 管理机构内部的组织树、人员账号，配置公文流转规则，管理机构电子签章。 | 类似于企业管理员，但更侧重于公文模板、签章管理和流程的合规性。 |
| **平台后台** | **运营后台人员** | 路浩AI电子签平台的内部运营和支持人员。**职责**: 管理官方模板市场、处理用户申诉、审核特殊认证、监控平台运行状态等。 | 不接触客户业务数据，仅进行平台级运营与维护。 |

### 3.2 用户需求与行为目标

  - **发起人 (通常为业务人员、法务人员)**：
      - **需求**: 快速、准确地创建并发出合同。能够方便地追踪合同状态，避免流程延误。对于标准化合同，希望能够一键批量发起，减少重复劳动。
      - **行为目标**: 尽可能缩短从“合同拟定”到“发送给首个签署人”的时间。在合同发出后，能实时看到每个签署人的状态（已查看、已签署、已拒签）。
  - **签署人 (个人用户或企业代表)**：
      - **需求**: 签署过程必须简单明了，在任何设备上（尤其是手机）都能轻松操作。要确保签署过程和结果具有法律效力，个人信息安全有保障。
      - **行为目标**: 在收到通知后，通过不超过3-4步核心操作（点击链接 -\> 验证身份 -\> 签名/盖章 -\> 确认）即可完成签署。
  - **管理员 (超级管理员、企业管理员)**：
      - **需求**: 对企业内的员工、权限、印章有绝对的控制力。能够灵活配置组织架构和审批流程，以适应公司管理制度。所有敏感操作必须有日志可查，以便审计和追责。
      - **行为目标**: 能够通过图形化界面轻松调整部门结构、增删员工。能够自定义角色，并精细化地勾选权限。能够随时调取用印日志和合同操作日志。
  - **审核员 (法务、财务、部门负责人)**：
      - **需求**: 在合同正式发出前，能方便地审查合同内容，确保其合规性、准确性。希望系统能辅助识别风险。
      - **行为目标**: 在待办列表中清晰看到待审任务，点击后可直接预览合同全文，并能看到AI高亮提示的风险点，一键“批准”或“驳回”并填写意见。

-----

## 四、产品功能架构图

### 4.1 功能结构图

产品采用分层解耦的架构，确保职责清晰，易于扩展。总体功能结构如下：

```mermaid
graph TD
    subgraph System [路浩AI电子签]
        direction TB

        subgraph Layer_System [1. 平台基础支撑系统]
            direction LR
            M1_1[统一账户中心]
            M1_2[组织与权限中心]
        end

        subgraph Layer_Lifecycle [2. 合同全生命周期系统]
            direction LR
            M2_1[合同拟定与模板]
            M2_2[签署与流转]
            M2_3[归档与管理]
        end

        subgraph Layer_Business [3. 业务支撑系统]
            direction LR
            M3_1[印章管理中心]
            M3_2[计费与消息]
            M3_3[开放平台]
        end
    end

    Layer_System --> Layer_Lifecycle
    Layer_Lifecycle --> Layer_Business

```

### 4.2 功能模块拆解

以下是各系统模块的详细功能说明：

| 一级系统 | 二级模块 | 三级功能 | 功能说明 | AI辅助 | 相关角色 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **1. 平台基础支撑系统** | **1.1 统一账户中心** | 多渠道注册/登录 | 支持用户通过"手机号+验证码"、"微信一键授权"等多种方式进行注册和登录。首次登录即自动创建账户。 | 否 | 所有用户 |
| | | 统一实名认证 | 提供多通道实名认证，包括"姓名+身份证号+人脸识别"(对接公安部/微信支付)、港澳台及护照认证，确保签署主体身份真实合法。 | 否 | 个人用户, 企业管理员 |
| | | 个人账户管理 | 用户可修改绑定的手机、邮箱，设置或重置签署密码。支持线上申请、上传证明、人脸识别等方式进行个人法定名称的变更。 | 否 | 个人用户 |
| | | 多通道企业认证 | 提供法人授权认证、对公打款认证、微信支付商户号授权、上传营业执照+授权书等多种方式，以适应不同企业。 | **是** (OCR识别营业执照) | 超级管理员 |
| | | 企业信息管理 | 展示企业名称、信用代码、认证状态。支持企业因工商变更而进行的名称、法人等信息的在线变更流程，需强验证并引导重做印章。 | 否 | 超级管理员, 法务人员 |
| | | 集团企业解决方案 | 支持创建"集团"并邀请"成员子企业"加入。集团可统一管理和共享资源（套餐、模板），并查看子公司数据。 | 否 | 超级管理员 |
| | **1.2 组织与权限中心** | 部门与员工管理 | 支持以树状结构创建和管理多层级部门。支持手动、批量导入、邀请链接等多种方式添加员工，并可对员工进行离职交接。 | 否 | 企业管理员 |
| | | 角色与权限(RBAC) | 内置"超级管理员"、"法务"、"财务"等常用角色。企业可自定义新角色，并精细勾选操作权限。 | 否 | 超级管理员, 企业管理员 |
| | | 数据权限控制 | 为角色配置数据可见范围，支持"全公司"、"本部门及子部门"、"仅本人"等多种数据隔离维度，实现最小权限原则。 | 否 | 超级管理员, 企业管理员 |
| | | 审批流引擎 | 提供图形化的审批流设计器，支持设置多级审批、会签、或签等复杂流程，并与"用印申请"、"合同发起"等场景绑定。 | 否 | 企业管理员, 法务人员 |
| **2. 合同全生命周期系统** | **2.1 合同拟定与模板** | 多源文件发起 | 支持上传本地Word/PDF/图片文件，或从企业/官方模板库选择模板来发起合同。设有草稿箱。 | 否 | 发起人, 业务人员 |
| | | AI合同生成 | **[核心]** 支持通过与AI助手进行多轮问答，快速生成一份完整的合同初稿。AI会根据对话内容，动态填充条款。 | **是** (RAG+LLM生成) | 发起人, 业务人员 |
| | | 协同编辑与评论 | 支持多人同时在线编辑一份合同草稿，修改实时同步。协作者可对具体条款进行评论和@相关人员，高效完成内部审核。 | 否 | 发起人, 法务人员, 业务人员 |
| | | AI合同审查 | **[核心]** 上传或编辑合同时，AI实时审查文本，识别潜在风险(如缺少关键条款、权利义务不对等)，并以高亮和批注形式提示。 | **是** (风险识别/条款分析) | 发起人, 法务人员 |
| | | 模板制作与配置 | 管理员可将定稿的合同文件制作成模板，通过拖拽方式添加动态填写控件，并预设签署流程和参与方角色。 | **是** (智能识别控件位置) | 管理员, 法务人员 |
| | **2.2 签署与流转** | 灵活的签署顺序 | 支持无序、顺序、混合签署流程。发起人可通过拖拽方式灵活调整签署方的顺序。 | 否 | 发起人 |
| | | 自动签署(本方) | 可配置本企业在满足特定条件(如其他方签署完毕)后，使用预设印章自动完成签署，极大提升效率。 | 否 | 发起人, 管理员 |
| | | 多重意愿认证 | 发起人可为签署方独立设置验证方式，包括人脸识别、签署密码、短信验证码等，以平衡安全与便捷。 | 否 | 发起人, 签署人 |
| | | 批量签署 | 对于同一签署人有多份待签合同时，支持一键批量签署。只需一次身份验证，即可完成所有合同的签署。 | 否 | 签署人 |
| | | 一码多签 | 针对标准协议(如入职登记表)，可生成一个固定的签署二维码。任何人扫码后即可发起一份新协议并完成签署。 | 否 | 发起人, HR |
| | | 特色签约方案 | 提供视频会议签（边谈边签）、战略会议签（仪式感签约）等场景化解决方案。 | 否 | 发起人, 业务人员 |
| | **2.3 归档与管理** | 智能归档与分类 | 已完成合同自动归档至档案库。支持自定义合同类型和标签，并可设置规则将合同自动分类。 | **是** (AI自动提取标签/分类) | 所有企业用户 |
| | | 多维度智能检索 | 支持关键字全文检索，并提供按合同类型、金额、签署方、日期等多维度筛选。 | **是** (支持自然语言搜索) | 所有企业用户 |
| | | 合同全链路视图 | 完整记录合同从创建到归档的所有操作日志，形成不可篡改的证据链。支持关联主合同与补充协议。 | 否 | 管理员, 法务人员 |
| | | 履约管理 | 用户可在合同中设置关键履约节点(如付款日、交付日)，系统会在到期前通过消息中心自动发送提醒。 | **是** (AI自动识别履约节点) | 业务人员, 财务人员 |
| | | 证据链报告与出证 | 可一键申请由权威CA机构出具的、具有法律效力的《电子文件签署报告》。可选将关键信息同步至区块链存证。 | 否 | 法务人员, 管理员 |
| **3. 业务支撑系统** | **3.1 印章管理中心** | 多类型印章创建 | 支持创建公章、合同章、财务章、法人章等。支持通过"标准模板生成"或"上传实体印章图片"两种方式。 | **是** (AI智能抠图/辅助真伪识别) | 管理员, 法定代表人 |
| | | 印章授权与审批 | 印章管理员可将印章使用权授权给指定员工、部门或角色。未获授权的员工需要用印时，可发起"用印申请"并流转审批。 | 否 | 管理员, 业务人员 |
| | | 用印日志 | 所有印章的使用都会被系统自动记录，形成不可篡改的审计日志，包含使用者、时间、合同、IP地址等信息。 | 否 | 管理员, 法务人员 |
| | **3.2 计费与消息** | 套餐与订单 | 提供多种规格的合同签署套餐包及增值资源包。用户可在线下单购买，支持微信、支付宝、对公转账。 | 否 | 超级管理员, 财务人员 |
| | | 发票管理 | 用户可在线提交开票申请。系统支持开具电子或纸质的增值税普通/专用发票。 | 否 | 财务人员 |
| | | 消息中心 | 统一管理所有系统通知。通过短信、邮件、微信模板消息、系统内站内信等方式，将各类消息(如待办、状态变更)触达用户。 | 否 | 所有用户 |
| | **3.3 开放平台** | API与SDK | 提供覆盖全业务流程的RESTful API和多语言SDK，支持与企业OA、ERP、CRM等系统深度集成。 | 否 | 企业管理员, 开发者 |
| | | 嵌入式组件 | 提供标准化的前端组件(Embed SDK)，可嵌入企业自有应用，实现"零跳转"的无缝签署体验。 | 否 | 开发者 |
| | | 事件回调(Webhook) | 企业可配置回调URL，当合同状态变化时(如已签署、已完成)，平台会实时向该URL推送事件消息，支持重试和签名验证。 | 否 | 开发者 |

-----

## 五、核心用户流程

### 5.1 发起签署流程（标准全流程）

此流程覆盖了一份标准合同从无到有、从草稿到法律文件的完整生命周期。

```mermaid
graph TD
    subgraph A [1. 合同拟定]
        A1[用户选择发起方式] --> A2{AI生成 / 模板发起 / 本地上传};
        A2 --> A3[在线协同编辑<br>多人修改/批注/版本管理];
    end

    subgraph B [2. 内部审批 (可选)]
        A3 --> B1{是否需要审批?};
        B1 -- 是 --> B2[提交审批流<br>法务/财务/主管审批];
        B2 --> B3{审批通过?};
        B3 -- 否 --> A3;
    end
    
    subgraph C [3. 签署流程配置]
        B1 -- 否 --> C1;
        B3 -- 是 --> C1[设置签署方及签署顺序(顺序/并行)];
        C1 --> C2[拖拽添加签署区/填写控件];
        C2 --> C3[设置签署意愿认证方式(人脸/密码)];
    end

    subgraph D [4. 多方签署]
        C3 --> D1[发起合同, 通知第一顺位签署人];
        D1 --> D2[签署人通过H5/小程序<br>完成信息填写和签署];
        D2 --> D3{所有方是否签署完毕?};
        D3 -- 否 --> D1;
    end

    subgraph E [5. 完成与归档]
        D3 -- 是 --> E1[合同签署完成, 通知所有参与方];
        E1 --> E2[生成带数字签名的最终PDF文件];
        E2 --> E3[**自动归档**<br>加密存储, 上链存证(可选)];
        E3 --> E4[用户可随时检索、下载、申请出证];
    end
```

**流程描述**:

1.  **发起**: 业务员在PC后台点击“发起合同”，选择上传一个本地的Word文件。
2.  **协同与审批**: 文件上传后，业务员@法务人员进行在线审核。法务人员添加批注后，业务员修改并提交至预设的“合同审批流”。
3.  **配置**: 审批通过后，业务员在合同预览页面，从右侧工具栏拖拽“签署区”和“日期”控件到文件末尾，并从企业通讯录中选择签署人，设定签署顺序为“先客户，后我方”。
4.  **签署**: 客户方收到短信通知，点击链接进入H5页面，在指定位置手写签名，系统提示进行人脸识别，完成后提示“签署成功”。
5.  **归档**: 所有方签署完毕后，业务员和客户方都收到合同完成的通知。该合同自动进入企业的“已完成”档案库，并被打上“销售合同”的标签。

### 5.2 模板签署流程（企业常用场景）

此流程专为需要大批量签署内容相似合同的场景设计，如劳动合同、渠道协议等。

1.  **模板准备**: 管理员或法务人员预先将《劳动合同》制作成标准模板，并将姓名、身份证号、住址等设为可填写的动态字段，签署流程固定为“先员工，后HR（自动签）”。
2.  **发起任务**: HR选择该《劳动合同》模板，点击“批量发起”。
3.  **导入数据**: 下载Excel模板，填入本批次50名新员工的姓名、手机号、身份证号等信息，然后将Excel文件上传。
4.  **一键发起**: 系统根据Excel内容，自动为每位员工生成一份填充好个人信息的独立合同，并一次性向所有50名员工发出签署邀请。
5.  **员工签署**: 每位员工收到各自的签署链接，独立完成签署。
6.  **自动完成**: 当任一员工签署完成后，系统将自动加盖公司预设的HR部门印章，合同立即生效并归档至该员工的电子档案下。

### 5.3 签署人操作流程

此流程聚焦于签署人收到邀请后的核心操作路径，力求简洁、高效、安全。

```mermaid
sequenceDiagram
    participant Platform as 路浩AI电子签平台
    participant Signatory as 签署人
    
    Platform->>Signatory: 1. 发送签署通知 (短信/邮件/微信)
    Signatory->>Platform: 2. 点击通知中的链接/卡片
    Note over Signatory: 进入H5/小程序合同预览页
    Signatory->>Platform: 3. 阅读合同内容, 点击“立即签署”
    Platform->>Signatory: 4. 触发身份验证 (如人脸识别)
    Signatory->>Platform: 5. 按提示完成验证动作 (如读数字)
    Platform->>Platform: 6. 验证通过
    Note over Signatory: 页面跳转至签名区域
    Signatory->>Platform: 7. 在签名板上手写签名 / 选择预设印章
    Signatory->>Platform: 8. 点击“确认签署”
    Platform-->>Signatory: 9. 提示“签署成功”, 流程结束
    Platform->>Platform: 10. 记录操作日志, 通知下一方或归档
```

-----

## 六、权限与角色模型

### 6.1 权限控制模型

  - **采用RBAC（Role-Based Access Control）模型**: 系统预设多种标准角色（如超级管理员、法务、财务、业务员等），企业也可以根据自身需求创建自定义角色。
  - **权限原子化**: 权限被拆解为最小的操作单元（如：查看、创建、编辑、删除、下载、授权等），管理员可以为角色精细地勾选权限组合。
  - **数据权限隔离**: 在操作权限的基础上，增加了数据可见性维度，实现对合同、模板、印章等核心资产的精细化管控。主要维度包括：
      - **全公司**: 可查看和管理公司所有相关数据。
      - **本部门及子部门**: 可查看和管理自己所在部门及所有下级子部门的数据。
      - **仅本人**: 只能查看和管理由自己创建或参与的数据。
  - **特殊权限审批**: 某些高危权限（如删除已归档合同、变更企业认证信息）需要二次验证或审批流确认，防止误操作和越权行为。

### 6.2 权限矩阵示例

| 核心功能/操作 | 业务人员 | 法务人员 | 财务人员 | 企业管理员 | 超级管理员 |
| :--- | :---: | :---: | :---: | :---: | :---: |
| **合同模块** | | | | | |
| 发起合同 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 查看本人相关的合同 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 查看本部门的合同 | 角色配置 | ✅ | 角色配置 | ✅ | ✅ |
| 查看全公司的合同 | ❌ | ✅ | 角色配置 | ✅ | ✅ |
| 申请合同作废 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 审批合同作废 | 角色配置 | ✅ | 角色配置 | ✅ | ✅ |
| **模板模块** | | | | | |
| 使用企业模板 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 创建/编辑企业模板 | ❌ | ✅ | ❌ | ✅ | ✅ |
| **印章模块** | | | | | |
| 使用个人签名 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 申请使用企业印章 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 审批用印申请 | 角色配置 | 角色配置 | 角色配置 | ✅ | ✅ |
| 管理和授权企业印章 | ❌ | 角色配置 | ❌ | ✅ | ✅ |
| **组织与费用** | | | | | |
| 查看组织架构 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 编辑组织架构 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 查看费用与订单 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 购买套餐/申请发票 | ❌ | ❌ | ✅ | ✅ | ✅ |
| **系统管理** | | | | | |
| 查看审计日志 | ❌ | ✅ | 角色配置 | ✅ | ✅ |
| 配置审批流 | ❌ | ✅ | 角色配置 | ✅ | ✅ |
| 管理所有角色与权限 | ❌ | ❌ | ❌ | ✅ | ✅ |
| 进行企业认证/变更 | ❌ | ❌ | ❌ | ❌ | ✅ |

-----

## 七、版本规划与产品演进

### 7.1 MVP版本（V1.0 - 坚实核心）功能清单

MVP阶段的目标是打造业界领先、安全合规的电子签名核心能力，跑通最关键的业务闭环，覆盖主流签署场景。

  - **用户系统**:
      - 个人用户手机号+验证码注册/登录
      - 个人强实名认证（姓名+身份证+人脸识别）
      - 企业认证（法人授权或对公打款）
      - 个人手写签名创建与管理
  - **合同与签署**:
      - 从本地文件（PDF/Word）发起合同
      - 支持设置无序/顺序签署
      - 可视化拖拽配置签署区
      - 支持短信验证码作为基础意愿认证
      - 签署人在线查看并完成手写签名
  - **归档与管理**:
      - 已完成合同自动归档
      - 提供基础的合同列表查看、按标题搜索和下载功能
      - 记录核心操作（发起、签署、完成）的初步审计日志
  - **印章管理**:
      - 支持创建标准模板企业印章（公章、合同章）
      - 超级管理员拥有所有印章使用权

### 7.2 后续版本迭代计划

| 阶段 | 版本/主题 | 核心功能 | 目标用户价值 |
| :--- | :--- | :--- | :--- |
| **V1.1 - 企业赋能** | **企业标准版** | - 完善的组织架构与员工管理\<br\>- 基于角色的权限控制 (RBAC)\<br\>- 引入用印审批流、合同发起审批流\<br\>- 合同模板制作与管理\<br\>- 批量签署、自动签署 | 满足企业内部管理需求，将电子签融入日常工作流，提升组织效率。 |
| **V1.2 - 智能初探** | **企业专业版** | - **基础AI能力**: AI合同风险审查、关键信息提取\<br\>- **开放能力**: 提供核心签署流程的API/SDK\<br\>- **高级功能**: 一码多签、集团企业管控\<br\>- 多维度合同高级检索 | 引入AI辅助，降低合同风险；通过API集成，打通业务系统，实现数据联动。 |
| **V2.0 - 智能驱动** | **企业旗舰版** | - **高级AI套件**: 智能问答式合同生成、AI履约提醒\<br\>- **深度集成**: 推出嵌入式SDK，完善Webhook\<br\>- **数据洞察**: 合同数据驾驶舱，提供多维度分析报表\<br\>- **场景化方案**: 视频会议签、战略会议签\<br\>- **安全合规**: 全面支持国密算法、区块链存证 | 全面融入AI，实现从“工具”到“智能平台”的跃迁，挖掘合同数据价值，赋能企业决策。 |

-----

## 八、产品关键指标设计

### 8.1 核心指标

核心指标（北极星指标）用于衡量产品是否在向正确的商业目标迈进。

  - **合同签署完成量**: 反映产品的核心价值交付量，是业务增长的直接体现。
  - **用户活跃数 (MAU/WAU)**: 衡量产品的用户粘性和市场渗透情况，区分企业用户和个人用户。
  - **付费企业客户数 & 年度经常性收入(ARR)**: 衡量产品的商业化成功程度。

### 8.2 模块级指标（示例）

模块级指标用于监控具体功能的健康度，并指导功能优化。

| 模块 | 指标项 | 说明与计算公式 | 业务价值 |
| :--- | :--- | :--- | :--- |
| **用户与转化** | **新用户注册转化率** | (完成注册用户数 / 访问注册页用户数) \* 100% | 衡量注册流程的顺畅度和吸引力。 |
| | **企业认证通过率** | (完成企业认证数 / 发起认证企业数) \* 100% | 反映企业入驻流程的友好度和便捷性。 |
| **合同发起与签署** | **发起-完成转化率** | (签署完成合同数 / 发起合同总数) \* 100% | 衡量端到端签署流程的效率和成功率。 |
| | **平均签署耗时** | (合同完成时间 - 合同发起时间) 的平均值 | 反映产品效率的核心指标，耗时越短，价值越高。 |
| | **签署拒绝率** | (被拒签合同数 / 发起合同总数) \* 100% | 分析拒绝原因有助于发现产品或流程设计问题。 |
| **AI智能功能** | **AI功能采纳率** | (使用AI功能的用户数 / 总活跃用户数) \* 100% | 衡量AI功能的吸引力和实用性。 |
| | **AI审查风险识别准确率** | (AI正确识别的风险点 / 人工认定的总风险点) \* 100% (通过抽样评估) | 衡量AI模型的核心性能和可靠性。 |
| **模板系统** | **模板复用率** | (通过模板发起的合同数 / 发起合同总数) \* 100% | 衡量模板功能是否有效提升了用户的发起效率。 |
| **留存与付费** | **次月留存率 (企业)** | (本月登录过的企业下月仍登录的比例) | 衡量产品对企业用户的长期价值和粘性。 |
| | **LTV/CAC 比率** | (客户生命周期价值 / 客户获取成本) | 衡量商业模式的健康度和可持续性。 |

-----

## 九、后台运营支持设计

### 9.1 后台管理功能

为了保障SaaS业务的健康运转和提供优质的客户支持，运营后台需具备以下功能：

  - **用户管理**:
      - 查询、查看平台内所有个人和企业用户的详细信息（脱敏后）。
      - 对用户账号进行管理操作，如：禁用/启用账号、重置密码（需严格流程）、处理用户申诉。
      - 管理和审核特殊认证申请，如企业更名、法人变更等。
  - **合同与流程监控**:
      - 根据合同ID或客户信息，查询特定合同的当前状态和详细操作日志。
      - 对异常流程进行干预，如处理因系统问题导致的签署超时或失败。
      - 在客户授权下，执行手动关闭/重启流程、强制归档等高权限操作。
  - **订单与财务管理**:
      - 查看所有套餐购买订单记录，确认支付状态。
      - 处理退款请求，管理和审核发票申请。
  - **内容与配置管理 (CMS)**:
      - **套餐管理**: 配置不同版本的产品功能、价格、合同份数、API调用次数等。
      - **模板市场**: 上架、下架、编辑和管理官方提供的标准合同模板。
      - **公告与帮助**: 发布系统公告、更新帮助中心文档、推送行业资讯。
  - **数据与报表**:
      - **核心数据看板 (Dashboard)**: 实时监控平台核心指标（注册用户数、活跃用户数、合同签署量、GMV等）。
      - 定期生成运营报表，用于分析用户行为和业务趋势。
  - **工单系统**:
      - 接收和分配来自用户的各类问题反馈（技术支持、业务咨询、投诉建议）。
      - 追踪工单处理进度，确保客户问题得到及时响应和解决。

### 9.2 配置项

平台应提供丰富的可配置项，以适应不同企业客户的个性化需求：

  - **安全配置**:
      - 是否强制要求所有签署流程使用强意愿认证（如人脸识别）。
      - 可配置签署密码的复杂度规则。
      - 可配置企业水印，用于下载和打印的文件。
  - **流程配置**:
      - 合同默认的有效期设置（如发起后X天未完成则自动失效）。
      - 拒签后是否允许发起人重新编辑并发起。
      - 签署顺序的强/弱约束（强约束下，未轮到者无法查看合同）。
  - **通知配置**:
      - 可选择性开启或关闭某些场景的通知（如合同被查看通知）。
      - 企业可自定义部分通知模板内容（如邮件签名）。
  - **集成配置**:
      - 企业管理员可在后台自行生成和管理API Key。
      - 配置Webhook的回调地址和事件类型。

-----

## 十、附录

  - **合同合法性说明资料**:

      - **《中华人民共和国电子签名法》简述**: 明确规定了“可靠的电子签名”与手写签名或盖章具有同等的法律效力。可靠电子签名需满足四个条件：1) 电子签名制作数据专有；2) 签署时仅由签名人控制；3) 签名后任何改动能被发现；4) 数据电文内容和形式任何改动能被发现。
      - **本平台合规性保障**: "路浩AI电子签"通过与权威CA机构合作，采用PKI（公钥基础设施）体系为用户颁发数字证书，结合时间戳、多重身份认证、区块链存证（可选）和完整的证据链报告，确保平台上的每一次签署都构成“可靠的电子签名”，符合法律要求。

  - **核心技术说明**:

      - **数字签名 (Digital Signature)**: 基于非对称加密技术，使用签署人的私钥对文件哈希值进行加密，形成数字签名，可验证签署人身份的真实性和文件的完整性。
      - **时间戳 (Timestamp)**: 由权威时间戳服务机构（TSA）签发，用于证明文件在某个特定时间点已经存在且未被篡改，是确定签署时间的铁证。
      - **PDF高级电子签名 (PAdES)**: 一种国际标准，将签名信息、证书、时间戳等证据固化在PDF文件内部，任何对文件的非法篡改都会导致签名失效，便于离线验证。
      - **国密算法**: 平台支持SM2（非对称加密）、SM3（哈希算法）、SM4（对称加密）等国家商用密码算法，以满足金融、政务等领域对信创环境的安全要求。

  - **签署字段类型说明表**:

| 字段类型 | 图标/样式 | 功能说明 | 适用场景 |
| :--- | :--- | :--- | :--- |
| **签名** | 手写笔 | 签署人在此区域进行手写签名或加盖个人名章。 | 个人签署、法定代表人签字。 |
| **印章** | 印章图案 | 企业用户在此区域加盖预设的电子印章（公章、合同章等）。 | 企业作为一方签署合同时盖章。 |
| **日期** | 日历 | 系统自动填充签署当天的日期，或允许签署人手动选择。 | 合同生效日期、签署日期。 |
| **文本框** | T | 允许签署人填写单行或多行文本信息。 | 填写联系方式、地址、补充说明。 |
| **数字框** | 123 | 限制只能输入数字，可用于金额、数量等。 | 填写金额、身份证号。 |
| **勾选框** | 复选框 | 用于表示同意或选择某个选项。 | “我已阅读并同意以上条款”。 |
| **附件** | 曲别针 | 允许签署人上传附加文件，如身份证复印件、资质证明。 | 劳动合同中要求上传学历证明。 |
| **骑缝章** | 分页印章 | 配置后，印章将自动均等地分布在合同文件的每一页侧边。 | 确保多页合同的整体性和防替换。 |
