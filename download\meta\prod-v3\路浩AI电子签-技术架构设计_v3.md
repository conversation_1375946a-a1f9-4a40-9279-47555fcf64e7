# 路浩AI电子签 - 技术架构设计（V3.0）

## 1. 架构设计哲学与原则

- **云原生优先 (Cloud-Native First)**: 全面拥抱容器化、微服务、服务网格(Service Mesh)和声明式API，充分利用云平台的弹性、韧性和可观测性。
- **领域驱动设计 (DDD)**: 以业务领域为核心划分微服务，确保服务边界清晰、高内聚、低耦合，使技术架构与业务架构保持一致。
- **安全左移 (Shift-Left Security)**: 将安全设计贯穿于整个软件开发生命周期（SDLC），从编码、构建、测试到部署的每一个环节都内置安全考量。
- **数据驱动 (Data-Driven)**: 架构设计需支撑数据的采集、处理、分析和应用，使业务运营和产品迭代均由数据驱动。
- **拥抱开源与自主可控**: 积极采用业界领先的开源技术（如Kratos, TiDB, Kafka），同时在核心安全模块（如加密、签名）上，优先考虑国密标准，确保自主可控。

## 2. 整体技术架构

系统采用基于Go-Kratos的微服务架构，通过API Gateway对外提供统一服务，内部服务间通过gRPC进行高效通信。

```mermaid
graph TD
    subgraph User_Layer [用户与客户端]
        direction LR
        Client_Web[PC Web (React)]
        Client_Mobile[移动端 (Uni-app)]
        Client_API[API/SDK (Java/Go/Python)]
    end

    subgraph Gateway_Layer [API网关层]
        GW[API Gateway (Kratos Gateway)]
    end

    subgraph Service_Layer [微服务层 (Go-Kratos)]
        direction TB
        subgraph Biz_Services [业务服务]
            S_Account[account-service<br>账户与认证]
            S_Org[organization-service<br>组织与权限]
            S_Contract[contract-service<br>合同与签署]
            S_Template[template-service<br>模板中心]
            S_Seal[seal-service<br>印章中心]
        end
        subgraph AI_Services [AI服务]
            S_AI_Gen[ai-generation-service<br>合同生成与审查]
            S_AI_OCR[ai-ocr-service<br>智能抠图与识别]
        end
        subgraph Support_Services [支撑服务]
            S_Notify[notification-service<br>消息通知]
            S_Billing[billing-service<br>计费与订单]
        end
    end

    subgraph Data_Layer [数据与中间件层]
        direction LR
        DB_MySQL[**主业务库**<br>MySQL/TiDB Cluster]
        DB_Redis[**缓存**<br>Redis Cluster]
        DB_ES[**搜索引擎**<br>Elasticsearch Cluster]
        DB_Vector[**向量数据库**<br>Milvus/Qdrant]
        MQ_Kafka[**消息队列**<br>Kafka/Pulsar]
        Storage_MinIO[**对象存储**<br>MinIO Cluster]
    end

    subgraph Third_Party_Layer [第三方与安全基础设施]
        direction LR
        TP_CA[CA机构]
        TP_TSA[可信时间戳中心]
        TP_Blockchain[区块链存证节点]
        TP_SMS[短信服务]
        TP_LLM[大语言模型API]
    end

    User_Layer --> GW
    GW --> Biz_Services
    GW --> AI_Services
    Biz_Services -- gRPC --> Biz_Services
    Biz_Services -- gRPC --> AI_Services
    Biz_Services -- gRPC --> Support_Services
    Biz_Services -- Async --> MQ_Kafka
    Support_Services -- Consume --> MQ_Kafka
    AI_Services -- gRPC --> AI_Services
    
    Service_Layer -- 数据读写 --> Data_Layer
    S_Contract -- 安全调用 --> TP_CA & TP_TSA & TP_Blockchain
    S_Notify -- 外部调用 --> TP_SMS
    S_AI_Gen -- 外部调用 --> TP_LLM
```

## 3. 核心技术实现细节

### 3.1 安全加密核心

- **传输层安全**: 全链路强制TLS 1.3，API Gateway终结TLS，内部服务间通信启用mTLS。
- **数据存储加密**:
  - **敏感信息**: 用户身份证号、手机号、银行卡号等在数据库中**必须**使用`AES-256-GCM`或国密`SM4`算法加密存储，密钥由独立的KMS（密钥管理服务）管理。
  - **合同文件**: 存储在MinIO对象存储中的合同文件，采用服务端加密（SSE-S3），由MinIO管理加密密钥。
- **电子签名核心算法**:
  - **摘要算法**: 对合同原文PDF计算摘要，必须使用`SHA-256`或国密`SM3`。
  - **签名算法**: 调用CA机构服务时，使用`RSA-2048`或国密`SM2`非对称加密算法生成数字签名。
  - **时间戳**: 采用`RFC3161`标准协议与可信时间戳中心交互。

### 3.2 核心库表结构设计 (部分示例)

使用DDL进行定义，展示核心业务表的结构。

```sql
-- 合同主表
CREATE TABLE `contracts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '合同自增ID',
  `flow_id` varchar(64) NOT NULL COMMENT '合同流程ID，全局唯一',
  `title` varchar(255) NOT NULL COMMENT '合同标题',
  `enterprise_id` bigint(20) unsigned NOT NULL COMMENT '发起方企业ID',
  `initiator_id` bigint(20) unsigned NOT NULL COMMENT '发起人用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '合同状态: 1-草稿, 2-签署中, 3-已完成, 4-已撤销, 5-已过期, 6-已拒签',
  `template_id` varchar(64) DEFAULT NULL COMMENT '来源模板ID',
  `deadline` datetime DEFAULT NULL COMMENT '签署截止时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_flow_id` (`flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 合同签署方表
CREATE TABLE `contract_approvers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `flow_id` varchar(64) NOT NULL COMMENT '合同流程ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '签署人用户ID',
  `enterprise_id` bigint(20) unsigned DEFAULT NULL COMMENT '签署方企业ID (若为企业签署)',
  `sign_order` int(11) NOT NULL DEFAULT '0' COMMENT '签署顺序，0为无序',
  `sign_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '签署状态: 0-待签署, 1-已签署, 2-已拒签, 3-已转交',
  `signed_at` datetime DEFAULT NULL COMMENT '签署时间',
  `sign_cert_sn` varchar(128) DEFAULT NULL COMMENT '签署时使用的数字证书序列号',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 企业印章表
CREATE TABLE `seals` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seal_id` varchar(64) NOT NULL COMMENT '印章唯一ID',
  `enterprise_id` bigint(20) unsigned NOT NULL COMMENT '所属企业ID',
  `name` varchar(100) NOT NULL COMMENT '印章名称',
  `type` varchar(20) NOT NULL COMMENT '印章类型: OFFICIAL, CONTRACT, FINANCE...',
  `source` varchar(20) NOT NULL COMMENT '来源: TEMPLATE, UPLOAD',
  `file_url` varchar(512) NOT NULL COMMENT '印章图片文件URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态: 1-已启用, 2-已停用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_seal_id` (`seal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3.3 MQ消息流转机制

采用发布-订阅模式，实现核心业务的异步化和解耦。

- **消息队列选型**: Kafka或Pulsar，以其高吞吐、高可用和持久化能力，支撑业务增长。
- **核心Topic**:
  - `contract_status_change`: 当合同状态变更时（如一方签署完成、合同全部完成），`contract-service`向此Topic发送消息。
  - `notification_request`: 当需要发送短信、邮件或站内信时，业务服务向此Topic发送消息。
  - `evidence_chain_data`: 当签署完成、文件上传等关键操作发生时，向此Topic发送需要存证的数据摘要。
- **消费者**:
  - `notification-service`: 订阅`contract_status_change`和`notification_request`，负责调用第三方服务发送通知。
  - `evidence-service` (未在架构图显示，但实际存在): 订阅`evidence_chain_data`，负责将数据上链存证。
  - `data-sync-service`: 订阅多个业务Topic，将数据同步到Elasticsearch和数据仓库。

### 3.4 合同分布式存储

- **存储方案**: 采用**MinIO**对象存储集群，进行私有化部署，确保数据物理安全。集群采用纠删码模式，实现高可用和数据冗余。
- **存储逻辑**:
  1.  **原文上传**: 用户上传的原始文件（Word/图片等）存入`raw-files`桶。
  2.  **PDF转换**: 系统统一转换为PDF后，存入`pdf-preview`桶，用于签署过程中的预览。
  3.  **版本化存储**: 每次签署操作后，生成的带有新数字签名的PDF文件，作为一个**新版本**存入`signed-contracts`桶，利用MinIO的版本控制功能保留所有签署过程中的文件版本，便于追溯。最终完成的合同是该对象的最新版本。
  4.  **证据报告**: 生成的证据链报告PDF，存入`evidence-reports`桶。
- **访问控制**: 所有桶均设置为私有。业务服务通过生成的临时授权URL（presigned URL）访问文件，避免AK/SK在网络中传输。

### 3.5 AI合同生成与抠图技术实现

- **AI合同生成**:
  - **技术栈**: LangChain / LlamaIndex + 私有化部署的大模型（如ChatGLM, Qwen） + Milvus向量数据库。
  - **流程**:
    1.  **知识库构建**: 将海量法律法规、标准合同范本、企业自有合同等进行切分、清洗，通过Embedding模型（如BGE）向量化后存入Milvus。
    2.  **意图识别**: `ai-generation-service`识别用户意图（如“生成一份租赁合同”）。
    3.  **RAG检索**: 根据用户意图和对话内容，在向量数据库中检索最相关的法律条款和合同片段。
    4.  **Prompt构建**: 将用户需求、检索到的知识、预设的Prompt模板组合成一个丰富的Prompt。
    5.  **LLM调用**: 调用大模型服务，生成合同文本。
    6.  **后处理**: 对生成内容进行校验、格式化，并返回给用户。
- **电子章智能抠图**:
  - **技术栈**: OpenCV, PaddleSeg/U-Net。
  - **流程**:
    1.  **图像预处理**: `ai-ocr-service`对上传的印章图片进行尺寸归一化、去噪、二值化。
    2.  **印章定位**: 使用基于深度学习的图像分割模型，精确定位印章的主体像素区域。
    3.  **背景去除**: 将分割出的印章区域外的所有像素设置为透明。
    4.  **边缘优化**: 使用形态学操作（如腐蚀、膨胀）平滑印章边缘，去除毛刺。
    5.  **颜色标准化**: 将印章颜色统一为标准的“中国红”。