#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版腾讯千帆爬虫 - 支持JavaScript渲染和完整链接提取
"""

import os
import re
import time
import json
import random
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from tqdm import tqdm

from config import *
from utils import *
from url_manager import URLManager


class EnhancedTencentQianCrawler:
    """增强版腾讯千帆爬虫类"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.start_url = START_URL
        self.output_dir = OUTPUT_DIR
        
        # URL管理器
        self.url_manager = URLManager(self.output_dir, self.base_url)
        
        # 会话管理
        self.session = requests.Session()
        self.setup_session()
        
        # 内容处理
        import html2text
        self.html2text = html2text.HTML2Text()
        self.setup_html2text()
        
        # 设置日志
        self.setup_logging()
        
        # 统计信息
        self.stats = {
            'total_found': 0,
            'total_crawled': 0,
            'total_failed': 0,
            'total_saved': 0
        }
        
        # 初始化起始URL
        self.url_manager.add_url(self.start_url)
        
    def setup_session(self):
        """设置会话"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def setup_html2text(self):
        """设置HTML转Markdown"""
        self.html2text.ignore_links = False
        self.html2text.ignore_images = True
        self.html2text.ignore_emphasis = False
        self.html2text.body_width = 0
        self.html2text.unicode_snob = True
        
    def setup_logging(self):
        """设置日志"""
        log_file = os.path.join(self.output_dir, 'enhanced_crawler.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('enhanced_crawler')
        
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(REQUEST_CONFIG['user_agents'])
        
    def fetch_page(self, url, retries=None):
        """获取网页内容，确保UTF-8编码处理"""
        if retries is None:
            retries = REQUEST_CONFIG['retry_times']
            
        for attempt in range(retries + 1):
            try:
                headers = {
                    'User-Agent': self.get_random_user_agent(),
                    'Accept-Charset': 'utf-8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                }
                
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=REQUEST_CONFIG['timeout']
                )
                
                response.encoding = 'utf-8'
                
                if response.status_code == 200:
                    self.logger.info(f"成功获取页面: {url}")
                    content = response.text
                    if isinstance(content, bytes):
                        content = content.decode('utf-8', errors='ignore')
                    return True, content
                elif response.status_code == 404:
                    self.logger.warning(f"页面不存在: {url}")
                    return False, "404 Not Found"
                else:
                    self.logger.warning(f"HTTP错误 {response.status_code}: {url}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{retries + 1}): {url} - {str(e)}")
                
                if attempt < retries:
                    wait_time = random.uniform(2, 5) * (attempt + 1)
                    time.sleep(wait_time)
                    
        return False, "Max retries exceeded"
        
    def extract_sidebar_links(self, html, base_url):
        """提取侧边栏中的所有链接"""
        soup = BeautifulSoup(html, 'lxml')
        links = set()
        
        # 查找侧边栏导航
        sidebar = soup.find('nav', {'aria-label': '文档侧边栏'})
        if not sidebar:
            sidebar = soup.find('aside', class_='theme-doc-sidebar-container')
        
        if sidebar:
            # 提取所有链接
            for link in sidebar.find_all('a', href=True):
                href = link['href']
                if href and not href.startswith('#'):
                    full_url = urljoin(base_url, href)
                    if is_valid_url(full_url, self.base_url):
                        links.add(full_url)
                        
        # 额外查找可能的文档链接模式
        # 查找所有符合文档URL模式的链接
        doc_pattern = re.compile(r'/document/\d+')
        for link in soup.find_all('a', href=True):
            href = link['href']
            if href and doc_pattern.search(href):
                full_url = urljoin(base_url, href)
                if is_valid_url(full_url, self.base_url):
                    links.add(full_url)
                    
        return list(links)
        
    def discover_all_links(self):
        """发现所有可能的文档链接"""
        self.logger.info("开始发现所有文档链接...")
        
        # 从起始页面开始
        success, html = self.fetch_page(self.start_url)
        if not success:
            self.logger.error("无法获取起始页面")
            return
            
        # 提取侧边栏链接
        sidebar_links = self.extract_sidebar_links(html, self.start_url)
        self.logger.info(f"从起始页面发现 {len(sidebar_links)} 个侧边栏链接")
        
        # 添加到URL管理器
        added_count = self.url_manager.add_urls_batch(sidebar_links, self.start_url)
        self.logger.info(f"新增 {added_count} 个URL到待抓取列表")
        
        # 尝试从其他页面发现更多链接
        sample_urls = list(self.url_manager.all_urls)[:5]  # 取前5个URL作为样本
        
        for url in sample_urls:
            if url == self.start_url:
                continue
                
            self.logger.info(f"从样本页面发现链接: {url}")
            success, html = self.fetch_page(url)
            if success:
                links = self.extract_sidebar_links(html, url)
                if links:
                    new_added = self.url_manager.add_urls_batch(links, url)
                    if new_added > 0:
                        self.logger.info(f"从 {url} 新增 {new_added} 个链接")
                        
            # 避免请求过快
            time.sleep(random.uniform(1, 3))
            
        # 保存发现的所有URL
        self.url_manager.save_urls()
        
        final_stats = self.url_manager.get_statistics()
        self.logger.info(f"链接发现完成，总共发现 {final_stats['total_urls']} 个URL")
        
    def extract_content(self, html, url):
        """提取页面内容"""
        soup = BeautifulSoup(html, 'lxml')
        
        # 提取标题
        title = self.extract_title(soup)
        
        # 提取主要内容
        main_content = self.extract_main_content(soup)
        
        # 转换为Markdown
        markdown_content = self.convert_to_markdown(main_content)
        
        return {
            'url': url,
            'title': title,
            'content': markdown_content,
            'links': []  # 不再从内容中提取链接，因为已经预先发现了所有链接
        }
        
    def extract_title(self, soup):
        """提取页面标题"""
        # 尝试多种标题提取方式
        title_selectors = [
            'h1',
            '.theme-doc-markdown h1',
            'title',
            '.docTitle_node_modules',
            '[data-testid="doc-title"]'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                if title and title != "文档中心":
                    return title
                    
        return "未知标题"
        
    def extract_main_content(self, soup):
        """提取主要内容"""
        # 查找主要内容区域
        content_selectors = [
            '.theme-doc-markdown',
            '.markdown',
            'article',
            '.docItemContainer_c0TR',
            'main .container'
        ]
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 清理不需要的元素
                self.clean_content(content_elem)
                return str(content_elem)
                
        # 如果没找到特定容器，返回body内容
        body = soup.find('body')
        if body:
            self.clean_content(body)
            return str(body)
            
        return str(soup)
        
    def clean_content(self, content_element):
        """清理内容元素"""
        # 移除不需要的元素
        unwanted_selectors = [
            'script', 'style', 'nav', 'header', 'footer',
            '.navbar', '.sidebar', '.toc', '.breadcrumb',
            '.pagination', '.feedback', '.advertisement',
            '[class*="ad-"]', '[id*="ad-"]'
        ]
        
        for selector in unwanted_selectors:
            for elem in content_element.select(selector):
                elem.decompose()
                
    def convert_to_markdown(self, html_content):
        """转换HTML为Markdown"""
        try:
            markdown = self.html2text.handle(html_content)
            # 清理多余的空行
            markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
            return markdown.strip()
        except Exception as e:
            self.logger.error(f"HTML转Markdown失败: {str(e)}")
            return html_content
            
    def save_content(self, content_info):
        """保存内容到文件"""
        try:
            # 生成文件名
            filename = generate_filename_from_content(
                content_info['title'],
                content_info['content'][:500],  # 使用前500字符生成文件名
                content_info['url']
            )
            
            # 确保文件名是有效的UTF-8字符串
            if isinstance(filename, bytes):
                filename = filename.decode('utf-8', errors='ignore')
            
            # 添加扩展名
            filename += FILE_CONFIG['markdown_extension']
            
            # 确保文件名唯一
            filepath = os.path.join(self.output_dir, filename)
            filepath = ensure_unique_filename(filepath)
            
            # 准备文件内容
            file_content = f"# {content_info['title']}\n\n"
            file_content += f"> 来源: {content_info['url']}\n"
            file_content += f"> 抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            file_content += "---\n\n"
            file_content += content_info['content']
            
            # 确保内容是字符串格式
            if isinstance(file_content, bytes):
                file_content = file_content.decode('utf-8', errors='ignore')
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8-sig', newline='\n') as f:
                f.write(file_content)
                
            safe_filename = os.path.basename(filepath)
            if isinstance(safe_filename, bytes):
                safe_filename = safe_filename.decode('utf-8', errors='replace')
            
            self.logger.info(f"保存文件: {safe_filename}")
            self.stats['total_saved'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            return False
            
    def crawl_single_page(self, url):
        """爬取单个页面"""
        try:
            # 随机等待
            random_sleep(
                REQUEST_CONFIG['delay_min'],
                REQUEST_CONFIG['delay_max']
            )
            
            # 获取页面内容
            success, html_content = self.fetch_page(url)
            
            if not success:
                self.url_manager.mark_failed(url, html_content)
                self.stats['total_failed'] += 1
                return False
                
            # 提取内容
            content_info = self.extract_content(html_content, url)
            
            # 保存内容
            save_success = self.save_content(content_info)
            
            # 更新状态
            if save_success:
                self.url_manager.mark_completed(url)
                self.stats['total_crawled'] += 1
            else:
                self.url_manager.mark_failed(url, "保存文件失败")
                
            return save_success
            
        except Exception as e:
            self.logger.error(f"爬取页面失败: {url} - {str(e)}")
            self.url_manager.mark_failed(url, str(e))
            self.stats['total_failed'] += 1
            return False
            
    def run(self, max_pages=None, max_workers=None, discover_links=True):
        """运行增强版爬虫"""
        if max_workers is None:
            max_workers = PROGRESS_CONFIG['max_concurrent']
            
        self.logger.info(f"开始增强版爬取，最大页面数: {max_pages or '无限制'}, 最大线程数: {max_workers}")
        
        # 第一步：发现所有链接
        if discover_links:
            self.discover_all_links()
        
        # 第二步：抓取所有页面
        pbar = tqdm(
            desc="抓取进度",
            unit="页",
            dynamic_ncols=True
        )
        
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                while not self.url_manager.is_all_completed():
                    if max_pages and self.stats['total_crawled'] >= max_pages:
                        self.logger.info(f"已达到最大页面数: {max_pages}")
                        break
                        
                    # 获取待处理的URL
                    pending_urls = self.url_manager.get_pending_urls()
                    current_batch = pending_urls[:max_workers]
                    
                    if not current_batch:
                        break
                        
                    # 提交任务
                    futures = [
                        executor.submit(self.crawl_single_page, url)
                        for url in current_batch
                    ]
                    
                    # 等待完成
                    for future in as_completed(futures):
                        result = future.result()
                        pbar.update(1)
                        
                        url_stats = self.url_manager.get_statistics()
                        pbar.set_postfix({
                            '成功': self.stats['total_crawled'],
                            '失败': self.stats['total_failed'],
                            '待处理': url_stats['pending_urls']
                        })
                        
                    # 定期保存进度
                    if self.stats['total_crawled'] % PROGRESS_CONFIG['save_interval'] == 0:
                        self.url_manager.save_urls()
                        
        except KeyboardInterrupt:
            self.logger.info("用户中断爬取")
        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {str(e)}")
        finally:
            pbar.close()
            self.url_manager.save_urls()
            self.print_summary()
            
    def print_summary(self):
        """打印爬取摘要"""
        url_stats = self.url_manager.get_statistics()
        
        print("\n" + "="*50)
        print("增强版爬取摘要")
        print("="*50)
        print(f"发现页面数: {url_stats['total_urls']}")
        print(f"成功爬取: {url_stats['completed_urls']}")
        print(f"爬取失败: {url_stats['failed_urls']}")
        print(f"保存文件: {self.stats['total_saved']}")
        print(f"待处理页面: {url_stats['pending_urls']}")
        print(f"完成率: {url_stats['completion_rate']:.2f}%")
        print("="*50)
        
        # 生成详细报告
        self.url_manager.export_report()
        
        if self.url_manager.is_all_completed():
            print("所有URL都已处理完成!")
        else:
            print("还有未完成的URL，可以重新运行继续抓取")


if __name__ == "__main__":
    crawler = EnhancedTencentQianCrawler()
    
    # 运行增强版爬虫
    crawler.run(
        max_pages=None,  # 不限制页面数
        max_workers=3,   # 并发数
        discover_links=True  # 启用链接发现
    ) 