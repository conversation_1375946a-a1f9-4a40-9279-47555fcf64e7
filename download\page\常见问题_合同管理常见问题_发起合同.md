# 发起合同

> 导航路径: 常见问题 > 合同管理常见问题 > 发起合同
> 来源: https://qian.tencent.com/document/71867
> 抓取时间: 2025-06-15 16:41:55

---

### 如何发起合同？ 

请参见 [发起合同](https://cloud.tencent.com/document/product/1323/61360 "https://cloud.tencent.com/document/product/1323/61360") 文档指引。

### 已发起的合同支持撤回吗？ 

在合同未完成签署前，对已发起的合同支持撤回操作，详细指引请参见 [撤回合同](https://cloud.tencent.com/document/product/1323/61359 "https://cloud.tencent.com/document/product/1323/61359") 文档指引。

### 当我的企业主体是 A 公司，可以由我司发起合同，然后由 B 公司和 C 公司双方进行签署吗？

可以。只需要在发起合同时，添加需要签署的企业或个人为签署方即可，并为上述签署方添加相关控件，如何发起合同请参见 [发起合同](https://cloud.tencent.com/document/product/1323/77774 "https://cloud.tencent.com/document/product/1323/77774") 文档。

### 我使用腾讯电子签签署了合同，这份合同是否可以导出保存到用户本地？

已签署的合同有以下方式下载：

1. 腾讯电子签小程序：您可在小程序的合同详情中，单击**发至邮箱** ，将合同文件发送至指定邮箱中（如下图所示）。

﻿

﻿

﻿  

2. 腾讯电子签电脑端：有查看合同权限的企业员工者可在**合同管理** 处选择相应合同进行下载。

﻿

﻿

﻿  

### 为什么在发起合同时提示：至少给签署方添加一个签署控件，并无法发起合同？

签署控件是配置合同必须添加的控件，至少为每个签署方添加一个签署控件才能发起合同。

操作方法：选择右方的**签署方** ，单击后出现该签署方的相应控件，拖拽其名下的签署控件即可生成（如下图所示）。

﻿

﻿

﻿  

### 合同发起后是如何通知签署人的？

合同发起成功后，系统将自动通过短信通知签署人进行签署。

### 一码多签和群闪签功能上有什么区别？

功能上，[一码多签](https://qian.tencent.com/document/93021#31e8a7e4-38e7-48bf-9453-12f321993bce) 及 [群闪签](https://solution.ess.tencent.cn/) 均支持通过合同模板生成签署码，一次发起可多人扫码签署合同。

区别在于一码多签功能比较简单，同一份模板只能生成一个签署码，适用于场景单一的活动签到，内测协议签署等。

而群闪签支持同一份模板根据签约机构层级不同，可以分层生成签署码，例如xx市中小学生暑假安全承诺书中可以根据不同的学校/年级/班级生成对应的签署码，有助于数据分层统计管理。

除此之外，群闪签提供多维度的签署数据可视化看板，实时查看签署详细情况，实现数据精准回收。