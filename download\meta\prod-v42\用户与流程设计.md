# 路浩AI电子签 - 用户与核心流程设计（V4.2）

本文档旨在明确"路浩AI电子签"平台的所有核心用户角色，并以流程图和文字描述相结合的方式，详细阐述关键业务流程，确保产品设计与开发团队对用户操作路径有统一和清晰的理解。

## 1. 核心用户角色定义 (System Roles)

V4.2版本对用户角色进行了扩展和细化，以覆盖更广泛的业务场景。

| 角色分类 | 角色名称 | 核心职责与权限范围 |
| :--- | :--- | :--- |
| **外部用户** | **个人用户** | **职责**: 作为独立的法律主体，签署个人相关的合同、协议、文件。 <br> **权限**: 拥有个人账户管理、实名认证、个人签名/印章管理、发起/签署个人合同、管理个人合同等能力。 |
| | **签署人 (Signatory)** | **职责**: 代表个人或企业，完成指定合同的签署操作。是临时的、任务性的角色。<br>**权限**: 仅限于查看和签署被指定的合同，无法访问平台其他功能。 |
| **企业内部用户** | **超级管理员 (Super Admin)** | **职责**: 企业平台的最高权限所有者，负责企业认证、购买服务、初始化配置、分配系统级管理员等。<br>**权限**: 拥有平台所有管理权限，是企业安全的最后一道防线。 |
| | **企业管理员 (Admin)** | **职责**: 负责企业的日常运营管理，包括组织架构维护、员工账号管理、角色与权限分配等。<br>**权限**: 由超管授予，通常管理除"超管设置"外的所有功能。 |
| | **法务人员 (Legal)** | **职责**: 负责企业合同的合规性审查、标准模板的制定与管理、处理合同纠纷。<br>**权限**: 通常被授予查看所有合同、管理合同模板、配置审批流、处理出证申请等权限。 |
| | **财务人员 (Finance)** | **职责**: 负责企业的费用管理、发票申请与核销、对公打款认证、合同金额相关的审批。<br>**权限**: 拥有费用中心访问权限，通常作为高金额合同审批流中的关键节点。 |
| | **业务人员 (Employee)** | **职责**: 企业内业务的执行者，在被授予的权限内发起和管理与自身业务相关的合同。<br>**权限**: 权限范围由其角色决定，如发起合同、申请用印、查看自己参与的合同等。 |
| **政务用户** | **机构管理员** | **职责**: 类似于企业管理员，负责政务机构的组织与人员管理，以及公文模板、签章的管理。<br>**权限**: 管理机构内部的组织树、人员账号，配置公文流转规则，管理机构电子签章。 |
| **平台后台** | **运营后台人员** | **职责**: 路浩AI电子签平台的内部运营和支持人员。<br>**权限**: 管理官方模板市场、处理用户申诉、审核特殊认证（如企业更名）、监控平台运行状态等，不接触客户业务数据。 |

---

## 2. 核心业务流程 (Core Business Flows)

### 2.1 流程一：标准合同签署全流程 (从起草到归档)

此流程覆盖了一份标准合同从无到有、从草稿到法律文件的完整生命周期。

```mermaid
graph TD
    subgraph A [合同拟定]
        A1[用户选择发起方式] --> A2{AI生成/模板发起/本地上传};
        A2 -- AI生成 --> A3[与AI助手多轮对话, 生成合同初稿];
        A2 -- 模板发起 --> A4[选择模板, 填写业务信息];
        A2 -- 本地上传 --> A5[上传Word/PDF, 系统自动转换格式];
    end

    subgraph B [内部协同与审批]
        A3 & A4 & A5 --> B1[在线协同编辑<br>多人修改/批注/版本管理];
        B1 --> B2{是否需要审批?};
        B2 -- 是 --> B3[提交审批流<br>法务/财务/主管审批];
        B3 --> B4{审批通过?};
        B4 -- 否 --> B1;
    end
    
    subgraph C [签署流程配置]
        B2 -- 否 --> C1;
        B4 -- 是 --> C1[设置签署方及签署顺序(顺序/并行)];
        C1 --> C2[拖拽添加签署区/填写控件];
        C2 --> C3[设置签署意愿认证方式(人脸/密码)];
    end

    subgraph D [多方签署]
        C3 --> D1[发起合同, 通知第一顺位签署人];
        D1 --> D2[签署人通过H5/小程序<br>完成信息填写和签署];
        D2 --> D3{所有方是否签署完毕?};
        D3 -- 否 --> D1;
    end

    subgraph E [完成与归档]
        D3 -- 是 --> E1[合同签署完成, 通知所有参与方];
        E1 --> E2[生成带数字签名的最终PDF文件];
        E2 --> E3[**自动归档**<br>加密存储, 上链存证(可选)];
        E3 --> E4[用户可随时检索、下载、申请出证];
    end
```

**操作页面视角描述**:
1.  **拟定**: 业务员在PC后台点击"发起合同"，选择"AI生成"，在弹出的对话框中回答AI提问。AI生成草稿后，页面跳转至在线编辑器，业务员可手动修改文本。
2.  **审批**: 编辑器右侧有"提交审批"按钮。点击后，选择预设的"高金额合同审批流"，系统将任务推送给财务总监。财务总监在手机端收到通知，打开合同预览，点击"同意"。
3.  **配置**: 业务员收到审批通过的通知，返回合同编辑页面，从右侧工具栏拖拽"签署区"控件到文件末尾，并从通讯录选择乙方签署人。
4.  **签署**: 乙方签署人收到短信，点击链接进入手机H5页面，在指定位置手写签名，系统提示进行人脸识别，完成后提示"签署成功"。
5.  **归档**: 流程结束后，业务员在"我的合同-已完成"列表中能看到该合同，并可点击"下载"或"申请出证"。

### 2.2 流程二：企业认证与印章管理流程

此流程是企业首次入驻平台并完成基础配置的核心步骤。

```mermaid
graph TD
    A[企业管理员首次登录] --> B(第一步：企业认证);
    B --> C{选择认证方式};
    C -- 法定代表人授权 --> D[填写企业名和信用代码<br>系统发送链接给法人];
    D --> E[法人扫码人脸识别, 认证成功];
    C -- 对公打款 --> F[填写企业对公账户信息];
    F --> G[向该账户打一笔随机金额];
    G --> H[管理员回填准确金额, 认证成功];
    
    E & H --> I(第二步：印章创建与管理);
    I --> J{选择创建方式};
    J -- AI抠图上传 --> K[上传清晰的实体印章盖印图片];
    K --> L[系统AI自动抠图、优化、生成预览];
    J -- 标准模板生成 --> M[选择印章类型(公章/合同章)<br>系统根据认证信息生成国标样式印章];
    
    L & M --> N[管理员确认, 印章创建成功];
    N --> O(第三步：印章授权);
    O --> P[选择印章, 点击"授权"];
    P --> Q[选择授权对象(员工/部门/角色)];
    Q --> R[设置授权期限(长期/指定时间)];
    R --> S[完成授权, 相关人员即可在签署时使用该印章];

```
**操作页面视角描述**:
1.  **认证**: 企业管理员在后台"企业设置-企业认证"页面，选择"法定代表人授权"方式，输入企业名称后，点击"发送授权邀请"，并将弹出的二维码截图发给法人。
2.  **印章创建**: 认证成功后，进入"印章管理"页面，点击"新增印章"，选择"上传图片创建"。上传一张在白纸上盖的公章图片，页面loading几秒后，显示出一个背景透明的、红色的印章预览图，点击"确认创建"。
3.  **印章授权**: 在印章列表，找到刚刚创建的"合同专用章"，点击操作栏的"授权"按钮。在弹出的窗口中，左侧是组织架构树，勾选"销售部"，点击"确定"，即完成对整个销售部的授权。

### 2.3 流程三：发票申请与处理流程

此流程展示了企业在购买套餐后如何申请发票。

```mermaid
sequenceDiagram
    participant Admin as 企业管理员/财务
    participant Platform as 平台后台
    participant BillingSvc as 计费中心
    participant FinanceSys as 企业财务系统

    Admin->>Platform: 1. 进入"费用中心-订单管理"
    Admin->>Platform: 2. 勾选一个或多个可开票的订单
    Admin->>Platform: 3. 点击"申请发票"
    
    Note right of Admin: 页面跳转至发票信息填写页

    Admin->>BillingSvc: 4. 填写发票抬头、税号、地址等信息, 选择发票类型(专票/普票), 提交申请
    BillingSvc-->>Admin: 5. 提示"申请已提交, 等待审核"
    
    BillingSvc->>运营后台: 6. 生成开票审核任务
    运营后台->>BillingSvc: 7. 审核通过, 触发开票
    
    alt 电子发票
        BillingSvc->>第三方税务接口: 8a. 请求开具电子发票
        第三方税务接口-->>BillingSvc: 9a. 返回发票PDF文件
        BillingSvc->>Admin: 10a. 发送通知, 管理员可在线下载/查看
        Admin->>FinanceSys: 11a. 下载电子发票, 导入财务系统报销
    else 纸质发票
        BillingSvc->>运营后台: 8b. 生成邮寄任务
        运营后台-->>Admin: 9b. 填写快递单号, 更新发票状态为"已邮寄"
        Admin->>FinanceSys: 10b. 收到纸质发票, 进行线下核验报销
    end

```
**操作页面视角描述**:
1.  **申请**: 财务人员登录后台，进入"费用中心-发票管理"，点击"索取发票"。在列表中勾选了上个月购买的"1000份合同套餐"订单，点击"下一步"。
2.  **填写**: 在发票信息页面，系统已自动带出企业名称和税号。财务人员补充了开户行和地址信息，选择了"增值税专用发票"，点击"提交"。
3.  **查看**: 两天后，财务人员收到平台短信通知，提示发票已开具。登录后台在"发票管理-历史记录"中，看到该条记录状态变为"已开具"，并可点击"下载"按钮获取PDF电子发票。

### 2.4 流程四：合同作废审批流程

对于已签署完成但需要作废的合同，平台提供合规的线上作废流程。

```mermaid
graph TD
    A[业务员在"已完成"合同列表中] --> B[找到需作废的合同, 点击"更多-申请作废"];
    B --> C[填写作废原因, 如"业务取消, 双方协商一致"];
    C --> D[选择预设的"合同作废审批流程"];
    D --> E[提交申请];
    
    subgraph Approval [审批环节]
        E --> F[任务流转至第一级审批人(部门主管)];
        F --> G{主管是否同意?};
        G -- 同意 --> H[任务流转至第二级审批人(法务)];
        H --> I{法务是否同意?};
        G -- 拒绝 --> J[流程终止, 通知申请人"作废被驳回"];
        I -- 拒绝 --> J;
    end
    
    I -- 同意 --> K[审批通过, 系统自动创建一份《合同作废协议》];
    K --> L[自动将原合同各签署方添加为作废协议的签署方];
    L --> M[向各方发送签署《作废协议》的通知];
    
    subgraph Signing [签署作废协议]
        M --> N[各方通过手机完成《作废协议》的签署];
        N --> O{是否全部签署完毕?};
        O -- 否 --> N;
    end
    
    O -- 是 --> P[原合同状态自动更新为"已作废"];
    P --> Q[所有操作被记录, 形成完整证据链];
```
**操作页面视角描述**:
1.  **发起申请**: 销售员在"我的合同"中找到一份已完成的合同，因客户业务调整需作废。他点击"申请作废"，在弹窗中写明原因并提交。
2.  **审批**: 销售主管和法务先后收到待办通知，在手机端查看作废申请和原合同信息后，均点击了"同意"。
3.  **签署作废协议**: 销售员和原合同的客户方法定代表人，都收到了签署一份名为"关于XXX合同的作废协议"的新通知，各自完成签署。
4.  **结果**: 签署完成后，销售员返回合同列表，看到原合同上已被标记了一个红色的"已作废"戳记。 