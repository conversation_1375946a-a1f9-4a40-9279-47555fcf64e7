# 合同管理

> 导航路径: 企业版操作指南 > 合同相关 > 合同管理
> 来源: https://qian.tencent.com/document/92778/
> 抓取时间: 2025-06-15 16:42:29

---

本文将为您介绍合同发起/签署后可以对合同进行的操作，包括**合同管理、下载、申请合同相关核验报告** 等。

## **功能导览**

1. 页面中的内容、功能及按钮的展示和使用会因各账号拥有权限而异，本文介绍的功能为全量功能，如有功能您无法使用请申请相应权限。

2. 功能主要在**合同中心（网页端）** /**文件夹（小程序端）** 页面内，也有少部分在其它页面的合同相关内容。

3. 本文涉及**所有功能** 及**支持端口** 一览（部分功能可能有多个路径可实现，单击查看仅展示其中一种方式）。

功能| 网页端| 小程序端  
---|---|---  
查询合同文件| ﻿点击查看﻿| ﻿点击查看﻿  
签署合同| ﻿点击查看﻿| ﻿点击查看﻿  
查看合同基本信息| ﻿点击查看﻿| ﻿点击查看﻿  
撤销合同| ﻿点击查看﻿| ﻿点击查看﻿  
拒签合同| ﻿点击查看﻿| ﻿点击查看﻿  
转他人处理| ﻿点击查看﻿| ﻿点击查看﻿  
设置合同类型| ﻿点击查看﻿| ﻿点击查看﻿  
再写一份| ﻿点击查看﻿| ﻿点击查看﻿  
下载签署码/发送给签署人| ﻿点击查看﻿| ﻿点击查看﻿  
下载合同| ﻿点击查看﻿| ﻿点击查看﻿  
设置合同到期提醒日| ﻿点击查看﻿| ﻿点击查看﻿  
合同草稿箱| ﻿点击查看﻿| /  
申请签署报告 _本篇重点功能_|  /| ﻿点击查看﻿  
申请公证处核验报告 _本篇重点功能_|  /| ﻿点击查看﻿  
批量操作-批量下载| ﻿点击查看﻿| /  
批量操作-设置合同类型| ﻿点击查看﻿| /  
批量操作-生成报表| ﻿点击查看﻿| /  
批量操作-清理失效文件| ﻿点击查看﻿| ﻿点击查看﻿  
合同验签 _本篇重点功能_|  ﻿点击查看﻿| ﻿点击查看﻿  
合同对比 _本篇重点功能_|  ﻿点击查看﻿| /  
合同解除 _本篇重点功能_|  ﻿点击查看（接口）| /  
  
## 合同中心（网页端）

﻿

﻿

﻿  

### **合同文件夹**

﻿

﻿

﻿  

**下级分类：**

**全部合同** ：以列表形式展示**已发起的合同** 及其相关信息，可以单击右上角齿轮 icon 调整展示信息项和顺序。

**我的待办** ：待我处理（填写、签署等）的所有合同。

**我参与的** ：包含我发起的、我参与签署的合同。

**提醒我关注的** ：抄送给我的所有合同。

﻿

**查询合同文件：**

﻿

﻿

﻿

﻿  

在顶部**搜索框** 根据相应内容进行搜索查询。

在**筛选** 中通过筛选项进行查询。

**对单个合同的操作：**

﻿

**详情**

：单击进入合同详情页，可以看到合同内容、基础信息和签署类型并进行以下操作。

﻿

﻿

﻿  

**更多操作** ：撤销（本人发起的合同出现该项）、拒签（待我签署的合同出现该项）、转他人处理（待我签署的合同出现该项）。

﻿

**下载签署码**

：下载签署码后可发送给签署方扫码进行签署。

**再写一份** ：以该合同为模直接生成一份合同，上一次自己填写的内容将会自动填充。

﻿

**下载合同**

：下载该合同为 PDF 版本。

**签署合同** ：待我签署的合同出现该项，具体操作请参见 [合同签署](https://qian.tencent.com/document/92775)。

﻿

**设置到期提醒日**

：在基础信息里找到到期提醒日，单击小铅笔图标进行设置。

﻿

﻿

﻿  

﻿

**签署**

：对待我签署的合同进行签署，单击进入签署流程。具体操作请参见 [合同签署](https://qian.tencent.com/document/92775)。

**更多** ：

﻿

**撤销**

：已发起的合同，在签署**双方未完成签署** 的情况下，企业方可撤销。

﻿

﻿

﻿  

﻿

**拒签**

：对于待我处理的合同，可以根据实际情况，选择拒绝签署。

﻿

﻿

﻿  

﻿

**转他人处理**

：对于待我处理的合同，可以根据实际情况选择转给企业其他人员处理。若已被经办人填写了内容，将不再支持转他人处理。

﻿

﻿

﻿  

﻿

**设置合同类型**

：对于未分类的合同，可以通过设置合同类型将其分类以便查找和管理。

﻿

﻿

﻿  

﻿

**再写一份**

：以自己曾经发起的合同为模板直接生成一份合同，上一次自己填写的内容将会自动填充。

﻿

﻿

﻿  

**批量操作：**

﻿

**批量下载**

：可批量下载选中的合同文件，不限制合同当前状态。

﻿

**生成报表**

：可批量生成选中的合同签署报表信息。

﻿

**设置合同类型**

：可以对选中合同设置合同类型，用户可以自定义合同类型。

﻿

**清理失效文件**

：可对已撤销、已拒签、已过期的合同进行删除。单击**清理失效文件** ，可根据上方标签进行**筛选** 、也可以单击勾选框对合同进行选择，单击确认**清理** 即可清理。这里仅是从本企业合同列表中删除。

﻿

﻿

﻿  

### **合同草稿箱**

**发起合同** ：在发起合同流程中，**保存为草稿** 的所有记录。可以继续**编辑** 或**删除** 记录。具体操作可见 [合同发起](https://qian.tencent.com/document/92855)。

﻿

﻿

﻿  

**起草合同** ：在起草合同流程中，未完成发起的所有记录。可以**下载** 合同文件、继续**编辑** 或**删除** 记录。具体操作请见 [合同起草](https://qian.tencent.com/document/100596)。

﻿

﻿

﻿  

### **批量发起记录**

﻿

﻿

﻿  

单击**查看** 进入批量发起合同页面。

﻿

﻿

﻿  

单击**导出结果** 即可将结果导出为表格。

单击**继续批量发起** 将进入新的批量发起流程。具体操作请参见 [批量发起合同](https://qian.tencent.com/document/92774)。

单击**批量发起历史记录** 将回到上级页面。

﻿

**合同类型状态** ：共有两种状态已启用/已停用。默认为启用，系统默认类型和自定义类型均支持停用。停用后，该类型将不会出现在发起合同、创建/编辑模板、设置合同类型时的选项列表中，即无法再选择此类型。启用后重新出现且可选。

**注意：**

在无合同或模板使用此类型时才支持停用。如您当前有较多合同/模板使用此类型，建议删除（删除时支持将此类型下的合同和模板归属到其他您指定的合同类型下）。

单击**新增** 即可新增自定义合同类型。

单击**编辑** 可对合同类型进行编辑。

单击**删除** 即可删除合同类型（如有合同选择该合同类型将会弹出弹窗引导修改其合同类型），**系统合同类型** 无法编辑和删除。

单击右上角**齿轮** 图标可以调整列表展示项和顺序（合同类型名称无法隐藏和调整顺序）。

## **文件夹（小程序端）**

###  顶部**查询合同文件**

**搜索** ：通过上方搜索框输入合同名称/签署方姓名等（签署方企业、发起方企业和姓名）搜索。支持通过历史记录再次搜索，历史记录保留最近的7条。

﻿

﻿

﻿  

**筛选** ：

**顶部小箭头** ：单击全部旁的小箭头，可以快速筛出**与我有关的合同** ：我参与的、我的待办、提醒我关注的。

﻿

﻿

﻿  

**右上角三个点** ：单击可对合同进行**排序** （以创建时间或过期时间为准）或**清理失效合同** （详情请下滑见第3部分）。

﻿

﻿

﻿  

**右上角筛选** ：单击筛选图标，可在浮层中通过筛选范围、状态、类型、创建时间范围、签署完成时间范围对合同进行**筛选** 。

﻿

﻿

﻿  

### **合同卡片按钮**

页面主体由**合同卡片** 排列形成，合同卡片上方有相应**按钮** ，不同状态对应的**不同按钮功能** 如下：

﻿

﻿

﻿  

﻿

**去签署**

：待我签署的合同。

单击**去签署** 或**合同卡片** 进入合同详情封面。单击下方**查看合同并签署** 进入**合同详情** 页查看**合同内容** ，可以**签署合同** 和 更多操作。详情请参见 [合同签署](/document/92775)。

﻿

﻿

﻿  

**去查看** ：未签署完成的非待我签署的合同。

单击**去查看** 或者**合同卡片** 进入合同详情封面，单击

**发送给签署人**

将发送合同卡片给微信联系人；单击**查看合同** 进入合同详情页**查看合同内容** ，可以

**撤销**

（若为我发起的合同）合同和 更多操作。

﻿

﻿

﻿  

﻿

**下载合同**

：签署完成的合同。单击下载合同即可将 PDF 合同**发送至微信或邮箱。**

单击**合同卡片** 进入合同详情封面，单击下方**查看合同** 进入合同详情页查看**合同内容** ，可以**下载合同** 和 更多操作。

﻿

﻿

﻿  

**查看失效理由** ：单击可查看该合同失效的理由。

﻿

**更多操作**

：

**合同详情页** 底部会出现**更多操作** 按钮，按钮中会根据不同的合同状态、操作人的身份，展示**不同功能** （图中为**不完全展示** ）：

﻿

﻿

﻿  

**撤销** ：**已发起** 的合同，在签署**双方未完成签署** 的情况下，企业方可撤销。

﻿

**转他人处理**

：对于**待我处理** 的合同，可以根据实际情况选择转给企业其他人员处。若已被经办人补充填写了内容，将不再支持转他人处理。

﻿

**拒签**

：对于**待我处理** 的合同，可以根据实际情况，选择拒绝签署。拒签需要输入不少于4字的拒签理由。

**查看审批详情** ：查看合同签署审批的详情。

﻿

**再写一份**

：以自己曾经发起的合同为模板直接生成一份合同，上一次自己填写的内容将会自动填充。

﻿

**申请签署报告**

：如您遇到法律纠纷，需要腾讯电子签官方出具合同签署意愿证明，可以申请签署报告。具体操作如下：

单击**申请签署报告** ，阅读页面内容，了解签署报告的基本信息后，单击底部**申请签署报告** ，补充邮箱信息，即可完成申请。

﻿

﻿

﻿  

签署报告将在24小时内发送至您的**邮箱** ，并且将通过**短信** 通知您发送结果。

申请后可在该合同的合同卡片下**查看申请状态** 、**查收报告** 等。

﻿

**申请公证处核验报告**

：如您遇到法律纠纷，需要进一步证明文件真实性和证明力，可以申请公证处核验报告。具体操作如下：

单击**申请公证处核验报告** ，出现区块链存证核验报告样例预览供借鉴参考，阅读**《腾讯电子签公证处核验报告使用规则》** 后勾选**我已阅读并同意** ，再次单击**申请公证处核验报告** 即可申请。

﻿

﻿

﻿  

签署报告将在24小时内发送至您的**邮箱** ，并且将通过**短信** 通知您发送结果。

申请后可在该合同的合同卡片下查看申请状态、查收报告等。

﻿

**查看合同基本信息**

：除查阅信息外，还可以

设置**合同类型**

（单击小三角）、设置

**到期提醒日**

（单击小三角进行设置）信息。

﻿

﻿

**﻿  
**

### **清理失效合同**

右上角单击三个点选择**清理失效合同** ，在弹出的浮层中**筛选** 清理文件范围，右下角**确认** 后的括号里会显示该范围命中的**合同数** ，单击**确认** 即可清理选中范围内的合同。

﻿

﻿

﻿  

## 合同工具

在电子签主页上方导航栏选择产品，找到最右边列**合同工具** ，合同工具中有两个功能：**合同验签和合同对比。**

**﻿**

﻿

﻿  

### **合同验签**

可以检验合同签署完成后是否**被篡改** ，是否仍**有效** 。

**网页端** ：可以上传任意电子合同平台签署完成的 PDF 文件，校验其**是否被篡改** 。单击合同验签，上传或拖拽需校验的合同到区域中即可。

﻿

﻿

﻿  

**小程序端** ：在**腾讯电子签小程序** > **个人中心** ，可见**合同验签** 入口。根据提示在微信对话中选择合同 PDF 文件，系统即可给出校验结果。

﻿

﻿

﻿  

**验签结果有如下几种：**

**验签结果**| **描述**  
---|---  
文件未被篡改| 合同签署完成后未被修改过。  
文件已被篡改| 合同签署完成后已被修改。  
验签失败| 合同中没有电子签名或文件格式损坏。  
  
### **合同对比**

如果您需要对比 2 份合同的**内容差异** ，可以使用 [合同对比](https://qian.tencent.com/compare/) 工具。

1. 进入到页面后，您需要分别上传新版和原版合同的文件，当前仅支持 PDF 版本文件，上传完成后单击**开始对比** 。

﻿

﻿

﻿  

2. 系统将智能对比两份文件的内容差异，并将**差异点** 标注在合同文本上，现支持文本、表格的增加、修改和删除内容对比。系统将默认进入**单屏** 查看模式，您可以单击合同文本中的色块、页面中间的图标或右侧的卡片，以查看具体的差异点说明。

﻿

﻿

﻿  

3. 单击页面右上角的**双屏** ，您也可以切换至双屏模式查看合同对比结果。双屏模式中您可以直接查看新版和原版合同，并进行对照。

﻿

﻿

﻿  

4. 若您发现对比结果不准确，可通过红框中的弹窗进行**反馈** ，我们将记录您的反馈并持续优化功能。

﻿

﻿

﻿  

## 合同解除

主要应用场景为：基于一份已经签署的合同（签署流程）进行**解除** 操作。 模板解除协议由官方提供，并已通过法务审核，目前不支持自定义。目前仅支持**接口发起** 解除，详情请参见接口 [发起解除协议](https://qian.tencent.com/developers/companyApis/operateFlows/CreateReleaseFlow)。

﻿  

﻿