#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动运行脚本 - 用于测试抓取功能
"""

import subprocess
import sys

def auto_crawl():
    """自动运行爬虫"""
    try:
        # 运行爬虫，自动输入y确认
        process = subprocess.Popen(
            [sys.executable, 'main.py', '-p', '5', '-w', '2'], 
            stdin=subprocess.PIPE, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True
        )
        
        # 自动输入y确认
        output, _ = process.communicate(input='y\n')
        print(output)
        
    except Exception as e:
        print(f"运行错误: {e}")

if __name__ == "__main__":
    auto_crawl() 